{"private": true, "author": "津玮 <liujin<PERSON>@shimo.im>", "scripts": {"build-all": "yarn build-pc && yarn merge-dist saveTemp pc && yarn build-mobile && yarn merge-dist merge pc", "build-mobile": "dotenvx run -f .env.production -f .env.mobile -- sh -c 'umi build && yarn rename-entry'", "build-pc": "dotenvx run -f .env.production -f .env.pc -- sh -c 'umi build && yarn rename-entry'", "clean": "find . -name 'node_modules' -type d -prune && find . -name 'node_modules' -type d -prune -exec rm -rf '{}' +", "clear-cache": "rm -rf ./node_modules/.cache && rm -rf ./dist && rm -rf ./src/.umi && rm -rf ./src/.umi-production", "dev": "yarn dev-pc", "dev-mobile": "dotenvx run -f .env.development -f .env.mobile -- umi dev", "dev-pc": "dotenvx run -f .env.development -f .env.pc -- umi dev", "postinstall": "UMI_GIT=false umi setup && husky", "lint": "umi lint", "merge-dist": "tsx ./scripts/merge-dist.ts", "rename-entry": "tsx ./scripts/rename-entry.ts", "setup": "umi setup", "start": "npm run dev", "upload": "tsx ./scripts/upload.ts"}, "lint-staged": {"*.{js,jsx,ts,tsx,css,less}": ["prettier --write --ignore-unknown", "umi lint --fix", "git add"]}, "dependencies": {"@shimo/collaborators-client": "^2.1.2", "@shimo/core-collaboration": "1.5.0", "@shimo/editor-registry": "0.0.36", "@shimo/lo-dates": "^1.18.1", "@shimo/sdk2-api": "^0.0.19", "@shimo/text-counter": "^0.1.6", "@shimo/widgets": "^1.0.29", "antd-mobile": "^5.39.0", "classnames": "^2.5.1", "core-js": "^3.19.1", "debug": "^4.3.3", "expire-set": "^1.0.0", "lodash": "^4.17.21", "node-rsa": "^1.1.1", "overlayscrollbars": "^2.11.1", "overlayscrollbars-react": "^0.5.6", "rc-virtual-list": "^3.18.6", "react-virtualized": "^9.22.6", "react-window": "^1.8.11", "shimo-broadcast-channel": "^1.2.0", "shimo-js-sdk-shared": "^1.5.2", "shimo-startparams": "^2.1.0", "socket.io-client": "^2.4.0", "tiny-emitter": "^2.1.0", "umi": "^4.4.6"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@dotenvx/dotenvx": "^1.43.0", "@formatjs/intl-localematcher": "^0.6.1", "@shimo/commitlint-config": "^3.0.3", "@shimo/oss-utilities": "^1.1.1", "@types/core-js": "^2.5.5", "@types/debug": "^4.1.7", "@types/js-cookie": "^3", "@types/lodash": "^4.17.16", "@types/mime": "^2.0.0", "@types/node-forge": "^1", "@types/node-rsa": "^1", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@types/react-virtualized": "^9", "@types/react-window": "^1.8.8", "@types/socket.io-client": "^1.4.36", "@umijs/max": "^4.4.6", "antd": "^5.24.5", "axios": "0.27.2", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "js-cookie": "^3.0.5", "lint-staged": "^15.5.0", "mime": "^2.4.5", "node-forge": "^1.3.1", "prettier": "^3.5.3", "prettier-plugin-packagejson": "^2.4.3", "tsx": "3.12.2", "typescript": "^5.0.3", "zustand": "^5.0.3"}, "packageManager": "yarn@4.9.1"}