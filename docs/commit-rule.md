# 一、背景

为了方便后续 Bug 定位，代码维护，同时为了方便工具自动化生成 changelog，我们会对所有全站涉及的常考启用 GitLab 远程 pre-receive 和 CI 检查。

我们会陆续对全站产品涉及的所有仓库都添加查钩子，所有 Commit 必须遵循 [Angular commit-message-format](https://github.com/angular/angular/blob/main/CONTRIBUTING.md#-commit-message-format) 规范，不符合规范的 commit 会被拒绝。

# 二、简单介绍

## 整体格式

整个 Code Message 分成三部分组成，即 **Header**、**Body**、**Footer** 提交格式如下：

```plain
<header>
<BLANK LINE>

<body>
<BLANK LINE>

<footer>
```

- 每次提交可以包含页眉（Header）、正文（Body）和页脚（Footer），每次提交必须包含页眉（Header）

- 每次提交的 Header、Body、Footer 信息不能超过 100 个字符

## 【必要】Commit Message Header

Header 是大家平常提交中最经常接触的，比如我们执行如下指令时：

```plain
git commit -m "feat(config): 增加接口连接数参数"
```

其中 "**feat(config): 增加接口连接数参数**" 就是 Header，整个 Header 分成三个部分：

- **type**: 提交类型

> 可选：build|ci|docs|feat|fix|perf|refactor|test

- **scope**: 业务域或者组件名称

> 名称可自定义，多个名称可用 "," 分隔。如 (config)、(api)、(config,api) 均可

- **short summary**: Header 摘要

> 在概括含义的前提下尽量精简

```plain
<type>(<scope>): <short summary>
  │       │             │
  │       │             └─⫸ 中文描述的修改摘要，结尾不要加句号。比如 "修复了导出类型错误的问题"
  │       │
  │       └─⫸ Commit Scope: 可以理解成业务域或者业务组件
  │
  └─⫸ Commit Type: build|ci|docs|feat|fix|perf|refactor|test
```

- build: 影响构建系统或外部依赖的更改（示例范围：gulp, broccoli, npm）

- ci: 对持续集成配置文件和脚本的更改（示例：CircleCi, SauceLabs）

- docs: 仅文档的更改

- feat: 增加了新功能

> feat 必须要带 scope。比如 git commit -m "feat(xxx): yyyyyyy"

- fix: 修复了某些 bug

> fix 必须带 scope。比如 git commit -m "fix(xxx): yyyyyyy"

- perf: 提高性能的代码更改

- refactor: 既不修复错误也不添加功能的代码更改

- test: 添加缺失的测试用例或更正现有测试用例

- revert: 功能 commit 回滚

- chore: 非以上场景的杂务处理

**注：必须是半角**`：`**，同时**`：`**后面必须跟一个空格。**

## 【可选】Commit Message Body

针对本次 Commit 更详细的描述可以省略，非必要。

## 【可选】Commit Message Footer

页脚可以包含有关 breaking changes 和 deprecations，也是引用此提交关闭或关联的 Issues、PR 等链接，非必要。

# 三、Header 使用举例

## build [scope 可选]

### 不指定 scope

```plain
git commit -m "build: 调整了构建脚本环境变量"
```

### 指定 scope

```plain
git commit -m "build(docker): 调整了 Dockerfile"
```

##

## ci [scope 可选]

### 不指定 scope

```plain
git commit -m "ci: 调整了 gitlab ci 单元测试脚本, 支持了 debug 模式"
```

### 指定 scope

```plain
git commit -m "ci(jenkins): 调整了 jenkins pipeline"
```

##

## docs [scope 可选]

### 不指定 scope

```plain
git commit -m "docs: 补充了关于鉴权流程相关的内容"
```

### 指定 scope

```plain
git commit -m "docs(api): 补充了关于 API 相关的文档"
```

##

## feat [scope 必填]

```plain
git commit -m "feat(config): 支持动态获取配置"
```

```plain
git commit -m "feat(api): 增加了新的登录接口"
```

## fix [scope 必填]

```plain
git commit -m "fix(http): 修复了 HTTP 组件鉴权使用了错误 Header 的问题"
```

##

```plain
git commit -m "fix(config): 修复了初始化配置加载失败的问题"
```

## perf [scope 可选]

### 指定了 scope

```plain
git commit -m "perf(http): 优化了 /users 接口性能，增加了 Redis 缓存"
```

### 不指定 scope

```plain
git commit -m "perf: 优化了内部处理变更导致的内存占用的问题"
```

##

## refactor [scope 可选]

### 指定了 scope

```plain
git commit -m "refactor(http): 调整 HTTP Server，将 echo 替换成 gin"
```

### 不指定 scope

```plain
git commit -m "refactor: 内部包路径进行规整，将部分不合理的包命名进行了规范"
```

##

## test [scope 可选]

### 指定了 scope

```plain
git commit -m "test(http): 增加了 /users 接口的测试用例"
```

### 不指定 scope

```plain
git commit -m "test: 增加了 getCurrentTime 方法的测试用例"
```

#

# 四、GitLab 仓库 CI

## 4.1. 仓库合并策略

**强烈建议所有仓库全部开启 Fast Forward 合并模式，配置方式如下：**

![gitlab-fast-forward](./assets/gitlab-fast-forward.png)

后续我们会定义一个 CI template，所有需要交付到客户环境的代码仓库都会强制配置上 CI 检查。

Doing 郑伟

## 4.2 仓库远程 pre-receive 钩子

我们会对指定仓库配置服务端 receive 钩子，不符合要求的 commit 直接会被拒绝，当前已配置服务端钩子的仓库清单如下：

[https://shimo.im/sheets/e1Azd7JJ4KsWOVqW/MODOC](https://shimo.im/sheets/e1Azd7JJ4KsWOVqW/MODOC)

如需增加清单，可以提供仓库地址 郑伟处理。

# 五、可能遇见的问题和处理方法

1. 我的提交被服务端拒绝怎么办？

- 【方法一】使用 `git reset HEAD~1`，取消上次提交，重新提交 commit

- 【方法二】 或 `git commit--amend`，直接修改上次提交的 commit message 信息。

1. 我在本地 merge 了其他的功能分支，带上去了不属于我的历史 commit，也被拒绝了怎么办？

- 【方法一】使用 git rebase 进行变基 刘腾

[https://shimo.im/docs/KlkKvnXyWQi08zqd](https://shimo.im/docs/KlkKvnXyWQi08zqd)

- 【方法二】使用 git cherry pick 挑选必要的 commit 刘腾

## [https://shimo.im/docs/vVAXM2RjL7uYma3m](https://shimo.im/docs/vVAXM2RjL7uYma3m)

1. 我基于 base 分支创建了一个新分支，推送到被服务端拒绝？

如果你是按照如下方式来操作：

```plain
# 列举当前分支
git branch
$ release # 假设当前 base 分支是 release

# 检出新分支
git checkout -b feat/my-feat

# 不提交任何 commit 直接尝试推送到远端
git push -u origin feat/my-feat
```

那么远端 pre-receive 钩子会对所有 release 历史 commit 做检查，所以会被服务端拒绝，解决的方案就是不要提交没有任何新 commit 的 feat 分支。

```plain
# 列举当前分支
git branch
$ release # 假设当前 base 分支是 release

# 检出新分支
git checkout -b feat/my-feat

# 修改代码，假设你想修改 readme.md
vim readme.md
git commit -m "docs(all): 修改说明文档"

# 不提交任何 commit 直接尝试推送到远端
git push -u origin feat/my-feat
```

这样就可以操作成功。

# 六、CommitLint 工具

[CommitLint](https://commitlint.js.org/) 是基于 Node.js 的工具，它通过一系列的规则来检查 Git 提交信息的格式，每次本地提交时 CommitLint 都会自动检查提交信息。如果提交信息不符合规则，CommitLint 会阻止提交并给出错误提示。

JS项目可以直接引入 `@shimo/commitlint-config` 作为 commitlint 的规则来使用，配置成功后本地提交将会按照石墨的规范进行检测

**使用方法**：

```bash
# 安装依赖
npm install --save-dev  @commitlint/cli @shimo/commitlint-config
# 在项目根目录进行配置
echo "export default { extends: ['@shimo/commitlint-config
'] };" > commitlint.config.js
```

**注意：**

- CommitLint 只负责规则检测部分，要使 git 提交时能够正常运行，项目中需要配置 commit-msg 的钩子（[参考文档](https://commitlint.js.org/guides/local-setup.html)）

- `@shimo/commitlint-config` 请使用 3.x 或更高版本。npm 包会不定期更新，同步 gitlab 上的检测逻辑 （发现和 gitlab 规则冲突时可以找 李 榜 进行更新）

# SmartCommit

该功能可以利用在团队提交代码的 commit message 中触发 jira issue 的一系列动作，只需要输入 Jira Issue Key 和期望动作的关键字在 commit message 中，比如：评论、工时、流转状态、分配等动作

官方语法规则：

```sql
<ISSUE_KEY> <ignored text> #<command> <optional command_params>
```

基于石墨 git 提交规范使用 SmartCommit

```sql
<type>(<scope>): <ISSUE_KEY> <short summary> #<command> <optional command_params>
```

将 ISSUE_KEY 放入到后面的 message 中就能关联上了，我们平常使用最多的就是评论、工时、流转动作（频率也会很低，目前大多数是回到 JIRA 界面上填写必填字段）

```sql
fix(user): TECHNOLOGY-43 修复空指针 #comment 错误未检测 #time 1h 修复bug
```

| comment | 插入 Issue 评论中 |
| :------ | :---------------- |
| time    | 记录工时          |

# 七、参考

- [Angular commit-message-format](https://github.com/angular/angular/blob/main/CONTRIBUTING.md#-commit-message-format)

- [Angular 提交信息规范](https://zj-git-guide.readthedocs.io/zh-cn/latest/message/Angular%E6%8F%90%E4%BA%A4%E4%BF%A1%E6%81%AF%E8%A7%84%E8%8C%83/)
