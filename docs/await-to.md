# await-to 工具函数

## 简介

try catch 的替代方法 一般用于简化请求的错误处理

## 方法来源

参照 [await-to-js](https://www.npmjs.com/package/await-to-js)

## 参数

- `promise` - 要执行的 Promise
- `errorExt` - 传递给err对象的附加信息

## 返回值

返回包含错误和数据的元组

## 使用示例

### 只关心成功结果，忽略错误处理

```javascript
import { to } from '@/api/Request';

async function aa() {
  const [, res] = await to(xxApi.foo(''));
  if (res?.status === xxx) {
    // 业务代码 成功后处理
  }
}
```

### 完整的错误处理示例

```javascript
async function aa() {
  const [err, res] = await to(xxApi.foo(''));
  if (res?.status !== xxx) return message.error(err?.data?.msg);
  // 业务代码 成功后处理
}
```
