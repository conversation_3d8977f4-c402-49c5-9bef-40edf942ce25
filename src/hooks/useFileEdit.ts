import { message } from 'antd';
import { debounce } from 'lodash';
import { useMemo } from 'react';
import { history } from 'umi';

import * as fileApi from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { isSupportedImportExt } from '@/constants/fileTypes';
import { fm } from '@/modules/Locale';
import type { FileDetail } from '@/types/api';
import { openFile } from '@/utils/file';

const initGetFile = async (fileGuid: string) => {
  const [err, res] = await catchApiResult(fileApi.fileDetail(fileGuid));
  if (err) {
    history.push(`/error?code=${err.code}`);
    return false;
  }
  return res?.data;
};

export const useFileEdit = (fileDetail: FileDetail | null, paramsGuid: string | undefined) => {
  const i18n_fileImportTypeErr = fm('File.importTypeErr');
  const i18n_fileImportErr = fm('File.importErr');
  const i18n_fileIsLoading = fm('File.isLoading');

  const debouncedHandleEdit = useMemo(
    () =>
      debounce(
        async () => {
          if (!fileDetail) return;

          if (!isSupportedImportExt(fileDetail.type)) {
            message.open({
              type: 'error',
              content: i18n_fileImportTypeErr,
            });
            return;
          }

          const [importErr, importRes] = await catchApiResult(fileApi.importFile(fileDetail.guid, fileDetail.type));
          if (importErr) {
            message.open({
              type: 'error',
              content: i18n_fileImportErr,
            });
            return;
          }

          const taskId = importRes?.data.data.taskId;

          message.open({
            type: 'loading',
            content: i18n_fileIsLoading,
            duration: 0,
            key: 'import',
          });

          const pollImportProgress = (taskId: string): Promise<void> => {
            return new Promise((resolve, reject) => {
              const interval = 50;

              const check = async () => {
                const [progressErr, progressRes] = await catchApiResult(fileApi.importFileProgress(taskId));
                if (progressErr) {
                  reject(progressErr);
                  return;
                }

                if (progressRes?.data.data.progress >= 100) {
                  resolve();
                } else {
                  setTimeout(check, interval);
                }
              };

              check();
            });
          };

          try {
            await pollImportProgress(taskId || '');
            message.destroy('import');

            const _fileDetail = await initGetFile(paramsGuid || '');
            if (!_fileDetail) return;

            fileApi.userAction(_fileDetail.guid, { isPreview: 1 });
            window.location.href = _fileDetail.url;
          } catch (err) {
            message.destroy('import');
            message.open({
              type: 'error',
              content: i18n_fileImportErr,
            });
          }
        },
        1000,
        { leading: true, trailing: false },
      ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [fileDetail?.guid, fileDetail?.type],
  );

  return { debouncedHandleEdit, initGetFile };
};

// pure fn
export const createDebouncedEditHandler = ({
  fileDetail,
  fileGuid,
  i18n,
}: {
  fileDetail: FileDetail;
  fileGuid: string;
  i18n: {
    fileImportTypeErr: string;
    fileImportErr: string;
    fileIsLoading: string;
  };
}) => {
  return debounce(
    async () => {
      if (!isSupportedImportExt(fileDetail.type)) {
        openFile({ type: fileDetail.type, guid: fileDetail.guid, url: fileDetail.url, model: 'new' });
        return;
      }

      const [importErr, importRes] = await catchApiResult(fileApi.importFile(fileDetail.guid, fileDetail.type));
      if (importErr) {
        message.open({
          type: 'error',
          content: i18n.fileImportErr,
        });
        return;
      }

      const taskId = importRes?.data.data.taskId;

      message.open({
        type: 'loading',
        content: i18n.fileIsLoading,
        duration: 0,
        key: 'import',
      });

      const pollImportProgress = (taskId: string): Promise<void> => {
        return new Promise((resolve, reject) => {
          const interval = 50;

          const check = async () => {
            const [progressErr, progressRes] = await catchApiResult(fileApi.importFileProgress(taskId));
            if (progressErr) {
              reject(progressErr);
              return;
            }

            if (progressRes?.data.data.progress >= 100) {
              resolve();
            } else {
              setTimeout(check, interval);
            }
          };

          check();
        });
      };

      try {
        await pollImportProgress(taskId || '');
        message.destroy('import');

        const _fileDetail = await initGetFile(fileGuid);
        if (!_fileDetail) return;

        fileApi.userAction(_fileDetail.guid, { isPreview: 1 });
        window.location.href = _fileDetail.url;
      } catch (err) {
        message.destroy('import');
        message.open({
          type: 'error',
          content: i18n.fileImportErr,
        });
      }
    },
    1000,
    { leading: true, trailing: false },
  );
};
