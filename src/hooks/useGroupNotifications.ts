import { useFormatTime } from '@/utils/file';

export const useGroupNotifications = () => {
  const { formatTime } = useFormatTime();

  /**
   * 按日期对通知进行分组
   * @param items - 需要分组的项目列表
   * @param getDate - 获取日期的函数
   * @returns 按日期分组后的列表，按日期倒序排列
   */
  function groupNotifications<T>(
    items: T[],
    getDate: (item: T) => string,
  ): {
    createdAt: string;
    children: T[];
  }[] {
    // 1. 创建临时分组字典
    const dateMap: Record<string, T[]> = {};

    // 2. 遍历处理每个项目
    items.forEach((item) => {
      try {
        const date = new Date(getDate(item));
        if (isNaN(date.getTime())) return;

        // 生成标准化日期键
        const dateKey = date.toISOString().split('T')[0];

        if (!dateMap[dateKey]) {
          dateMap[dateKey] = [];
        }

        dateMap[dateKey].push(item);
      } catch (error) {
        // console.error(`处理项目时出错:`, error);
      }
    });

    // 3. 转换为目标结构并排序
    return Object.entries(dateMap)
      .map(([dateKey, items]) => ({
        createdAt: dateKey,
        children: items.sort((a, b) => new Date(getDate(b)).getTime() - new Date(getDate(a)).getTime()),
      }))
      .sort((a, b) => b.createdAt.localeCompare(a.createdAt)); // 日期倒序
  }

  return {
    groupNotifications,
    formatTime,
  };
};
