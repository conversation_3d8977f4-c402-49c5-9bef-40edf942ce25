import { message } from 'antd';
import { useCallback } from 'react';

import { getSecurityPassword } from '@/modules/encrypt';
import { useFormatMessage } from '@/modules/Locale';
import { useGetMe, useLogin } from '@/service/Me';
import { useMeStore } from '@/store/Me';

let hasTriedGetMe = false;

/**
 * 检查是否已经初始化用户信息，如果没有就要初始化用户信息
 * 初始化之后，要么是登录用户，要么是匿名用户
 * 服务器报错时只尝试一次
 */
export function useAnonymousEffects() {
  const getMe = useGetMe();

  const me = useMeStore((state) => state.me);
  if (me.id === null && !hasTriedGetMe) {
    // 尚未尝试登录流程，需要尝试获取用户登录状态
    hasTriedGetMe = true;
    throw getMe();
  }
}

export function useLoginGuard(ignoreRedirect = false) {
  const me = useMeStore((state) => state.me);
  if (me.teamRole === 'disabled') {
    location.href = '/forbidden';
  }
  if (typeof me.id === 'number' && me.id < 0 && !ignoreRedirect) {
    location.href = `/login?redirect_url=${decodeURIComponent(location.href)}`;
  }
}

export function useHasLogin() {
  return useMeStore((state) => (state.me?.id ?? -1) > 0);
}

export function useLoginByPassword() {
  const login = useLogin();
  const loginFailedMessage = useFormatMessage('hooks.Authorization.loginFailedMessage');

  return useCallback(async (data: { username: string; password: string }): Promise<boolean> => {
    const encryptedPassword = getSecurityPassword(data.password);
    try {
      await login(data.username, encryptedPassword);
      return true;
    } catch (e: unknown) {
      message.error((e as any)?.data?.msg ?? loginFailedMessage);
      // 登录失败，要显示提示信息
      return false;
    }
  }, []);
}
