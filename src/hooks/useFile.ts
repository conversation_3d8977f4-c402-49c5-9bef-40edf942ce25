import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'umi';
import { history } from 'umi';

import { fileDetail, files } from '@/api/File';
import type { FileDataModel } from '@/pages/pc/Desktop/model';
import { useMeStore } from '@/store/Me';

import { useFileSort } from './useFileSort';

export const useFile = () => {
  const id = useMeStore((state) => state.me.id);
  const [fileList, setFileList] = useState<FileDataModel[]>([]);
  const [detail, setDetail] = useState<Partial<FileDataModel> | null>();
  const { guid } = useParams();
  const [loading, setLoading] = useState(true);
  const sortData = useFileSort({
    id,
    fileList,
    setFileList,
  });
  const getFileList = useCallback(() => {
    setLoading(true);
    files({ folder: guid === 'index' ? undefined : guid })
      .then((res) => {
        if (res.status === 200) {
          const { list = [] } = res.data;
          setFileList(
            list?.map((item: FileDataModel) => ({
              updatedUser: item.updatedUser,
              guid: item.guid,
              updatedAt: item.updatedAt,
              isFolder: item.isFolder || false,
              createdAt: item.createdAt,
              name: item.name,
              type: item.type,
              subType: item.subType,
            })) || [],
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  }, [guid]);
  const clickItem = useCallback((data: FileDataModel) => {
    if (data.type === 'folder') {
      history.push(`/mobile/folder/${data.guid}`);
    }
  }, []);
  const getFileDetail = useCallback(() => {
    if (guid) {
      fileDetail(guid).then((res) => {
        if (res.status === 200) {
          setDetail({
            name: res.data.name,
          });
        }
      });
    } else {
      setDetail(null);
    }
  }, [guid]);

  const back = useCallback(() => {
    history.back();
  }, []);
  const fileHeaderProp = useMemo(
    () => ({
      back,
      data: detail,
      guid,
    }),
    [back, guid, detail],
  );
  useEffect(() => {
    getFileList();
    getFileDetail();
  }, [getFileList, getFileDetail]);
  return { fileList, fileHeaderProp, clickItem, ...sortData, detail, setFileList, loading };
};
