import { useCallback, useEffect, useMemo } from 'react';

import type { SortModel } from '@/model/FileList';
import { ModeTypeEnum } from '@/model/FileList';
import { fm } from '@/modules/Locale';
import type { FileDataModel } from '@/pages/pc/Desktop/model';

import { useLocalStorage } from './UseStorage';
type Props = {
  id: number | null;
  fileList: FileDataModel[];
  setFileList: (callback: (fileList: FileDataModel[]) => FileDataModel[]) => void;
};
export const useFileSort = ({ id, fileList, setFileList }: Props) => {
  const fileKey = `${id}-FOLDER`;
  const [storedValue, setValue] = useLocalStorage<SortModel>(fileKey, {
    name: fm('File.defaultSort'),
    isFolderPinned: true,
    isReversed: true,
    layout: ModeTypeEnum.list,
    selectedType: 'default',
  });
  const changeMode = useCallback(() => {
    setValue({
      ...storedValue,
      layout: storedValue.layout === ModeTypeEnum.list ? ModeTypeEnum.card : ModeTypeEnum.list,
    });
  }, [storedValue, setValue]);

  useEffect(() => {
    if (fileList.length) {
      setFileList((fileList: FileDataModel[]) => {
        // 主排序逻辑
        const compareFn = (a: FileDataModel, b: FileDataModel) => {
          switch (storedValue.selectedType) {
            case 'name':
              return a.name.localeCompare(b.name);
            case 'createTime':
              return a.createdAt - b.createdAt;
            case 'updateTime':
            default:
              return a.updatedAt - b.updatedAt;
          }
        };

        // 应用排序方向
        const direction = storedValue.isReversed ? -1 : 1;
        // 分别排序
        if (storedValue.isFolderPinned) {
          const folders = fileList.filter((item) => item.isFolder);
          const nonFolders = fileList.filter((item) => !item.isFolder);
          nonFolders.sort((a, b) => direction * compareFn(a, b));
          folders.sort((a, b) => direction * compareFn(a, b));
          return [...folders, ...nonFolders];
        } else {
          const sorted = [...fileList];
          sorted.sort((a, b) => direction * compareFn(a, b));
          return sorted;
        }
      });
    }
  }, [setFileList, storedValue, fileList.length]);

  const fileSortProp = useMemo(
    () => ({
      changeMode,
      setSortData: setValue,
      sortData: storedValue,
    }),
    [changeMode, setValue, storedValue],
  );
  return {
    fileSortProp,
    changeMode,
  };
};
