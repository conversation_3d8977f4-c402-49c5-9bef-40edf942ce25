import { useEffect, useState } from 'react';

function useLocalStorage<T>(key: string, defaultValue: T): [T, (value: T) => void, (value: T) => void] {
  const initialValue = localStorage.getItem(key) as string | null;
  const initialValueParsed = initialValue ? JSON.parse(initialValue) : defaultValue;

  const [storedValue, setStoredValue] = useState<T>(initialValueParsed);

  // 处理localStorage的更新
  useEffect(() => {
    localStorage.setItem(key, JSON.stringify(storedValue));
  }, [key, storedValue]);

  const setValue = (value: T) => {
    setStoredValue(value);
  };

  const removeValue = () => {
    localStorage.removeItem(key);
    setStoredValue(defaultValue);
  };

  return [storedValue, setValue, removeValue];
}

export { useLocalStorage };
