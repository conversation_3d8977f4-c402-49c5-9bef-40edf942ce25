import { message, Modal } from 'antd';
import { useEffect, useState } from 'react';
import { flushSync } from 'react-dom';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import { useFormatMessage as $t } from '@/modules/Locale';
import type { StatusType, UploadItem } from '@/store/Upload';
import { useUploadStore } from '@/store/Upload';
import { getGenerateTempId } from '@/utils/file';
// 文件上传结果类型
export type FileUploadResult = {
  file: File;
  status: StatusType;
  message?: string;
  done?: boolean;
  guid?: string;
  generateTempId: string | number;
};

export type FileStatus = {
  item: UploadItem | null;
  progress: number;
};

type UploadOptions = {
  // 上传成功回调
  onSuccess?: (files: FileUploadResult[]) => void;
  // 是否允许选择多个文件
  multiple?: boolean;
  // 允许的文件类型
  accept?: string;
  // 父级目录GUID
  parentGuid?: string;
};

type UploadResult = {
  // 触发文件选择框
  triggerUpload: () => void;
  uploadFile: (fileTemp: UploadItem) => Promise<FileUploadResult>;
};

/**
 * 文件上传 Hook
 * @param options - 上传配置项
 * @returns UploadResult
 */
export default function useFileUpload(options: UploadOptions = {}): UploadResult {
  const i18nText = {
    networkError: $t('UseFileUpload.networkError'),
    noSpaceTitle: $t('UseFileUpload.noSpaceTitle'),
    noSpaceContent: $t('UseFileUpload.noSpaceContent'),
    noSpaceOkText: $t('UseFileUpload.noSpaceOkText'),
  };
  const { multiple = true, accept = '', parentGuid = 'Desktop' } = options;
  const [singleResult, setSingleResult] = useState<FileUploadResult | null>(null);
  const [fileStatus, setFileStatus] = useState<FileStatus>({
    progress: 0,
    item: null,
  });
  const { uploadList, setUploadList, setIsShowBoard } = useUploadStore((state) => state);

  useEffect(() => {
    if (!uploadList.length) return;
    const arr = uploadList.map((item) => {
      if (item.generateTempId === fileStatus.item?.generateTempId) {
        return { ...item, progress: fileStatus.progress };
      }
      return { ...item };
    });
    //获取进度信息 更新list
    setUploadList([...arr]);
  }, [fileStatus]);
  useEffect(() => {
    if (!uploadList.length) return;
    const arr = uploadList.map((item) => {
      if (item.generateTempId === singleResult?.generateTempId) {
        return { ...item, ...singleResult };
      } else {
        return item;
      }
    });
    //获取状态信息 更新list
    setUploadList([...arr]);
  }, [singleResult]);

  // 上传文件主函数
  async function uploadFile(item: UploadItem): Promise<FileUploadResult> {
    //变更状态 为上传
    const list = uploadList;
    const index = list.findIndex((it) => item.generateTempId === it.generateTempId);
    list[index] = { ...item, status: 'uploading' };
    setUploadList([...list]);

    // 第一步：获取上传策略
    const formData_1 = new FormData();
    formData_1.append('encodedFileName', item.name);
    formData_1.append('file', item.file);
    const headers = {
      'x-file-parent-guid': parentGuid,
      'x-file-size': String(item.size),
      'x-file-type': item.type,
      'x-requested-with': 'XMLHttpRequest',
    };

    const [error1, postPolicyRes] = await to(fileApi.uploadPostPolicy(formData_1, headers, item.controller));
    if (error1 || postPolicyRes?.status !== 200) {
      return {
        generateTempId: item.generateTempId,
        file: item.file,
        status: error1?.message === 'canceled' ? 'cancel' : 'fail',
        done: true,
        message: error1?.data?.msg || postPolicyRes?.data?.message || i18nText.networkError,
      };
    }
    const { url, formData: formDataParams } = postPolicyRes.data.uploadUrl;

    // 第二步：上传文件到Minio
    const formData = new FormData();
    Object.entries(formDataParams || {}).forEach(([key, value]) => {
      formData.append(key, value as string);
    });
    formData.append('file', item.file);
    const [error2, uploadRes] = await to(
      fileApi.uploadMinio(url, formData, item.controller, (progress) => {
        setFileStatus({ progress, item });
      }),
    );
    if (error2 || uploadRes?.status !== 200) {
      return {
        generateTempId: item.generateTempId,
        file: item.file,
        status: error2?.message === 'canceled' ? 'cancel' : 'fail',
        done: true,
        message: error2?.data?.msg || uploadRes?.data?.message || i18nText.networkError,
      };
    }

    // 第三步：上传回调
    const callbackData = {
      bucket: formDataParams.bucket,
      fname: item.name,
      size: item.size,
      key: formDataParams.key,
      token: formDataParams['x:token'],
    };

    const [error3, callbackRes] = await to(fileApi.uploadCallback(callbackData, item.controller));
    if (error3 || callbackRes?.status !== 200) {
      return {
        generateTempId: item.generateTempId,
        file: item.file,
        status: error3?.message === 'canceled' ? 'cancel' : 'fail',
        done: true,
        message: error3?.data?.msg || callbackRes?.data?.message || i18nText.networkError,
      };
    }

    return {
      generateTempId: item.generateTempId,
      file: item.file,
      status: 'success',
      done: true,
      guid: callbackRes?.data?.guid || '',
    };
  }

  useEffect(() => {
    const list = uploadList;
    if (list.length) {
      const waitingFileList = list.filter((item) => item.status === 'waiting');
      if (waitingFileList.length) {
        const fetchData = async (item: UploadItem) => {
          try {
            const res = await uploadFile(item);
            const resObj = { ...singleResult, ...item, ...res };
            flushSync(() => {
              setSingleResult(resObj);
            });
            setTimeout(() => {}, 0);
          } catch (error) {}
        };
        const uploadingFileList = list.filter((item) => item.status === 'uploading');
        if (uploadingFileList.length <= 10) {
          //限制同时上传 10个文件
          fetchData(waitingFileList[0]);
        }
      }
    }
  }, [uploadList]);

  const handleFilesToUploadListFunc = async (resList: UploadItem[], files: FileList) => {
    for (let i = 0; i < files.length; i++) {
      resList.unshift({
        type: files[i].type,
        size: files[i].size,
        file: files[i],
        generateTempId: getGenerateTempId(),
        name: files[i].name,
        parentGuid: parentGuid,
        status: 'waiting',
        controller: new AbortController(),
      });
    }
    setUploadList([...resList]);
  };

  const showNoSpace = () => {
    Modal.info({
      icon: null,
      title: i18nText.noSpaceTitle,
      content: i18nText.noSpaceContent,
      okText: i18nText.noSpaceOkText,
    });
  };

  const ischeckSpaceSizeOk = async (arr: File[] | { size: number }[]) => {
    let totalSize = 0;
    arr.forEach((it: { size: number }) => {
      totalSize += Number(it.size || 0);
    });
    const [error, callbackRes] = await to(fileApi.getFileQuota());
    if (error) {
      message.error(i18nText.networkError);
      return { noNetwork: true };
    }
    const remaining = callbackRes?.data?.personalDiskVolume?.remaining || 0;
    return { noNetwork: false, onNeedSpaceSize: Number(remaining) > totalSize };
  };
  // 触发文件选择
  function triggerUpload() {
    // 创建文件选择器
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.multiple = multiple;
    fileInput.accept = accept;
    fileInput.style.display = 'none';

    const handleFileChange = async (event: any) => {
      const files = (event.target as HTMLInputElement).files;
      if (!files || files.length === 0) return;
      if (files && files.length > 80) {
        message.warning('一次最多上传80个文件');
        fileInput.removeEventListener('change', handleFileChange);
        return;
      }

      if (files) {
        const resList = uploadList;
        //空间问题
        const { noNetwork, onNeedSpaceSize } = await ischeckSpaceSizeOk(Array.from(files));
        if (noNetwork) return;
        if (!onNeedSpaceSize) {
          showNoSpace();
          return;
        }

        setIsShowBoard(true);
        handleFilesToUploadListFunc(resList, files);
      }
    };

    // 监听文件选择事件
    fileInput.addEventListener('change', handleFileChange);

    // 触发文件选择器点击
    document.body.appendChild(fileInput);
    fileInput.click();

    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(fileInput);
    }, 100);
  }

  return {
    triggerUpload,
    uploadFile,
  };
}
