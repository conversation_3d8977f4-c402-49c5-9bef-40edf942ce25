import { useEffect, useRef, useState } from 'react';

export const useVisibleHeightObserver = () => {
  const ref = useRef(null);
  const [visibleHeight, setVisibleHeight] = useState(0);

  useEffect(() => {
    if (!ref.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setVisibleHeight(entry.intersectionRect.height);
          } else {
            setVisibleHeight(0);
          }
        });
      },
      { threshold: 0.01 },
    );

    observer.observe(ref.current);
    const currentElement = ref.current;
    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, []);

  return { ref, visibleHeight };
};
