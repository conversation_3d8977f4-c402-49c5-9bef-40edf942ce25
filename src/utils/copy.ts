/**
 * 老复制方法 兼容性较好，可以自动触发也可以在事件触发时使用
 *
 * 复制内容，兼容有HTML标签的文本
 */
export function oldCopy(content: string) {
  const temDIV = document.createElement('div');
  temDIV.innerHTML = content;
  document.body.appendChild(temDIV);

  // 创建选区
  const range = document.createRange();
  window.getSelection()?.removeAllRanges();

  // 选中
  range.selectNode(temDIV);
  window.getSelection()?.addRange(range);

  // 执行
  document.execCommand('copy');

  // 收尾
  window.getSelection()?.removeAllRanges();
  document.body.removeChild(temDIV);
}

/**
 * 复制简单文本不建议用
 *
 * 新复制方法 注意新方法可能需要事件触发 自动触发会有问题
 *
 * 注意这里按照规范来说 write 为高级命令 有诸多权限和协议问题
 *
 * 复制内容，兼容有HTML标签的文本
 *
 * @param content 为HTML字符串 也就是innerHTML的内容
 */
export function newCopy(content: string) {
  const textHtmlBlob = new Blob([content], { type: 'text/html' });
  const options = {
    'text/html': textHtmlBlob,
  };
  // eslint-disable-next-line
  const newBlobData = new window['ClipboardItem'](options);
  // eslint-disable-next-line
  navigator.clipboard['write']([newBlobData])
    .then(() => {
      console.log('Text copied to clipboard');
    })
    .catch(() => {
      // 如果出错使用老方法
      oldCopy(content);
    });
}
