/**
 * 用于格式化get请求的参数
 * @param data
 * @returns
 */
export function toQueryString(data: { [key: string]: any }): string {
  const params = new URLSearchParams();
  for (const [key, value] of Object.entries(data)) {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  }
  return params.toString();
}
export const ContentType = 'content-type';

export const fetchGet = ({
  url,
  headers = {},
}: {
  url: string;
  headers?: {
    [key: string]: string;
  };
}) => {
  if (!headers[ContentType]) {
    headers[ContentType] = 'application/json;charset=UTF-8';
  }

  return fetch(url, {
    method: 'GET',
    headers: {
      accept: 'application/nd.shimo.v2+json',
      'x-requested-with': 'XMLHttpRequest',
      ...headers,
    },
  });
};

export const fetchPost = ({
  url,
  params,
  method = 'POST',
  headers = {},
  responseType,
}: {
  url: string;
  method?: 'POST' | 'PUT';
  params?: any;
  headers?: {
    [key: string]: string;
  };
  responseType?: string;
}) => {
  let body = {};
  let curHeaders = {
    ...headers,
  };

  if (!curHeaders[ContentType]) {
    curHeaders = {
      ...headers,
      [ContentType]: 'application/json;charset=UTF-8',
    };
  }

  if (curHeaders[ContentType] === 'multipart/form-data') {
    body = {
      body: params, // 此时 params 应该是 form-data 类型
    };
    // 这里之所以删除是因为浏览器会根据 body form-data 类型 给 content-type 加boundary
    // 直接指定 multipart/form-data 后，浏览器就不干预了
    delete curHeaders[ContentType];
  } else if (params) {
    body = { body: JSON.stringify(params) };
  }

  if (responseType) {
    body = {
      ...body,
      responseType,
    };
  }

  return fetch(url, {
    method: method,
    headers: {
      accept: 'application/nd.shimo.v2+json',
      'x-requested-with': 'XMLHttpRequest',
      ...curHeaders,
    },
    ...body,
  });
};

export const fetchPut = (putData: {
  url: string;
  params?: any;
  headers?: {
    [key: string]: string;
  };
  responseType?: string;
}) => {
  return fetchPost({ ...putData, method: 'PUT' });
};

export const fetchPatch = ({
  url,
  params,
  headers = {},
}: {
  url: string;
  params: any;
  headers?: {
    [key: string]: string;
  };
}) => {
  let body = { body: params };
  if (params && !headers[ContentType]) {
    headers[ContentType] = 'application/json;charset=UTF-8';
    body = { body: JSON.stringify(params) };
  }

  return fetch(url, {
    method: 'PATCH',
    headers: {
      accept: 'application/nd.shimo.v2+json',
      'x-requested-with': 'XMLHttpRequest',
      ...headers,
    },
    ...body,
  });
};

export const fetchDelete = ({
  url,
  params,
  headers = {},
}: {
  url: string;
  params?: any;
  headers?: {
    [key: string]: string;
  };
}) => {
  let body = { body: params };

  if (!headers[ContentType] && params) {
    body = { body: JSON.stringify(params) };
    headers[ContentType] = 'application/json;charset=UTF-8';
  }
  return fetch(url, {
    method: 'DELETE',
    headers: {
      accept: 'application/nd.shimo.v2+json',
      'x-requested-with': 'XMLHttpRequest',
      ...headers,
    },
    ...body,
  });
};
