// 自定义事件 监听器管理工具
import type { CustomEventName } from '@/model/CustomEvent';

// 存储事件队列
const eventQueueMap: Map<string, any[]> = new Map();
// 存储监听器计数
const listenerCountMap: Map<string, number> = new Map();

// 添加自定义事件监听器
export function onCustomEvent<T>(eventName: string, callback: (eventDetail: T) => void) {
  function eventHandler(event: Event) {
    const customEvent = event as CustomEvent;
    callback(customEvent.detail);
  }

  window.addEventListener(eventName, eventHandler);
  // 增加监听器计数
  listenerCountMap.set(eventName, (listenerCountMap.get(eventName) || 0) + 1);

  // 检查是否有未处理的事件
  const queuedEvents = eventQueueMap.get(eventName);

  if (queuedEvents?.length) {
    queuedEvents.forEach((detail) => {
      callback(detail);
    });
    eventQueueMap.delete(eventName);
  }

  return () => {
    window.removeEventListener(eventName, eventHandler);
    // 减少监听器计数
    const count = listenerCountMap.get(eventName) || 0;
    if (count > 0) {
      listenerCountMap.set(eventName, count - 1);
    }
  };
}

// 触发自定义事件并通知所有监听器
export function emitCustomEvent<T>(eventName: CustomEventName, detail: T) {
  // 检查是否有监听器
  const hasListeners = (listenerCountMap.get(eventName) || 0) > 0;

  if (hasListeners) {
    // 如果有监听器，直接触发事件
    window.dispatchEvent(
      new CustomEvent(eventName, {
        detail,
      }),
    );
  } else {
    // 如果没有监听器，将事件存入队列
    const queuedEvents = eventQueueMap.get(eventName) || [];
    queuedEvents.push(detail);
    eventQueueMap.set(eventName, queuedEvents);
  }
}
