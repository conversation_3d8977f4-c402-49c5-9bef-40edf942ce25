import debug from 'debug';
import ExpireSet from 'expire-set';
import isPlainObject from 'is-plain-obj';
import type { ShimoMessageEvent } from 'shimo-broadcast-channel';
import { isShimoMessageEventLike, MessageTimeoutError, ShimoBroadcastChannel } from 'shimo-broadcast-channel';
import type {
  DisableMentionCards,
  MentionInfo,
  MouseMovePayload,
  PerformanceEntry,
  SDKEventMap,
  ShowToastOptions,
} from 'shimo-js-sdk-shared';
import {
  ContainerMethod,
  FileType,
  FileTypeAlias,
  InvokeMethod,
  isSDKEventMessage,
  ReadyState,
  SDKEvent,
} from 'shimo-js-sdk-shared';
import { StartParams } from 'shimo-startparams';
import { TinyEmitter } from 'tiny-emitter';
import url from 'url';

import { DEBUG_NAMESPACE, SDK_V2_SIGNATURE, SDK_V2_TOKEN } from '../constants';
import { allowed, getBoolean, getHeaderPrefix, getString, setEnv } from '../env';
import { adaptor } from './APIAdaptor';
import type { BaseEditor, ContainerRect, File, FileBaseItem, RuntimeEnv, URLInfo } from './types';
import { API_PATH, START_PARAMS_KEY, URLSharingType } from './types';

const AUD = 'smjssdk';

interface EditorMentionInfo extends Omit<MentionInfo, 'userId'> {
  /**
   * 编辑器传递的 userId 是数字类型
   */
  userId?: number;
}

const mouseEvents = new Set(['MouseMove', 'VerticalScroll', 'HorizontalScroll']);

/**
 * SDK 内部用户 ID 和客户 ID 的映射关系
 */
const userProviderIdToIdCache = new Map<string, number>();

/**
 * SDK 客户 ID 和内部用户 ID 的映射关系
 */
const userIdToProviderIdCache = new Map<number, string>();

/**
 * 内部 file id 和客户 file id 的映射关系
 */
const fileIdToProviderIdCache = new Map<number, string>();

/**
 * 内部 file guid 和客户 file id 的映射关系
 */
const fileGuidToProviderIdCache = new Map<string, string>();

export interface ShimoSDKOptions {
  fileType: FileType;
  eventBinder?: AddEventListenerHandler;
  file: File;
}

/**
 * 编辑器的方法调用器
 * @param method - 方法名
 * @param args - 方法参数
 * @param noNotFoundError - 是否抛出方法不存在的错误
 */
export type EditorMethodInvoker = (
  /**
   * 方法名
   */
  method: string,
  /**
   * 方法参数
   */
  args: unknown[],
  /**
   * 是否抛出方法不存在的错误
   */
  noNotFoundError?: boolean,
) => Promise<unknown>;

export type RuntimeUpdater = (runtime: RuntimeEnv) => void;

export type EventCallback = (value: unknown) => unknown;

/**
 * 编辑器事件监听器，用于 SDK 处理容器监听编辑器事件
 */
export type AddEventListenerHandler = (event: string, callback: EventCallback) => void;

/**
 * SDK 初始化参数
 */
interface SDKOptions {
  disableMentionCards?: DisableMentionCards;

  plugins?: Record<string, boolean>;
  /**
   * shimo-js-sdk里通过SDKInit注入的变量
   */
  hasOpenLink?: boolean;
  hasShowToast?: boolean;
  hasMentionClickHandlerForMobile?: boolean;

  disableSignatureComponent?: boolean;

  /**
   * 开启 debug 模式
   */
  debug?: boolean;

  /**
   * 客户的 APIAdaptor 函数，以 Function.toString() 传递
   */
  apiAdaptor?: string;

  apiAdaptorContext?: string;
}

const log = debug(DEBUG_NAMESPACE);

const defaultSDKOptions: SDKOptions = {
  // 轻文档需要这个参数存在
  disableMentionCards: {},
};

interface ShimoPrintMessage {
  command: 'print';
}

function isPrintMessageEventLike(message: unknown): boolean {
  return typeof message === 'object' && message !== null && (message as ShimoPrintMessage).command === 'print';
}

function getIdb() {
  if (typeof window !== 'undefined') {
    const win: any = window;
    return win.indexedDB || win.mozIndexedDB || win.webkitIndexedDB || win.msIndexedDB;
  }

  return null;
}

/**
 * 判断当前设备能用什么类型的 BroadcastChannel。
 */
function getBroadcastChannelType(): any | undefined {
  if (typeof BroadcastChannel === 'function') {
    return;
  }

  // iOS 13 或以下在 file:// 等场合会遇到跨域问题。
  const IndexedDB = getIdb();
  if (IndexedDB) {
    try {
      const dbName = `__sm_test__`;
      IndexedDB.open(dbName);
      IndexedDB.deleteDatabase(dbName);
      return;
    } catch (e: unknown) {}
  }

  return 'localstorage';

  /* eslint-enable */
}

export class ShimoSDK extends TinyEmitter {
  /**
   * SDK 和编辑器配置，通常由 parent window 传入，用于调整 SDK 和编辑器的配置
   */
  sdkOptions: SDKOptions = {
    ...defaultSDKOptions,
  };

  startParams: StartParams;

  /**
   * 添加编辑器事件监听器
   */
  onAddEventListener?: AddEventListenerHandler;

  editor?: BaseEditor;

  private readonly fileType: FileType;
  private _readyState: ReadyState = ReadyState.Loading;

  private _invokeEditorMethod?: EditorMethodInvoker;
  /**
   * 客户侧已监听事件列表
   */
  private readonly listenedEditorEvents = new Set<string>();
  private runtimeUpdaters: RuntimeUpdater[] = [];
  private file: File;
  private readonly channel: ShimoBroadcastChannel;
  // channel 是否连接
  private connected = false;
  private prepared = false;
  private _searchParams: URLSearchParams;

  /**
   * 性能信息列表
   */
  private performanceEntries: PerformanceEntry[] = [];

  /**
   * 已经处理过的消息 ID 缓存，主要用于避免通过 window.postMessage 传递的消息重复处理
   */
  private readonly handledMessageCache: ExpireSet<string>;

  constructor(options: ShimoSDKOptions) {
    super();

    this.fileType = options.fileType;

    this.signature = getString(SDK_V2_SIGNATURE);
    this.token = getString(SDK_V2_TOKEN);

    this.file = options.file;

    const searchParams = new URLSearchParams(location.search);
    this._searchParams = searchParams;
    this.startParams = new StartParams(searchParams.get(START_PARAMS_KEY) ?? {});

    this.debug = this.startParams.debug === true || searchParams.get('debug') === 'true';

    this.channel = new ShimoBroadcastChannel({
      channelId: this.startParams.channelId,
      debug: this.debug,
      autoStructuredClone: true,
      debugNamespace: `${DEBUG_NAMESPACE}_CHAN`,
      broadcastChannelOptions: {
        type: getBroadcastChannelType(),
      },
    });
    log('channel id', this.channel.id);

    this.channel.on('message', (evt: { data: unknown }) => {
      log('channel message', evt);

      if (isSDKEventMessage(evt.data)) {
        this.handleEvent(evt.data.event, evt.data.payload);
      }
    });

    this.channel.on('postMessage', (evt: unknown) => log('channel postMessage', evt));

    if (this.startParams.channelId === null) {
      this.startParams.channelId = this.channel.id;
    }

    // 用于缓存消息，避免 iframe 通信时，重复处理消息，按秒算
    let messageExpires = parseInt(getString('SDK_MESSAGE_EXPIRE_SEC'), 10) * 1000;
    if (!Number.isInteger(messageExpires) || messageExpires < 0) {
      messageExpires = 60 * 5 * 1000;
    }
    this.handledMessageCache = new ExpireSet<string>(messageExpires);

    // 非同源的情况，只有 parent window 会用 iframe.postMessage，需要将消息转入 channel 中处理
    if (
      window.parent !== window &&
      // cross origin 时，如果 referrer 不存在或 origin 不一致，就启用 iframe.postMessage
      (!document.referrer || location.origin !== new URL(document.referrer).origin)
    ) {
      this.channel.on(
        'postMessage',
        (evt: ShimoMessageEvent) => {
          window.parent.postMessage(evt, '*');
        },
        { audience: '*' },
      );

      window.addEventListener('message', (evt: globalThis.MessageEvent) => {
        log('received message:', evt);
        const data = evt.data;

        if (isPrintMessageEventLike(data)) {
          this.editor?.print?.(); // 轻文档、幻灯片、表格
          this.editor?.docsApi?.sendEvent?.('OnPrint'); // 传统文档
          return;
        }

        if (isShimoMessageEventLike(data)) {
          if (
            // 不是当前 channel 的消息
            data.channelId !== this.channel.id ||
            // 消息已经过期
            data.time + messageExpires < Date.now() ||
            // 已经处理过
            this.handledMessageCache.has(data.context.messageId)
          ) {
            return;
          }

          this.handledMessageCache.add(data.context.messageId);
          this.channel.distributeMessage(data).catch((err: unknown) => log(err));
        }
      });
    }

    // 修复点击 iframe 外部时，无法正确失去焦点的问题
    window.addEventListener('blur', () => {
      if (this.fileType === FileType.Form || this.editor?.keepFocus?.() === true) {
        return;
      }

      let eventType: string;
      let eventName: string;
      if ('ontouchstart' in document.documentElement) {
        eventType = 'TouchEvent';
        eventName = 'touchstart';
      } else {
        eventType = 'MouseEvents';
        eventName = 'mousedown';
      }

      const e = document.createEvent(eventType);
      e.initEvent(eventName, true, true);
      document.body.dispatchEvent(e);
    });

    this.setReadyState(ReadyState.Loading).catch((e) => {
      console.error(e);
    });

    this.markPerformanceEntry('sdk_initialized');
  }

  get readyState(): ReadyState {
    return this._readyState;
  }

  async setReadyState(state: ReadyState, err?: Error) {
    this._readyState = state;

    await this.channel.postMessage(
      {
        event: InvokeMethod.ReadyState,
        payload: {
          state: this._readyState,
          fileType: this.fileType,
          error: err instanceof Error ? err : undefined,
        },
      },
      {
        audience: AUD,
      },
    );
  }

  get signature(): string {
    return getString(SDK_V2_SIGNATURE);
  }

  /**
   * 基于石墨 app ID、secret 机制生成的鉴权 token，由客户生成和传入
   */
  set signature(signature: string) {
    setEnv(SDK_V2_SIGNATURE, signature);
    this.updateRuntime();
  }

  get token(): string {
    return getString(SDK_V2_TOKEN);
  }

  /**
   * 客户侧的鉴权 token，由客户生成和传入
   */
  set token(token: string) {
    setEnv(SDK_V2_TOKEN, token);
    this.updateRuntime();
  }

  get debug() {
    return debug.enabled(DEBUG_NAMESPACE);
  }

  /**
   * debug 模式。
   *
   * @param enable - 开关 debug 模式
   */
  set debug(enable: boolean) {
    debug.enable(enable ? DEBUG_NAMESPACE : `-${DEBUG_NAMESPACE}`);
    if (this.channel) {
      this.channel.debug = enable;
    }
    if (this.startParams) {
      this.startParams.debug = enable;
    }
  }

  /**
   * 进行 SDK 初始化工作
   */
  async prepare() {
    // 防止重复初始化
    if (this.prepared) {
      return;
    }

    this.initChannel();

    this.prepared = true;

    try {
      const options = await this.fetchSDKOptions();

      if (options) {
        if (typeof options.debug === 'boolean') {
          this.debug = options.debug;
        }

        window.__RUNTIME_ENV__.SDK_V2_API_ADAPTOR = options.apiAdaptor;
        window.__RUNTIME_ENV__.SDK_V2_API_ADAPTOR_CONTEXT = options.apiAdaptorContext;

        this.sdkOptions = {
          ...defaultSDKOptions,
          ...options,
        };
      }
    } catch (e: unknown) {
      // channel 连接超时不抛出异常
      if (e instanceof MessageTimeoutError) {
        return;
      }

      throw e;
    }

    this.connected = true;
  }

  /**
   * 将传入数据解析成 StartParams，如果解析失败，则返回 undefined，不抛出 error
   */
  parseStartParams(params: unknown[]): StartParams | undefined {
    try {
      return new StartParams(params as Array<string | StartParams | Record<string, unknown>>);
    } catch (e: unknown) {}

    return undefined;
  }

  /**
   * 将 StartParams 字符串序列化
   */
  encodeStartParams(params: StartParams | Record<string, unknown>): string {
    return new StartParams(params).toString();
  }

  /**
   * 向其他频道调用方法，仅限默认 audience
   */
  async invokeChannelMethod<T>(method: ContainerMethod | InvokeMethod, ...args: unknown[]): Promise<T> {
    if (!this.connected) {
      throw new Error('SDK channel connection failed');
    }

    return await this.channel.invoke(method, args, { audience: AUD });
  }

  /**
   * 对传入的 StartParams 进行序列化，并生成 url，url 主体由客户侧处理。
   * 对 StartParams 进行
   */
  async generateUrl(params: StartParams, info: URLInfo): Promise<string> {
    const guid = params.guid ?? params.fileGuid;
    let fileId: string | undefined;

    if (typeof guid === 'string') {
      const ids = await this.convertFileGuidToProviderId([guid]);
      fileId = ids[guid];
    }

    const fileType = this.getFileType(info?.sharingType);

    let u;
    if (info?.sharingType === URLSharingType.FormFill) {
      u = `${location.origin}/${fileType}/${fileId}/fill-form`;
    } else if (info?.sharingType === URLSharingType.FormResponseShare) {
      const path = typeof params?.path === 'string' ? params.path : '';
      u = `${location.origin}/${fileType}/${fileId}${decodeURIComponent(path)}`;
    } else if (info?.sharingText) {
      u = `${location.origin}/${fileType}/${fileId} ${info.sharingText}`;
    } else {
      u = `${location.origin}/${fileType}/${fileId}`;
    }

    const urlObject = url.parse(u);

    const searchParams = new URLSearchParams(urlObject.search ?? '');
    searchParams.set(START_PARAMS_KEY, this.encodeStartParams(params));
    urlObject.search = searchParams.toString();

    return url.format(urlObject);
  }

  getFileType(type: URLSharingType): string {
    switch (type) {
      case URLSharingType.NewDoc:
        return 'docs';
      case URLSharingType.Docx:
        return 'docx';
      case URLSharingType.PPT:
        return 'presentation';
      case URLSharingType.Sheet:
        return 'sheets';
      case URLSharingType.Table:
        return 'tables';
      default:
        return 'forms';
    }
  }

  /**
   * 对传入的 StartParams 进行序列化，并生成 url，url 主体从runtime_env中获取。
   */
  // eslint-disable-next-line @typescript-eslint/require-await
  async getInnerUrl(params: StartParams): Promise<string> {
    const u = `${getString('SDK_V2_HOST')}${getString('SDK_V2_PATH_PREFIX')}/internal/shimo-files`;
    const urlObject = url.parse(u);

    const searchParams = new URLSearchParams(urlObject.search ?? '');
    searchParams.set(START_PARAMS_KEY, this.encodeStartParams(params));
    if (this.signature) {
      searchParams.set('signature', this.signature);
    }
    if (this.token) {
      searchParams.set('token', this.token);
    }
    searchParams.set(START_PARAMS_KEY, this.encodeStartParams(params));
    urlObject.search = searchParams.toString();
    return url.format(urlObject);
  }

  /**
   * 从路径中提取 16 位的 GUID
   * @param path - 输入的路径字符串
   */
  extractGuidFromPath(path: string): string | undefined {
    const regex = /\/([a-zA-Z0-9]{16})/;
    const match = path.match(regex);
    if (match && match[1]) {
      return match[1];
    }
    return undefined;
  }

  /**
   * 对传入的 URL 进行反序列化，返回一个 StartParams 给套件 SDK 使用。
   */
  async parseUrl(input: string): Promise<StartParams | undefined> {
    const urlObject = url.parse(input);
    const searchParams = new URLSearchParams(urlObject.search ?? '');
    let smParamsObj;

    if (searchParams.has(START_PARAMS_KEY)) {
      smParamsObj = new StartParams(searchParams.get(START_PARAMS_KEY)!);
    }

    if (smParamsObj?.fileId && smParamsObj.type) {
      return smParamsObj;
    }

    // 如果smParams里没有fileGuid或 type，就必须走下面的逻辑补上
    const fileId = this.extractGuidFromPath(input);

    if (!fileId) {
      return smParamsObj;
    }

    const resMap = await this.getFileBases({
      providerFileIds: [fileId],
      returnMap: { guid: true, type: true },
    });

    if (!resMap[fileId]) {
      return undefined;
    }

    return {
      fileGuid: resMap[fileId].guid,
      guid: resMap[fileId].guid,
      type: resMap[fileId].type,
      ...smParamsObj,
    };
  }

  /**
   * 处理石墨文档内点击链接事件
   */
  async openLink(url: string): Promise<void> {
    window.open(url);
  }
  /**
   * 显示外部toast
   */
  showToast = async (options: ShowToastOptions): Promise<void> => {
    if (this.sdkOptions.hasShowToast) {
      await this.invokeChannelMethod(ContainerMethod.ShowToast, options);
    } else {
      window.parent.postMessage({ command: 'showToast', options }, '*');
    }
  };

  /**
   * 设置编辑器方法的调用器，会根据 \@shimo/sdk2-api 的结果过滤移动端可用的方法。
   * @param editor - 编辑器实例
   * @param invoker - 方法调用器
   */
  setEditorMethodInvoker(editor: BaseEditor, invoker?: EditorMethodInvoker) {
    this._invokeEditorMethod = async (method: string, args: unknown[], suppressNotFoundError?: boolean) => {
      if (!allowed(this.fileType, method)) {
        return await new Promise<void>((resolve, reject) => {
          if (suppressNotFoundError) {
            resolve();
          } else {
            reject(new Error(`method \`${method}\` not found or not support current env`));
          }
        });
      }
      if (typeof editor[method] !== 'function') {
        if (suppressNotFoundError) {
          return;
        }
        throw new Error(`unknown method: ${method}`);
      }

      try {
        if (invoker) {
          await invoker(method, args);
        }

        return (await editor[method](...args)) as unknown;
      } catch (e: unknown) {
        console.error('invoke editor method error', {
          error: e,
          method,
          args,
        });
        throw new Error(`invoke editor method error: (${method}) ${String(e)}`);
      }
    };
  }

  /**
   * 发起 HTTP request，自动处理 base 和 signature、token。
   * @param url - 目标 URL
   * @param init - fetch 的 init 参数
   */
  async request(url: string, init?: RequestInit): Promise<Response> {
    const u = new URL(`${getString('SDK_V2_PATH_PREFIX')}${url}`, location.origin);
    u.pathname = u.pathname.replace(/\/{2,}/g, '/');

    return await fetch(u.toString(), {
      headers: {
        'Content-Type': 'application/json',
        [`${getHeaderPrefix()}signature`]: this.signature,
        [`${getHeaderPrefix()}token`]: this.token,
      },
      ...init,
    });
  }

  /**
   * 将 user ID 列表进行转换
   * @param providerUserIds - 待转换的客户侧 user ID 列表
   * @param localUserIds - 内部 user ID 列表
   */
  async getUserIds(providerUserIds: string[] | null, localUserIds?: number[]): Promise<Record<string, number>> {
    const providerIds = new Set<string>();
    const localIds = new Set<number>();
    const result: Record<string, number> = {};

    if (Array.isArray(providerUserIds)) {
      for (const id of providerUserIds) {
        if (userProviderIdToIdCache.has(id)) {
          const cache = userProviderIdToIdCache.get(id)!;
          if (typeof cache === 'number') {
            result[id] = cache;
          } else {
            userProviderIdToIdCache.delete(id);
            providerIds.add(id);
          }
        } else {
          providerIds.add(id);
        }
      }
    }

    if (Array.isArray(localUserIds)) {
      for (const id of localUserIds) {
        if (userIdToProviderIdCache.has(id)) {
          const cache = userIdToProviderIdCache.get(id)!;
          if (typeof cache === 'string') {
            result[cache] = id;
          } else {
            userIdToProviderIdCache.delete(id);
            localIds.add(id);
          }
        } else {
          localIds.add(id);
        }
      }
    }

    if (providerIds.size > 0 || localIds.size > 0) {
      const resp = await this.request(API_PATH.BATCH_GET_USER_IDS, {
        method: 'POST',
        body: JSON.stringify({
          providerUserIds: Array.from(providerIds.values()),
          localUserIds: Array.from(localIds.values()),
        }),
      });

      if (resp.status === 200) {
        for (const [providerId, localId] of Object.entries<number>(await resp.json())) {
          result[providerId] = localId;
          userProviderIdToIdCache.set(providerId, localId);
          userIdToProviderIdCache.set(localId, providerId);
        }
      } else {
        throw new Error(`failed to get user ids: ${await resp.text()}`);
      }
    }

    return result;
  }

  async convertFileIdToProviderId(fileIds: number[]): Promise<Record<number, string>> {
    const rest = [] as number[];
    const cached = {} as Record<number, string>;

    for (const id of fileIds) {
      if (fileIdToProviderIdCache.has(id)) {
        const cache = fileIdToProviderIdCache.get(id)!;
        if (typeof cache === 'string') {
          cached[id] = cache;
        } else {
          fileIdToProviderIdCache.delete(id);
          rest.push(id);
        }
      } else {
        rest.push(id);
      }
    }

    if (rest.length) {
      const resp = await this.convertFileIds({
        fileIds: rest,
        returnKey: 'providerId',
        key: 'id',
      });

      for (const [key, val] of Object.entries(resp)) {
        const k = parseInt(key, 10);
        fileIdToProviderIdCache.set(k, val);
        cached[k] = val;
      }
    }

    return cached;
  }

  async convertFileGuidToProviderId(guids: string[]): Promise<Record<string, string>> {
    const rest = [] as string[];
    const cached = {} as Record<string, string>;

    for (const guid of guids) {
      if (fileGuidToProviderIdCache.has(guid)) {
        const cache = fileGuidToProviderIdCache.get(guid)!;
        if (typeof cache === 'string') {
          cached[guid] = cache;
        } else {
          fileGuidToProviderIdCache.delete(guid);
          rest.push(guid);
        }
      } else {
        rest.push(guid);
      }
    }

    if (rest.length) {
      const resp = await this.convertFileIds({
        fileGuids: rest,
        key: 'guid',
      });

      for (const [key, val] of Object.entries(resp)) {
        fileGuidToProviderIdCache.set(key, val);
        cached[key] = val;
      }
    }

    return cached;
  }

  /**
   * 根据文件 GUID、ID、file ID 进行转换
   */
  async convertFileIds(data: {
    fileGuids?: string[];
    fileIds?: number[];
    providerFileIds?: string[];

    /**
     * 返回结果中，以什么为 key，默认为 providerFileId，guid 为值；传 `guid` / `id` 则用 guid / id 为 key，providerFileId 为值
     */
    key?: 'guid' | 'id';

    /**
     * 返回结果中，以什么为值，默认为 \{guid:providerFileId\} \{id:guid\} \{providerFileId:guid\}。`guid` | `id` | `providerId`
     */
    returnKey?: 'guid' | 'providerId' | 'id';
  }): Promise<Record<string, string>> {
    const resp = await this.request(API_PATH.BATCH_CONVERT_FILE_IDS, {
      body: JSON.stringify(data),
      method: 'POST',
    });

    if (resp.status !== 200) {
      throw new Error(`failed to convert file ids: ${await resp.text()}`);
    }

    return (await resp.json()) as Record<string, string>;
  }

  /**
   * 根据文件 GUID、ID、file ID 获取文件基本信息
   *
   */

  async getFileBases(data: {
    fileGuids?: string[];
    fileIds?: number[];
    providerFileIds?: string[];

    /**
     * Key 返回结果中，以什么为 key，默认为 providerFileId，guid 为值；传 `guid` / `id` 则用 guid / id 为 key，providerFileId 为值
     */
    key?: 'guid' | 'id';

    /**
     * Key 返回结果中，需要哪些，`guid` | `providerId` | `type`
     */
    returnMap?: object;
  }): Promise<Record<string, FileBaseItem>> {
    const resp = await this.request(API_PATH.BATCH_GET_FILE_BASES, {
      body: JSON.stringify(data),
      method: 'POST',
    });

    if (resp.status !== 200) {
      throw new Error(`failed to convert file ids: ${await resp.text()}`);
    }

    return (await resp.json()) as Record<string, FileBaseItem>;
  }

  /**
   * 添加新的 RUNTIME_ENV 更新器，一般用于 worker 这类需要更新的情况
   */
  addRuntimeUpdater(updater: RuntimeUpdater): void {
    this.runtimeUpdaters.push(updater);
  }

  /**
   * 获取容器元素的宽高、viewport 信息
   */
  async getContainerRect(): Promise<ContainerRect> {
    return await this.invokeChannelMethod<ContainerRect>(ContainerMethod.GetContainerRect);
  }

  /**
   * 获取默认的通用初始化编辑器配置
   */
  getDefaultCreateEditorOptions(
    options: {
      /**
       * 是否启用预览模式
       */
      preview?: boolean;
      /**
       * 应用表格参数
       */
      tableOptions?: {
        /**
         * 预览指定应用表格版本
         */
        snapshot?: string;
        /**
         * 是否禁用应用表格预览快照 Header
         */
        disabaleSnapshotHeader?: boolean;
      };
    } = {},
  ) {
    const sdkOptions = this.sdkOptions;
    const searchParams = this._searchParams;

    return {
      generateUrl: this.generateUrl.bind(this),

      parseUrl: this.parseUrl.bind(this),

      getInnerUrl: this.getInnerUrl.bind(this),

      overrideOpenLink: this.openLink.bind(this),
      showToast: sdkOptions.hasShowToast || searchParams.get('showToast') ? this.showToast : undefined,

      disableMentionCards: sdkOptions.disableMentionCards,

      mentionClickHandlerForMobile: sdkOptions.hasMentionClickHandlerForMobile
        ? (payload: unknown) => {
            this.mentionClickHandlerForMobile(payload as MouseMovePayload).catch((e) => {
              console.error(e);
            });
          }
        : undefined,

      encodeStartParams: this.encodeStartParams.bind(this),

      getContainerRect: this.getContainerRect.bind(this),

      plugins: sdkOptions.plugins,

      startParams: this.parseStartParams([
        this.startParams,
        {
          guid: this.file.guid,
          fileGuid: this.file.guid,
          mode: options.preview ? 'preview' : undefined,
          type: this.fileType !== FileType.Unknown ? FileTypeAlias[this.fileType] : this.fileType,
          preview: options.preview,
          ...(options.tableOptions ?? {}),
        },
      ]),

      apiAdaptor: adaptor,

      onPerformanceTiming: this.markPerformanceEntry.bind(this),

      disableCollaborate: options.preview,
    };
  }

  /**
   * 记录 PerformanceEntry
   */
  markPerformanceEntry(mark: string) {
    this.performanceEntries.push({
      mark,
      timestamp: Date.now(),
      timeOrigin: performance.timeOrigin,
    });
  }

  /**
   * iframe 嵌套模式时，尝试从 parent window 获取 SDK 配置。
   */
  private async fetchSDKOptions(): Promise<Partial<SDKOptions> | null> {
    if (getBoolean('DISABLE_FETCH_OPTIONS') === true) {
      return null;
    }
    if (window.parent && window.parent !== window) {
      // 每 300 * 次数ms 尝试取一次，直到取到或达到固定次数。
      // 弱网环境通信耗时比较长，设置为30ms会一直超时导致无法获取到用户自定义配置
      let times = 0;
      while (times++ < 3) {
        try {
          const options = await this.channel.invoke<SDKOptions>(InvokeMethod.SDKInit, [], {
            audience: AUD,
            timeout: times * 300,
          });

          return options;
        } catch (e: unknown) {
          // 超过重试次数，创建连接失败
          if (times >= 3) {
            throw e;
          }
        }
      }
    }

    log('fetch sdk options', this.sdkOptions);

    return null;
  }

  /**
   * 初始化 BroadcastChannel，并监听特定消息
   */
  private initChannel(): void {
    const channel = this.channel;

    channel.addInvokeHandler(
      InvokeMethod.SetCredentials,
      (credentials: unknown): void => {
        if (isPlainObject(credentials)) {
          const { signature, token } = credentials;
          if (typeof signature === 'string') {
            this.signature = signature;
          }
          if (typeof token === 'string') {
            this.token = token;
          }
        }
      },
      { audience: AUD },
    );

    /**
     * 响应 parent window 调用编辑器方法的请求。
     */
    channel.addInvokeHandler(
      InvokeMethod.InvokeEditorMethod,
      async (method: unknown, args: unknown[]): Promise<unknown> => {
        if (typeof method !== 'string') {
          throw new Error(`invalid method name: ${String(method)}`);
        }

        if (typeof this._invokeEditorMethod !== 'function') {
          throw new Error('method caller not set');
        }

        if (method === 'mentionClickHandlerForMobile') {
          await this.handleMouseMovePayload(args[0] as MouseMovePayload);
        }

        // 采取白名单模式，避免抢 parent window 的焦点，避免内部方法调用时导致失焦
        if (method === 'startDemonstration' && !document.hasFocus()) {
          window.focus();
        }

        switch (method) {
          case 'setTitle':
            this.file.name = args[0] as string;
            return await this._invokeEditorMethod(method, args, true);

          case 'getTitle': {
            let value = await this._invokeEditorMethod(method, args, true);
            if (typeof value !== 'string') {
              value = this.file.name;
            }
            return value;
          }

          default:
            return await this._invokeEditorMethod(method, args);
        }
      },
      { audience: AUD },
    );

    /**
     * 监听编辑器事件，并转发给 parent window。
     * 由于客户侧的监听器和 SDK 内部不是同一个，因此每个事件只会监听一次，分发工作放在 parent window。
     */
    channel.addInvokeHandler(
      InvokeMethod.ListenEditorEvent,
      (event: string): void => {
        if (!this.listenedEditorEvents.has(event)) {
          this.listenedEditorEvents.add(event);
          this.onAddEventListener!(event, (payload: unknown) => {
            (async () => {
              try {
                if (mouseEvents.has(event) && payload !== null && typeof payload === 'object') {
                  await this.handleMouseMovePayload(payload as MouseMovePayload);
                } else if (event === 'paramsChanged') {
                  // StartParams 对客户应该以 string 处理，不暴露内部具体数据，客户收到后只需要处理 URL 上的替换
                  // eslint-disable-next-line no-param-reassign
                  payload = new StartParams(payload as Record<string, unknown>).toString();
                }

                await this.invokeChannelMethod(InvokeMethod.DispatchEditorEvent, event, payload);
              } catch (e: unknown) {
                log(e);
                await this.invokeChannelMethod(InvokeMethod.Error, e instanceof Error ? e.message : String(e));
              }
            })().catch((e) => {
              console.error(e);
            });
          });
        }
      },
      { audience: AUD },
    );

    /**
     * 响应 PerformanceEntry 的请求。
     */
    channel.addInvokeHandler(
      InvokeMethod.RequestPerformanceEntries,
      (): PerformanceEntry[] => {
        const entries = [
          ...(Array.isArray(window.__SM__?.performanceEntries) ? window.__SM__.performanceEntries : []),

          ...this.getResourceTimingEntries(),

          ...this.performanceEntries,
        ].sort((a, b) => a.timestamp - b.timestamp);
        return entries;
      },
      { audience: AUD },
    );
  }

  private async mentionClickHandlerForMobile(payload: MouseMovePayload) {
    await this.handleMouseMovePayload(payload);
    this.invokeChannelMethod(ContainerMethod.MentionClickHandlerForMobile, payload).catch((e) => {
      console.error(e);
    });
  }

  private getResourceTimingEntries(): PerformanceEntry[] {
    const entries: PerformanceEntry[] = [];

    if (typeof performance !== 'undefined' && typeof PerformanceResourceTiming !== 'undefined') {
      const resources = performance.getEntriesByType('resource');

      if (Array.isArray(resources)) {
        for (const resource of resources) {
          if (!(resource instanceof PerformanceResourceTiming)) {
            // eslint-disable-next-line no-continue
            continue;
          }

          if (
            // other 一般是 iframe
            resource.initiatorType !== 'other' &&
            // SDK_V2_PATH_PREFIX 的请求是 API 请求，不需要上报
            !resource.name.includes(getString('SDK_V2_PATH_PREFIX'))
          ) {
            entries.push({
              mark: resource.name,
              timestamp: performance.timeOrigin + resource.responseEnd,
              timeOrigin: performance.timeOrigin,
              resourceTiming: resource.toJSON(),
            });
          }
        }
      }
    }

    return entries;
  }

  /**
   * 对 MouseMovePayload 中数据做处理：
   * - user id / file id 替换为客户系统的 id
   * - x y 等数据因为 iframe 的特性，需要由 parent window 来处理
   *
   * @param v - 编辑器鼠标移动事件 payload
   */
  private async handleMouseMovePayload(v: MouseMovePayload): Promise<void> {
    const val = v;
    if (val.isMention) {
      const mentionInfo = val.mentionInfo as undefined | EditorMentionInfo;
      if (mentionInfo?.userId) {
        const id = Object.keys(await this.getUserIds(null, [mentionInfo?.userId]))[0];
        if (id) {
          val.mentionInfo.userId = id;
        }
      }

      if (val.mentionInfo?.fileId) {
        const numId =
          typeof val.mentionInfo.fileId === 'number' ? val.mentionInfo.fileId : parseInt(val.mentionInfo.fileId, 10);

        // 数字 id
        if (!isNaN(numId)) {
          const id = (await this.convertFileIdToProviderId([numId]))[numId];
          if (id) {
            val.mentionInfo.fileId = id;
          }
        } else {
          // guid
          const guid = val.mentionInfo.fileId;
          const id = (await this.convertFileGuidToProviderId([guid]))[guid];
          if (id) {
            val.mentionInfo.fileId = id;
          }
        }
      }
    }
  }

  /**
   * 在更新 RuntimeEnv 时触发
   */
  private updateRuntime(): void {
    this.triggerRuntimeUpdater();
    this.updatePageUrl();
  }

  /**
   * 更新页面 URL 中的 query 里包含的 signature token 等信息
   */
  private updatePageUrl(): void {
    const { search } = location;
    const url = new URLSearchParams(search);

    let hasChanged = false;

    // 如果 signature 和 query 中的 signature 不一致，更新 query 中的 signature
    if (url.has('signature')) {
      const signature = url.get('signature');

      if (signature !== this.signature) {
        url.set('signature', this.signature);
        hasChanged = true;
      }
    }

    // 同上
    if (url.has('token')) {
      const token = url.get('token');

      if (token !== this.token) {
        url.set('token', this.token);
        hasChanged = true;
      }
    }

    if (hasChanged) {
      const { pathname, hash } = location;
      history.replaceState({}, '', `${pathname}?${url.toString()}${hash}`);
    }
  }

  /**
   * 触发 runtime env 的更新，比如表格 web worker 中的 runtime env。
   * 需要由外部提供更新函数，用 addRuntimeUpdater 更新。
   */
  private triggerRuntimeUpdater(): void {
    for (const updater of this.runtimeUpdaters) {
      if (typeof updater === 'function') {
        updater(window.__RUNTIME_ENV__);
      }
    }
  }

  /**
   * 处理 SDK 事件。
   */
  private handleEvent<T extends keyof SDKEventMap>(event: T, payload: unknown): void {
    if (event === SDKEvent.ViewportResize) {
      /**
       * "visualViewportResize" https://shimo.im/sheets/XGGdK3gRHxpXgqck/MODOC#anchor-B27:B27
       */
      this.editor?.emit('visualViewportResize', payload);
    }
  }
}

export type CreateSDKFunction = (options: ShimoSDKOptions) => Promise<ShimoSDK>;

export const createSDK = async (options: ShimoSDKOptions): Promise<ShimoSDK> => {
  const sdk = new ShimoSDK(options);
  await sdk.prepare();
  return sdk;
};
