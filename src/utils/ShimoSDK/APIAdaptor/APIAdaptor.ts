/* eslint-disable @typescript-eslint/ban-types */
/**
 * ！！！！！！！！！注意 ！！！！！！！！！
 * 这个文件不能有任何外部依赖，这个文件的代码会被序列化后由表格套件传入表格的 worker 环境中通过 eval 执行，所以不能有外部依赖
 */
// import type { SMGlobal } from '../ShimoSDK/types';

import type { SMGlobal } from '../types';

export default function adaptor(options: RequestOptions): RequestOptions {
  // Data URI 不需要处理
  // eslint-disable-next-line @typescript-eslint/prefer-string-starts-ends-with
  if (options.url.indexOf('data:') === 0) {
    return options;
  }
  function getGlobal() {
    if (typeof window !== 'undefined') {
      return window;
    } else if (typeof self !== 'undefined') {
      // 支持 web worker，需要 worker 使用方实现更新 __RUNTIME_ENV__ 的方式，由外部调用更新
      return self;
    }

    /* eslint-disable-next-line no-new-func,@typescript-eslint/no-unsafe-return,@typescript-eslint/no-implied-eval */
    return Function('return this')();
  }

  const global = getGlobal();
  if (global.__SM__ === null) {
    global.__SM__ = {};
  }
  const SM = global.__SM__ as SMGlobal;

  const requestOptions = options;

  function getEnv(name: string): unknown {
    return global?.__RUNTIME_ENV__?.[name];
  }

  const sdkEnv = getEnv('SDK_ENV');
  // Data URI 不需要处理
  // eslint-disable-next-line @typescript-eslint/prefer-string-starts-ends-with
  if (sdkEnv !== true || options.url.indexOf('data:') === 0) {
    return requestOptions;
  }

  function getEnvString(name: string): string {
    const v = getEnv(name);
    return typeof v === 'string' ? v : '';
  }

  function getPrefix(name: string): string {
    const prefix = getEnvString(name);
    return prefix.endsWith('/') ? prefix : `${prefix}/`;
  }

  function getHeaderPrefix(): string {
    let prefix = getEnvString('SDK_V2_HEADER_PREFIX');

    if (!prefix) {
      prefix = atob('eC1zaGltby0=');
      global.__RUNTIME_ENV__ = {
        ...global.__RUNTIME_ENV__,
        SDK_V2_HEADER_PREFIX: prefix,
      };
    }

    return prefix;
  }

  // 主站旧 API 前缀
  const LIZARD_API_PREFIX = '/lizard-api/';
  // 协同相关的 API 从 /lizard-api/* 变为 /edit/*
  const EDIT_API_PREFIX = '/edit/';
  // 主站部分新 API 前缀
  const PANDA_API_PREFIX = '/panda-api/';
  // 替换所有 /api/ 前缀但没使用 API_PATH 的接口，需要过滤 /sdk/v2/api/ 的
  const API_PREFIX = '/api/';
  /**
   * 新评论服务 API 前缀
   */
  const COMMENT_API_PREFIX = '/comment-api/';
  /**
   * 表单服务 API 前缀
   */
  const FORM_API_PREFIX = '/api/newforms/';

  const SDK_V2_SIGNATURE = 'SDK_V2_SIGNATURE';
  const SDK_V2_TOKEN = 'SDK_V2_TOKEN';

  const SDK_V2_PATH_PREFIX = getPrefix('SDK_V2_PATH_PREFIX');

  const SIGNATURE_KEY = `${getHeaderPrefix()}signature`;
  const TOKEN_KEY = `${getHeaderPrefix()}token`;
  const PRODUCT_KEY = `${getHeaderPrefix()}product`;

  // SDK 自动禁止 credential 信息，默认使用 token 鉴权
  if (requestOptions.withCredentials && !getEnv('SDK_V2_CREDENTIAL')) {
    requestOptions.withCredentials = false;
  }

  requestOptions.headers = requestOptions.headers ?? {};
  requestOptions.query = requestOptions.query ?? {};

  /**
   * 判断是否需要跳过 URL 处理
   */
  function shouldSkip(): boolean {
    // 当 url query 出现某些 key 时，不处理，SDK_V2_API_QUERY_EXCLUSIVE 格式为 foo,bar
    const exclusive = ['signature', 'token'].concat(
      getEnvString('SDK_V2_API_QUERY_EXCLUSIVE')
        .split(',')
        .filter((i) => i.trim()),
    );

    for (const k in requestOptions.query) {
      if (exclusive.some((e) => k.toLowerCase().indexOf(e) > -1)) {
        return true;
      }
    }

    /**
     * 当 header 出现特定 key 时，不处理
     */
    if (
      SIGNATURE_KEY in requestOptions.headers ||
      TOKEN_KEY in requestOptions.headers ||
      'authorization' in requestOptions.headers
    ) {
      return true;
    }

    return false;
  }

  if (shouldSkip()) {
    return requestOptions;
  }
  if (requestOptions.url.indexOf(SDK_V2_PATH_PREFIX) === -1) {
    const legacyAPIs = ['/api/sheetcalc', '/api/users/me'].concat(
      getEnvString('SDK_V2_API_TRANSFORM_LIST')
        .split(',')
        .filter((i) => i),
    );

    let replaced = false;

    // URL 替换规则
    // [[ 旧的 prefix, 替换后的 prefix ], ...]
    const URL_REPLACER_MAP = [
      [LIZARD_API_PREFIX, SDK_V2_PATH_PREFIX],
      [EDIT_API_PREFIX, SDK_V2_PATH_PREFIX],
      [PANDA_API_PREFIX, getPrefix('SDK_V2_PATH_PREFIX_PANDA_API')],
      [COMMENT_API_PREFIX, SDK_V2_PATH_PREFIX],
      [FORM_API_PREFIX, SDK_V2_PATH_PREFIX],
    ];

    for (let index = 0; index < URL_REPLACER_MAP.length; index++) {
      const srcPrefix = URL_REPLACER_MAP[index][0];
      const tgtPrefix = URL_REPLACER_MAP[index][1];
      if (requestOptions.url.indexOf(srcPrefix) > -1) {
        requestOptions.url = requestOptions.url.replace(srcPrefix, tgtPrefix);
        // 替换过之后应该不需要再继续找了
        replaced = true;
        break;
      }
    }

    if (!replaced && legacyAPIs.some((i) => requestOptions.url.indexOf(i) > -1)) {
      requestOptions.url = requestOptions.url.replace(API_PREFIX, SDK_V2_PATH_PREFIX);
    }
  }
  const sig = getEnvString(SDK_V2_SIGNATURE);
  const token = getEnvString(SDK_V2_TOKEN);
  requestOptions.headers[SIGNATURE_KEY] = sig;
  requestOptions.headers[TOKEN_KEY] = token;
  requestOptions.headers[PRODUCT_KEY] = 'sdk';

  function parseFunction(fnStr: string): Function {
    /* eslint-disable no-new-func,@typescript-eslint/no-unsafe-return,@typescript-eslint/no-implied-eval */

    try {
      /**
       * function () {}
       * function fn() {}
       * () => value
       * () => {}
       */
      return new Function('a', 'b', `return (${fnStr})(a, b)`);
    } catch (e: unknown) {}

    try {
      /**
       * { fnName () {} } -> fnName () {}
       */
      return new Function('a', 'b', `return (function ${fnStr})(a, b)`);
    } catch (e: unknown) {}

    /* eslint-enable */

    throw new Error(`invalid custom adaptor: ${fnStr}`);
  }

  /**
   * 调用自定义 API Adaptor 进行最后处理，会从 SDK_V2_API_ADAPTOR 和 SDK_V2_API_ADAPTOR_CONTEXT 取结果，如果任一解析出错，会直接抛错误
   */
  function invokeCustomAPIAdaptor(options: RequestOptions): RequestOptions {
    const customAdaptorFuncString = getEnvString('SDK_V2_API_ADAPTOR');
    if (customAdaptorFuncString === '') {
      return options;
    }

    if (SM.customAdaptorFuncString !== customAdaptorFuncString || typeof SM.customAdaptor !== 'function') {
      SM.customAdaptor = parseFunction(customAdaptorFuncString);
      SM.customAdaptorFuncString = customAdaptorFuncString;
    }

    const customContext = getEnvString('SDK_V2_API_ADAPTOR_CONTEXT');
    if (SM.customAdaptorContextString !== customContext && customContext !== '') {
      SM.customAdaptorContext = JSON.parse(customContext);
      SM.customAdaptorContextString = customContext;
    }

    return SM.customAdaptor(options, SM.customAdaptorContext ?? {}) as RequestOptions;
  }

  return invokeCustomAPIAdaptor(requestOptions);
}

export interface RequestOptions {
  url: string;
  method: string;
  headers: {
    [K: string]: string;
  };
  query?: Record<string, string>;
  body?: Record<string, unknown>;
  withCredentials?: boolean;
}
