export const themeConfig = {
  token: {
    borderRadius: 2,
    colorPrimary: 'var(--theme-basic-color-primary)',
  },
  components: {
    DatePicker: {
      cellActiveWithRangeBg: 'var(--theme-datepicker-color-cell-active-range-bg)',
      activeShadow: 'var(--theme-brand-color)',
    },
    Menu: {
      subMenuItemBorderRadius: 4,
      borderRadius: 4,
      itemBorderRadius: 4,
      itemPaddingInline: 1,
    },
    Layout: {
      headerPadding: '0 40px 0 20px',
      headerHeight: 48,
      headerBg: 'var(--theme-layout-color-bg-white)',
    },
    Table: {
      cellFontSize: 13,
      cellPaddingBlock: 10,
      rowSelectedBg: 'var(--theme-menu-color-bg-hover)',
      rowSelectedHoverBg: 'var(--theme-menu-color-bg-hover)',
      headerSplitColor: 'none',
      cellPaddingInline: 12,
    },
    Dropdown: {
      paddingBlock: 8,
    },
    Button: {
      borderRadius: 4,
      paddingInline: 15,
      defaultShadow: 'none',
      primaryShadow: 'none',
      dangerShadow: 'none',
      contentFontSizeSM: 13,
      defaultHoverBorderColor: 'var(--theme-menu-color-bg-hover)',
    },

    Input: {
      activeShadow: `var(--theme-input-color-active-shadow)`,
      activeBorderColor: 'var(--theme-input-color-active-border)',
      hoverBorderColor: 'var(--theme-input-color-active-border)',
      borderColor: 'var(--theme-separator-color-light)',
    },
    InputNumber: {
      activeShadow: `0 0 0 2px #eee`,
    },
    Tooltip: {
      fontSize: 13,
    },
    Select: {
      optionFontSize: 14,
      optionSelectedFontWeight: 400,
      activeBorderColor: 'var(--theme-select-color-active-border)',
      activeOutlineColor: 'var(--theme-select-color-active-shadow)',
      optionSelectedBg: 'var(--theme-select-color-active-bg)',
    },
    Tree: {
      nodeSelectedBg: 'var(--theme-select-color-selected-bg)',
    },
  },
};
