import { Space, Tooltip } from 'antd';

import { getFileIcon, openFile } from '@/utils/file';

export const FileName = ({ record, name, disabled }: { record: any; name: string; disabled?: boolean }) => {
  let type = null;
  if (record.isShortcut) {
    type = record.shortcutSource.type;
  } else {
    type = record.type;
  }
  return (
    <Space align="start" className="fileInfo">
      <img height={20} src={getFileIcon({ type: type, isSpace: record.isSpace })} width={20} />
      <Tooltip placement="top" title={name}>
        <span
          className="fileName"
          onClick={(event) => {
            // 事件穿透，列表被勾选
            event.stopPropagation();

            const newArg = record;
            if (record.isShortcut) {
              const { url, type } = record.shortcutSource;
              newArg.type = type;
              newArg.url = url;
            }
            openFile({ ...record, disabled });
          }}
        >
          {name}
        </span>
      </Tooltip>
      {record.starred && <img height={20} src={getFileIcon({ type: 'favorites' })} width={20} />}
    </Space>
  );
};
