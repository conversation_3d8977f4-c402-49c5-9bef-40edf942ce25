import { message } from 'antd';
import { history } from 'umi';

import { getNavigation } from '@/api/File';
// 1:文件夹，2:桌面，3:团队空间
export const getPositioning = async (record: { guid: string }) => {
  try {
    const response = await getNavigation(record.guid);
    const { data } = response;
    const rootType = data.rootType;
    if (data.ancestors.length <= 1) {
      message.warning('无权限打开该文件的上级目录');
    }
    if (data.ancestors.length > 1) {
      let url = '';
      if (rootType === 1) {
        url = `folder/${data.ancestors[data.ancestors.length - 2].guid}`;
      }
      if (rootType === 2 || rootType === 3) {
        url = `folder/${data.ancestors[data.ancestors.length - 1].guid}`;
      }
      history.push(url);
    }
  } catch (error) {
    message.error('获取数据失败，请重试');
  }
};
