import { FileType } from 'shimo-js-sdk-shared';

import { CustomEventName } from '@/model/CustomEvent';
import { getBrandConfig } from '@/utils/Brand';
import { emitCustomEvent } from '@/utils/customEvent';
import { renderEditorBanner } from '@/utils/EditorBanner/renderEditorBanner';
import { renderUserCard } from '@/utils/RenderUserCard';
import type { EventCallback, ShimoSDK } from '@/utils/ShimoSDK';
import { ReadyState } from '@/utils/ShimoSDK/types';

import { createCheckPermission } from '../../CheckPermission';
import { ShimoFileType } from '../../CheckPermission/scripts';

const loadSdk = async () => {
  return new Promise<any>(function handler(resolve, reject) {
    if (window.smForm) {
      resolve(window.smForm);
    } else {
      setTimeout(() => {
        handler(resolve, reject);
      }, 50);
    }
  });
};

export async function initializer() {
  let sdk: ShimoSDK | undefined;

  await (async () => {
    const elm = document.createElement('div');
    elm.style.height = '100%';
    const rootElm = document.querySelector('#editor-content')!;
    rootElm.appendChild(elm);

    const { file, user } = window;

    sdk = await window.createSDK({
      fileType: FileType.Form,
      file,
    });
    sdk.setReadyState(ReadyState.LoadingEditor).catch((e) => {
      console.error(e);
    });

    const socketResponse = window.initSocket(sdk, { fileGuid: file.guid });
    const socket = socketResponse.socket;

    sdk.addRuntimeUpdater(socketResponse.configUpdater);
    const checkPermission = await createCheckPermission(ShimoFileType.form);
    const renderNotificationBar = (props: { dom: HTMLDivElement }) => {
      const { dom } = props;
      renderEditorBanner({
        socket,
        dom,
      });
    };
    const formType = new URL(location.href).searchParams.get('formType') || undefined;

    // 确保smForm已经加载完成再createSDK2
    await loadSdk();

    const editor = await window.smForm.createSDK2({
      file,
      user,
      socket,
      container: elm,
      formType,

      ...sdk.getDefaultCreateEditorOptions(),

      plugins: {
        ...sdk.sdkOptions.plugins,
        scriptsStore: false, // 禁用脚本插件相关请求
      },
      renderUserCard,
      checkPermission,
      getBrandConfig,
      renderNotificationBar,
    });

    rootElm.classList.add('rendered');
    sdk.editor = editor;

    window.__SM__ = {
      ...window.__SM__,
      editor,
    };

    editor.on('titleChange', (newTitle: string) => {
      emitCustomEvent(CustomEventName.editorTitleChange, { newTitle });
    });

    emitCustomEvent(CustomEventName.socketReady, { socket });

    sdk.setEditorMethodInvoker(editor);

    sdk.onAddEventListener = (event: string, callback: EventCallback) => {
      editor.on(event, callback);
    };

    sdk.setReadyState(ReadyState.Ready).catch((e) => {
      console.error(e);
    });

    sdk.markPerformanceEntry('editor_render_end');
  })().catch((e) => {
    if (sdk) {
      sdk.setReadyState(ReadyState.Failed, e).catch((e) => {
        console.error(e);
      });
    }
    console.error(e);
  });
}
