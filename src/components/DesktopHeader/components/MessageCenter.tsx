import { BellOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Empty, List, message, Popover } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import type { NotificationWrapper } from '@/api/Message';
import { getNotifications, getUnpeekedCount, readAll, readAlon } from '@/api/Message';
import { ReactComponent as CheckSvg } from '@/assets/images/svg/check.svg';
import { useGroupNotifications } from '@/hooks/useGroupNotifications';
import { useViewportHeight } from '@/hooks/useViewportHeight';
import { useFormatMessage } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import styles from '../index.less';
import { MessageCard } from './MessageCard';

interface GroupedNotification {
  createdAt: string;
  children: NotificationWrapper[];
}

export const MessageCenter = () => {
  const i18nText = {
    filterRead: useFormatMessage('MessageCenter.onlyUnreadButtonText'),
    readAll: useFormatMessage('MessageCenter.allMarkReadButtonText'),
    noPermission: useFormatMessage('Error.noPermission'),
  };
  const [data, setData] = useState<GroupedNotification[]>([]);

  const [count, setCount] = useState(0);

  const [open, setOpen] = useState(false);

  const { formatTime } = useFormatTime();

  const [status, setStatus] = useState<'all' | 'unread'>('all');

  const { groupNotifications } = useGroupNotifications();
  const height = useViewportHeight();

  const firstNotificationsId = localStorage.getItem('firstNotificationsId');

  /** 查看未读计数 */
  const getUnReadCount = () => {
    getUnpeekedCount().then((res) => {
      if (res.status === 200) {
        setCount(res.data);
      }
    });
  };

  const getMessageList = (isSetNotificationsId?: boolean) => {
    getNotifications({ status, limit: 20 }).then((res) => {
      if (res.status === 200) {
        const groupData = groupNotifications(res.data || [], (item) => item.createdAt);
        setData(groupData);
        if (isSetNotificationsId) {
          if (res.data.length > 0) {
            localStorage.setItem('firstNotificationsId', res.data[0]['id']);
          }
        }
      }
    });
  };

  const changeStatus = () => {
    setStatus(status === 'all' ? 'unread' : 'all');
  };

  const readAllMessage = () => {
    readAll()
      .then(() => {
        getUnReadCount();
        getMessageList();
      })
      .catch(() => {});
  };

  const readMessage = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    readAlon(id);
  };

  const cb = () => {
    getUnReadCount();
    getMessageList();
  };

  useEffect(() => {
    getUnReadCount();
  }, []);

  const onOpenChange = (open: boolean) => {
    setOpen(open);
    if (open) {
      getMessageList(true);
    }
  };

  const hidden = useMemo(() => {
    if (open) return true;
    if (!data.length) return true;
    if (!count) return true;
    if (!firstNotificationsId) return false;
    if (firstNotificationsId) {
      return data[0].children[0].id === firstNotificationsId;
    }
  }, [data, count, open]);

  useEffect(() => {
    getMessageList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status]);

  return (
    <Popover
      content={
        <Card
          className={styles.messageContent}
          extra={
            <Button
              className={styles['messageTopButton']}
              disabled={!count}
              icon={<CheckSvg />}
              size="small"
              type="text"
              onClick={readAllMessage}
            >
              {i18nText.readAll}
            </Button>
          }
          title={
            <Button
              className={styles['messageTopButton']}
              size="small"
              type={status === 'all' ? 'text' : 'default'}
              onClick={changeStatus}
            >
              {i18nText.filterRead}
            </Button>
          }
        >
          <div
            style={{
              height: height - 120,
              overflowY: 'scroll',
            }}
          >
            {data.length ? (
              <List
                dataSource={data}
                renderItem={({ children, createdAt }, groupIndex) => (
                  <List
                    key={groupIndex}
                    dataSource={children}
                    header={<span className={styles.messageHeaderClassName}>{formatTime(createdAt, 'message')}</span>}
                    itemLayout="horizontal"
                    renderItem={(item, index) => (
                      <List.Item
                        key={index}
                        onClick={(e) => {
                          if (!item.isRead) {
                            // 未读
                            readMessage(e, item.id);
                          }
                          if (!item.notification?.file) {
                            message.warning(i18nText.noPermission);
                            return;
                          }
                          window.open(item.notification?.file?.url);
                        }}
                      >
                        <MessageCard callback={cb} item={item} />
                      </List.Item>
                    )}
                  />
                )}
              />
            ) : (
              <Empty />
            )}
          </div>
        </Card>
      }
      placement="bottom"
      trigger={['click']}
      onOpenChange={onOpenChange}
    >
      <Badge color="#6DA0E3" dot={!hidden} offset={[-8, 18]} size="small">
        <Button icon={<BellOutlined />} type="text" />
      </Badge>
    </Popover>
  );
};
