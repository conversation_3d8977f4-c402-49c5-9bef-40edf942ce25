/**
 * 目前方案暂定为共用组件;
 * todo: 改造方案, 后续会在H5段umi路由配置包H5的layout路由;
 */

import { LeftOutline } from 'antd-mobile-icons';
import React from 'react';

import styles from './index.less';

interface ActionBarProps {
  title?: string;
  onBack?: () => void;
  leftActions?: React.ReactNode[];
  rightActions?: React.ReactNode[];
  slots?: React.ReactNode;
}

const ActionBar: React.FC<ActionBarProps> = ({ title, onBack, leftActions = [], rightActions = [], slots }) => {
  return (
    <div className={styles.actionBar}>
      {slots ? (
        slots
      ) : (
        <>
          {onBack && (
            <div className={styles.left} onClick={onBack}>
              <LeftOutline />
            </div>
          )}

          {leftActions && (
            <div className={styles.left}>
              {leftActions.map((action, index) => (
                // eslint-disable-next-line react/no-array-index-key
                <div key={index} className={styles.action}>
                  {action}
                </div>
              ))}
            </div>
          )}

          {title && <div className={styles.title}>{title}</div>}

          <div className={styles.right}>
            {rightActions.map((action, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <div key={index} className={styles.action}>
                {action}
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default ActionBar;
