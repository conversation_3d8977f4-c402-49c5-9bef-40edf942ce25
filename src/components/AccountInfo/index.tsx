import './index.less';

import type { MenuProps } from 'antd';
import { Avatar, Menu } from 'antd';
import React, { useState } from 'react';
import { history } from 'umi';

import { ReactComponent as EnterpriseIcon } from '@/assets/images/svg/enterprise.svg';
import { ReactComponent as EnterpriseManage } from '@/assets/images/svg/enterpriseManage.svg';
import { ReactComponent as PersonIcon } from '@/assets/images/svg/person.svg';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

export type MenuItem = Required<MenuProps>['items'][number];
const SettingSiderMenu: React.FC = () => {
  const i18nText: any = {
    accountInfo: $t('Profile.accountInfo'),
    preferenceSitting: $t('Profile.preferenceSitting'),
  };
  const [keySelected, setKeySelected] = useState('AccountInformation');
  const me = useMeStore((state) => state.me);
  const goManagement = () => {
    window.open('/enterprise/members'); // 默认跳转通讯录
  };
  const items: MenuItem[] = [
    {
      key: 'AccountInformation',
      icon: <PersonIcon />,
      label: i18nText.accountInfo,
      onClick: () => {
        setKeySelected('AccountInformation');
        history.push('/profile/accountinfo');
      },
    },
    // {
    //   key: 'PreferenceSettings',
    //   icon: <SettingOutlined />,
    //   label: i18nText.preferenceSitting,
    //   onClick: () => {
    //     setKeySelected('PreferenceSettings');
    //     history.push('/profile/preference');
    //   },
    // },
  ];
  return (
    <div className="siderContainer">
      <div>
        <div className="userInfoBox">
          <Avatar size={82} src={me.avatar} />
          <div className="enterpriseIcon">
            <div className="dividerLine" />
            <EnterpriseIcon />
            <div className="dividerLine" />
          </div>
          <div className="userNameBox">{me.name}</div>
          <div className="userEmailBox">{me.email}</div>
          <div className="userEmailBox">{me.team?.name}</div>
        </div>
        <Menu className="styledMenu" items={items} selectedKeys={[keySelected]} style={{ borderInlineEnd: 'none' }} />
      </div>
      <div className="enterpriseBox" onClick={goManagement}>
        <EnterpriseManage /> <span className="marginIcon8">{$t('SiderMenu.siderMenuBusinessText')}</span>
      </div>
    </div>
  );
};
export default React.memo(SettingSiderMenu);
