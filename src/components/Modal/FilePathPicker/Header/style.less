.header {
  display: flex;
  align-items: center;
  padding: 7px 20px;
}

.locationBreadcrumb {
  align-self: end;
  display: flex;
  align-items: center;
  height: 32px;

  :global(.ant-btn-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .forwardBtn {
    margin-left: 24px;
  }

  .backBtn,
  .forwardBtn {
    width: 24px;
    height: 24px;
    font-size: 12px;
    color: var(--theme-text-color-default);

    :global(.ant-btn-icon) {
      font-size: 12px;
    }

    &:hover:not(:disabled) {
      background-color: var(--theme-text-button-color-hover);
    }

    &:disabled {
      color: var(--theme-text-color-disabled);
    }
  }
}

.fileInfo {
  width: 359px;
  margin-left: 76px;

  :global(.ant-input) {
    color: var(--theme-text-color-default);
    background-color: var(--theme-basic-color-bg-default);
  }
}

.fileName {
  display: flex;
  align-items: center;
  margin-bottom: 14px;

  :global(.ant-input) {
    flex: 1;
    height: 32px;
    margin-left: 15px;
  }

  .fileNameText {
    width: max-content;
    color: var(--theme-text-color-default);
  }
}

.fileNav {
  display: flex;
  align-items: center;

  :global {
    .ant-select {
      flex: 1;
      height: 32px;
      overflow: hidden;
      margin-left: 15px;

      &.ant-select-single.ant-select-open {
        .ant-select-selection-item {
          color: var(--theme-text-color-default);
        }
      }

      .ant-select-selector {
        color: var(--theme-text-color-default);
        background-color: var(--theme-basic-color-bg-default);
        border-color: var(--theme-basic-color-black);
        box-shadow: none;

        &:hover {
          border-color: var(--theme-basic-color-black);
        }

        .ant-select-prefix {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 8px;
        }
      }

      .ant-select-arrow {
        color: var(--theme-text-color-secondary);
      }

      .ant-select-selection-placeholder {
        color: var(--theme-basic-color-black);
      }
    }
  }

  .fileNavText {
    width: max-content;
  }
}

.viewControls {
  display: flex;
  align-items: center;
  margin-left: auto;
  align-self: end;
  height: 32px;

  .searchIcon {
    font-size: 18px;
    cursor: pointer;
  }
}
