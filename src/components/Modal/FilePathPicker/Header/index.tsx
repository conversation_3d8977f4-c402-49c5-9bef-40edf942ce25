import { Button, Input, Select } from 'antd';

import folderIcon from '@/assets/images/fileIcon/<EMAIL>';
import { ReactComponent as DesktopIcon } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as SpaceIcon } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as ArrowLeftIcon } from '@/assets/images/svg/arrowLeft-18.svg';
import { ReactComponent as ArrowRightIcon } from '@/assets/images/svg/arrowRight-18.svg';
import { ReactComponent as CheckOutlined } from '@/assets/images/svg/check.svg';
import { ReactComponent as SolidArrowDownIcon } from '@/assets/images/svg/solidArrowDown.svg';
import { fm2 } from '@/modules/Locale';

import type { FolderItem, LocationOption } from '../FolderManager';
import { Search } from '../Search/index';
import css from './style.less';

interface Props {
  curFolder: FolderItem | null;
  canGoBack: boolean;
  canGoForward: boolean;
  curLocationName: string;
  curLocationOptions: LocationOption[];
  filename?: string;
  onLocationChange: (data: LocationOption) => void;
  sideMenuId: string;
  type?: 'create' | 'move' | 'shortcut';
  onGoBack: () => void;
  onGoForward: () => void;
  onSearchItemClick: (folder: FolderItem) => void;
}

export function Header({
  curFolder,
  canGoBack,
  canGoForward,
  curLocationName,
  curLocationOptions,
  filename,
  sideMenuId,
  type = 'create',
  onGoBack,
  onGoForward,
  onLocationChange,
  onSearchItemClick,
}: Props) {
  function renderSelectPrefix() {
    if (curFolder?.sourceMenuId === 'space' && !curFolder.isChild) {
      return <SpaceIcon />;
    }
    if (curLocationName === 'desktop') {
      return <DesktopIcon />;
    }
    if (curFolder) {
      return <img src={folderIcon} style={{ width: 18 }} />;
    }
    return null;
  }

  function renderSelectOption(data: LocationOption) {
    const { value, sourceMenuId, label } = data;

    let icon;
    if (sourceMenuId === 'space' || value === 'space') {
      icon = <SpaceIcon style={{ color: 'var(--theme-text-color-default)' }} />;
    } else if (value === 'desktop') {
      icon = <DesktopIcon />;
    } else {
      icon = <img src={folderIcon} style={{ width: 18 }} />;
    }

    const isSelected =
      (value === sideMenuId && !curFolder) ||
      value === curFolder?.id ||
      (['used', 'shared', 'favorites'].includes(sideMenuId) && value === 'desktop');

    return (
      <div style={{ display: 'flex', alignItems: 'center', height: '100%' }} onClick={() => onLocationChange(data)}>
        {icon}
        <span style={{ marginLeft: 8, flex: 1, overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' }}>
          {label}
        </span>
        {isSelected && (
          <CheckOutlined
            style={{ color: 'var(--theme-basic-color-primary)', marginLeft: 'auto', paddingLeft: '30px' }}
          />
        )}
      </div>
    );
  }

  return (
    <div className={css.header}>
      {/* 位置选择 */}
      <div className={css.locationBreadcrumb}>
        {/* 后退按钮 */}
        <Button className={css.backBtn} disabled={!canGoBack} icon={<ArrowLeftIcon />} type="text" onClick={onGoBack} />
        {/* 前进按钮 */}
        <Button
          className={css.forwardBtn}
          disabled={!canGoForward}
          icon={<ArrowRightIcon />}
          type="text"
          onClick={onGoForward}
        />
      </div>
      {/* 文件名 */}
      <div className={css.fileInfo}>
        {type === 'create' && (
          <div className={css.fileName}>
            <span className={css.fileNameText}>{fm2('FilePathPicker.fileName')}</span>
            <Input disabled value={filename} />
          </div>
        )}
        {/* 文件操作 */}
        <div className={css.fileNav}>
          <span className={css.fileNavText}>
            {type === 'create' ? fm2('FilePathPicker.createIn') : type === 'move' ? fm2('FilePathPicker.moveTo') : ''}
          </span>
          <Select
            choiceTransitionName="" // 移除选项选择的过渡动画
            optionRender={(option) => renderSelectOption(option.data)}
            options={curLocationOptions}
            placeholder={fm2('FilePathPicker.selectPlaceholder')}
            popupClassName="locationSelectDropdown"
            prefix={renderSelectPrefix()}
            suffixIcon={<SolidArrowDownIcon />}
            transitionName="" // 移除下拉框打开/关闭的过渡动画，实现立即关闭
            value={curLocationName === 'space' ? undefined : curLocationName || undefined}
          />
        </div>
      </div>

      {/* 视图选择 暂时注释，后续再放开 */}
      <div className={css.viewControls}>
        {/* <Radio.Group
            value={viewMode}
            onChange={(e) => handleViewModeChange(e.target.value as ViewMode)}
            buttonStyle="solid"
          >
            <Radio.Button value="list">
              <UnorderedListOutlined />
            </Radio.Button>
            <Radio.Button value="grid">
              <AppstoreOutlined />
            </Radio.Button>
          </Radio.Group> */}
        <Search onItemClick={onSearchItemClick} />
      </div>
    </div>
  );
}
