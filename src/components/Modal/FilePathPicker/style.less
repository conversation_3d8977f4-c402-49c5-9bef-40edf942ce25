.filePathPicker {
  * {
    box-sizing: border-box;
  }

  :global {
    .ant-modal-content {
      padding: 0;

      .ant-modal-body {
        .ant-modal-confirm-paragraph {
          max-width: 100%;
        }

        .ant-modal-confirm-body-wrapper {
          .ant-modal-confirm-body {
            .ant-modal-confirm-content {
              margin-top: 0;
            }
          }
        }
      }
    }
  }

  // 内容区域
  .modalContent {
    display: flex;
    height: 528px;
    background-color: var(--theme-basic-color-bg-default);
    color: var(--theme-text-color-default);
  }

  .mainContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .fileList {
    flex: 1;
    overflow-y: auto;
    padding: 10px 2px 10px 21px;
    border-top: 1px solid var(--theme-box-shadow-color-level8);
    border-bottom: 1px solid var(--theme-box-shadow-color-level8);

    .fileTree {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    :global {
      .ant-spin-nested-loading {
        height: 100%;
        margin-right: 19px;

        .ant-spin-container {
          width: 100%;
          height: 100%;
        }

        .ant-spin-dot-holder {
          color: var(--theme-text-color-default);
        }
      }
    }
  }

  .folderItem {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 42px;
    padding-left: 10px;
    border-radius: 4px;
    border: 1px solid transparent;

    &:hover {
      background-color: var(--theme-menu-color-bg-hover);
      border-color: var(--theme-basic-color-lighter);
    }

    .expandIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      font-size: 12px;
      flex-shrink: 0;
      border-radius: 1px;

      &:hover {
        background-color: var(--theme-text-button-color-hover);
      }
    }
  }

  .emptyFolder {
    display: flex;
    align-items: center;
    height: 40px;
    padding-left: 30px;
    border-radius: 4px;

    .emptyFolderText {
      padding-left: 24px;
      color: var(--theme-text-color-secondary);
    }
  }

  .emptyList {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icon {
      width: 200px;
      color: var(--theme-basic-color-primary);
    }

    .emptyTitle {
      margin-top: 8px;
      color: var(--theme-basic-color-primary);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
    }

    .emptySubTitle {
      color: var(--theme-text-color-secondary);
      margin-top: 8px;
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
    }
  }

  .folderIcon {
    width: 18px;
    margin-left: 10px;
  }

  .spaceIcon {
    color: var(--theme-text-color-default);
    margin-left: 10px;
    width: 18px;
  }

  .fileIcon {
    width: 20px;
    height: 20px;
  }

  .fileItem {
    height: 42px;
    padding-left: 42px;
    margin: 0;
    display: flex;
    align-items: center;

    &:hover {
      background-color: var(--theme-menu-color-bg-hover);
      border-color: var(--theme-basic-color-lighter);
    }
  }

  .itemName {
    margin-left: 8px;
    font-size: 14px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    position: relative;
    top: -1px;
  }

  // 网格视图
  .gridView {
    width: 100%;
    height: 100%;
  }

  .gridItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;
    cursor: pointer;
    border-radius: 4px;
    width: 100%;
    min-width: 0;

    &:hover {
      background-color: var(--theme-menu-color-bg-hover);
    }

    .folderIcon {
      margin: 0;
      margin-bottom: 8px;
      width: 48px;
      flex-shrink: 0;
    }

    .fileIcon {
      width: 48px;
      flex-shrink: 0;
    }

    .itemName {
      text-align: center;
      margin: 0;
      margin-top: 8px;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 12px;
      line-height: 16px;
    }
  }
}
