// 搜索区域
.searchContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.searchIcon {
  width: 18px;
  height: 18px;
  background-color: var(--theme-basic-color-lighter);
  border-radius: 2px;
}

.searchWrapper {
  width: 168px;
  position: relative;

  :global(.ant-input-outlined) {
    color: var(--theme-text-color-default);
    border: 1px solid var(--theme-basic-color-black);
    background-color: var(--theme-basic-color-bg-default);
    box-shadow: 0 2px 8px 0 var(--input-border-shadow);

    &:focus {
      border: 1px solid var(--theme-basic-color-primary);
      box-shadow: 0 2px 8px 0 var(--input-border-shadow);
    }
  }

  .inputSearchIcon {
    width: 18px;
    height: 18px;
  }

  .inputCloseIcon {
    width: 24px;
    height: 24px;
    color: var(--theme-text-color-secondary);
    cursor: pointer;
  }
}

.resultContainer {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--theme-basic-color-bg-default);
  border: 1px solid var(--theme-box-shadow-color-level8);
  border-radius: 2px;
  box-shadow: 0 8px 18px 0 var(--theme-box-shadow-color-level6);
  z-index: 1000;
}

.searchResults {
  padding: 4px 0;

  .item {
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 1;
    padding: 0 16px;
    cursor: pointer;
    color: var(--theme-text-color-default);

    &:hover {
      background-color: var(--theme-text-button-color-hover);
    }

    .folderIcon {
      width: 18px;
    }

    .spaceIcon {
      width: 18px;
      color: var(--theme-text-color-default);
      flex-shrink: 0;
    }

    .fileName {
      font-size: 13px;
      margin-left: 8px;
      color: var(--theme-basic-color-primary);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.noResults {
  display: flex;
  align-items: center;
  height: 40px;
  padding-left: 27px;
  color: var(--theme-basic-color-black);
  font-size: 12px;
  font-weight: 400;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  font-size: 16px;
}
