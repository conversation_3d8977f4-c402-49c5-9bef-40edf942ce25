// 侧边栏
.sideMenu {
  width: 170px;
  border-right: 1px solid var(--theme-basic-color-lighter);
  padding: 12px;
  overflow-y: auto;
  background-color: var(--theme-layout-color-bg-new-page);
  color: var(--theme-text-color-default);
}

.header {
  margin-left: 12px;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.subTitle {
  height: 16px;
  font-size: 10px;
  margin-left: 12px;
  color: var(--theme-basic-color-black);
  font-weight: 400;
}

.desktopSpace {
  margin-top: 24px;
}

.menuItem {
  height: 36px;
  padding: 0 12px;
  line-height: 1;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  border-radius: 2px;

  svg {
    margin-right: 8px;
  }

  &:hover {
    background-color: var(--theme-basic-color-lighter);
  }

  &.active {
    background-color: var(--theme-basic-color-light);
  }

  .anticon {
    font-size: 16px;
    color: inherit;
  }
}
