import { message, Modal } from 'antd';
import { useState } from 'react';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import { fm2 } from '@/modules/Locale';
import { getBaseName } from '@/utils/file';

import { Content } from './Content';
import { SideMenu } from './SideMenu/index';
import css from './style.less';

export interface Options {
  /** 目标位置guid */
  locationGuid?: string;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  type?: 'create' | 'move' | 'shortcut';
  source: {
    /** 待操作文件 name */
    name: string;
    /** 待操作文件 guid */
    fileGuid: string;
    /** 待操作文件父级guid */
    parentGuid: string;
    isAdmin?: boolean;
    role?: string;
  }[];
}

export const FilePathPicker = (options: Options) => {
  const { locationGuid = 'desktop', onOk, type = 'create', source } = options;
  if (source.length === 0) throw new Error('原文件不能为空');

  const _filename =
    source.length > 1
      ? `${fm2('FilePathPicker.originalName')} ${fm2('FilePathPicker.copy')}`
      : `${getBaseName(source[0].name)} ${fm2('FilePathPicker.copy')}`;

  let modalInstance: any;

  function ModalContent() {
    const [curLocationGuid, setCurLocationGuid] = useState(locationGuid);
    const [menuId, setMenuId] = useState<string>(type === 'create' ? 'desktop' : 'used');
    const [isManual, setIsManual] = useState(false);

    // 创建副本
    async function createDuplicate(locationGuid: string, folderName: string) {
      const params = {
        files: source.map((item) => {
          return {
            guid: item.fileGuid,
            name: `${getBaseName(item.name)} ${fm2('FilePathPicker.copy')}`,
          };
        }),
        folder: locationGuid,
      };
      const [err, res] = await to(fileApi.duplicateBatch(params));
      if (res?.status !== 200) return message.error(err?.data?.msg);

      message.success(fm2('FilePathPicker.createSuccess', { folderName }));
      onOk?.();
    }

    // 移动到
    async function moveFile(locationGuid: string, folderName: string) {
      const params = {
        entries: source.map((item) => {
          return {
            to: locationGuid,
            fileGuid: item.fileGuid,
          };
        }),
      };
      const [err, res] = await to(fileApi.move(params));
      if (res?.status !== 204) return message.error(err?.data?.msg);

      message.success(fm2('FilePathPicker.moveSuccess', { folderName }));
      onOk?.();
    }

    return (
      <div className={css.modalContent}>
        <SideMenu
          curMenuId={menuId}
          type={type}
          onSideMenuChange={(menuId, isManual) => {
            setMenuId(menuId);
            setIsManual(isManual);
          }}
        />
        <Content
          filename={_filename}
          isAdmin={source[0].isAdmin}
          isManual={isManual}
          locationGuid={locationGuid}
          role={source[0].role}
          sideMenuId={menuId}
          sourceFileParentGuid={source[0].parentGuid === 'Desktop' ? 'desktop' : source[0].parentGuid}
          type={type}
          onCancel={() => {
            modalInstance.destroy();
          }}
          onLocationChange={(locationGuid) => {
            setCurLocationGuid(locationGuid);
          }}
          onOk={(folderName) => {
            const _locationGuid = curLocationGuid === 'desktop' ? 'Desktop' : curLocationGuid;
            const _folderName = curLocationGuid === 'desktop' ? fm2('FilePathPicker.desktop') : folderName;
            if (type === 'create') {
              createDuplicate(_locationGuid, _folderName);
            } else if (type === 'shortcut') {
              console.log('调创建快捷方式接口，并关闭窗口执行回调');
              console.log(_locationGuid, _folderName);
            } else {
              moveFile(_locationGuid, _folderName);
            }
            modalInstance.destroy();
          }}
          onSideMenuChange={(menuId, isManual = false) => {
            setMenuId(menuId);
            setIsManual(isManual);
          }}
        />
      </div>
    );
  }

  modalInstance = Modal.info({
    width: 893,
    icon: null,
    footer: null,
    centered: true,
    closable: false,
    className: css.filePathPicker,
    content: <ModalContent />,
  });
};
