import { Modal } from 'antd';

import { ModalContent } from './ModalContent';

interface ModalProps {
  onOk: (url: string) => void;
  title?: string;
  type: 'upload' | 'modify';
  logoUrl?: string;
}

export function UploadLogo(props: ModalProps) {
  const { title } = props;
  // 创建Modal实例
  let modalInstance: any = null;

  // 关闭弹窗的函数
  function closeModal() {
    if (modalInstance) {
      modalInstance.destroy();
    }
  }

  // 修改onOk回调，确保关闭Modal
  const enhancedProps = {
    ...props,
    onClose: closeModal,
    onOk: (url: string) => {
      props.onOk(url);
      closeModal();
    },
  };

  // 使用Modal.info创建弹窗
  modalInstance = Modal.info({
    title,
    width: 400,
    content: <ModalContent {...enhancedProps} />,
    className: 'upload-logo-modal',
    icon: null,
    footer: null,
    centered: true,
    closable: true,
    maskClosable: true,
    onCancel: closeModal,
  });
}
