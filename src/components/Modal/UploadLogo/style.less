.container {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  color: var(--theme-text-color-medium);
  font-weight: 500;
  line-height: 24px;
}

.btn {
  gap: 6px;

  :global(.ant-btn-icon) {
    line-height: 1;
    font-size: 0;
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 14px;

  .logoWrap {
    width: 156px;
    height: 156px;
    background: url('@/assets/images/common/<EMAIL>') 100% 100% no-repeat;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .tip {
    margin-top: 11px;
    color: var(--theme-text-color-secondary);
    text-align: center;
    font-size: 10px;
    font-weight: 400;
    line-height: 16px;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 14px;
}
