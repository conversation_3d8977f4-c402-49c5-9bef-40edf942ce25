import { Button, Form, Input, message } from 'antd';
import React, { useEffect } from 'react';
import { history } from 'umi';

import { verifyFilePassword } from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { useFormatMessage } from '@/modules/Locale';

import styles from './PasswordInput.less';

const fm = useFormatMessage;

interface PasswordInputProps {
  shareUserName: string;
  autoFillPassword?: boolean;
  fileGuid: string;
  fileType: string;
  pathname: string;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  autoFillPassword = false,
  shareUserName,
  fileGuid,
  fileType,
  pathname,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (!autoFillPassword) return;

    const lastPathSegment = decodeURIComponent(pathname.split('/').pop() || '');
    const extractedPassword = lastPathSegment.slice(-6);

    if (extractedPassword && extractedPassword.length === 6) {
      form.setFieldsValue({ password: extractedPassword });
      form.submit();
    }
  }, [form, autoFillPassword, pathname]);

  const handleSubmit = async (values: { password: string }) => {
    if (!values.password) return;

    if (!fileGuid || !fileType) {
      console.error(fm('FilePasswordInput.invalidGuidOrType'));
      return;
    }

    const [err, res] = await catchApiResult(verifyFilePassword(fileGuid, values.password));
    if (err) {
      console.error(err);
      message.error(err?.data?.msg);
      return;
    }
    if (res?.status === 200 && res.data.verified) {
      history.replace(`/${fileType}/${fileGuid}`);
      window.location.reload();
    }
  };

  return (
    <div className={styles.passwordInputLayouts}>
      <div className={styles.encryptedFileAccess}>
        <p className={styles.userShare}>{fm('FilePasswordInput.encryptedFileShared', { name: shareUserName })}</p>
        <div className={styles.passwordContainer}>
          <div className={styles.horizontalLine} />
          <p className={styles.passwordTips}>{fm('FilePasswordInput.encryptedFilePasswordTip')}</p>
          <div className={styles.horizontalLine} />
        </div>
        <Form form={form} layout="inline" style={{ height: 84 }} onFinish={handleSubmit}>
          <Form.Item
            name="password"
            rules={[{ required: true, message: fm('FilePasswordInput.PasswordRequired') }]}
            style={{ margin: '0 10px 0 16px' }}
          >
            <Input.Password
              placeholder={fm('FilePasswordInput.encryptedFilePasswordPlaceholder')}
              size="large"
              style={{ width: 220 }}
              visibilityToggle={false}
            />
          </Form.Item>

          <Form.Item>
            <Button htmlType="submit" size="large" style={{ width: 96 }} type="primary">
              {fm('FilePasswordInput.confirm')}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default PasswordInput;
