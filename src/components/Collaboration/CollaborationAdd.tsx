import { UserOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import { Avatar, Collapse, Input, message, Switch, Tabs, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  deleteAdmin,
  deleteDepAdmin,
  getOrgDepartment,
  getOrgDepartmentUser,
  getSearchUser,
  setAdmin,
  setDepAdmin,
} from '@/api/Collaboration';
import { ReactComponent as ArrowTop } from '@/assets/images/svg/arrowTop.svg';
import { ReactComponent as DarkArrowRight } from '@/assets/images/svg/darkArrowRight.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { ReactComponent as TopLevel } from '@/assets/images/svg/topLevel.svg';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import deleteConfirm from '../fileList/components/deleteConfirm';
import { getMergerAccount, getMergerRoles } from './components';
import DropdownMenuItemAdmin from './components/DropdownMenuItemAdmin';
import DropdownMenuItemRole from './components/DropdownMenuItemRole';
import type { DataItem } from './types';
import { ChangeRolePermission, recentContact } from './utils';
interface CollaborationAddProps {
  addAdminsOrRoles: string;
  guid: string;
  canExit: boolean;
  canManageCollaborator: boolean;
}
export const CollaborationAdd: React.FC<CollaborationAddProps> = ({
  addAdminsOrRoles,
  guid,
  canExit,
  canManageCollaborator,
}) => {
  const [recentData, setRecentData] = useState<DataItem[]>([]);
  const [currentDepartment, setCurrentDepartment] = useState<DataItem[]>([]);
  const [subdepartments, setSubdepartments] = useState<DataItem[]>([]);
  const [departmentDataMap, setDepartmentDataMap] = useState<Record<string, DataItem[]>>({});

  const [inputValue, setInputValue] = useState<string>('');
  const [switchCheckedValid, setSwitchCheckedValid] = useState<boolean>(true);
  const [clickDepId, setClickDepId] = useState<number | string>(1);
  const meId = useMeStore((state) => state.me.id);
  const [searchResult, setSearchResult] = useState<DataItem[]>([]);
  const [tabKeyValue, setTabKeyValue] = useState<string>('recently');
  //首次进入 调最近联系人，addAdminsOrRoles区别是协作者还是管理者
  const getRecent = useCallback(async () => {
    const dataRecent = await recentContact();
    const mergerFn = addAdminsOrRoles === 'admins' ? getMergerAccount : getMergerRoles;
    mergerFn(dataRecent, guid, setRecentData);
  }, [addAdminsOrRoles, guid]);
  //获取组织结构树并对应的权限回显示
  const getDepartment = (id: number) => {
    if (departmentDataMap[id]) return;
    getOrgDepartment(id).then((res) => {
      const currentDept = res.data.currentDepartment;
      const subDepts = res.data.subdepartments;
      getOrgDepartmentUser(currentDept.id, { perPage: 500, page: 1 }).then((userRes) => {
        const users = userRes.data.users || [];
        const updatedList = [...subDepts, ...users];
        if (id === 1) {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount([currentDept], guid, setCurrentDepartment);
            getMergerAccount(updatedList, guid, setSubdepartments);
          } else {
            getMergerRoles([currentDept], guid, setCurrentDepartment);
            getMergerRoles(updatedList, guid, setSubdepartments);
          }
        } else {
          setClickDepId(id);
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount(updatedList, guid, (data) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [id]: data,
              }));
            });
          } else {
            getMergerRoles(updatedList, guid, (data) => {
              setDepartmentDataMap((prev) => ({ ...prev, [id]: data }));
            });
          }
        }
      });
    });
  };
  const onChangeTab = (key: string) => {
    setTabKeyValue(key);
    if (key === 'recently') {
      getRecent();
    }
    if (key === 'department') {
      getDepartment(1);
    }
  };
  const handleCollapseTwo = (key: string) => {
    const id = Array.isArray(key) ? key[0] : key;
    getDepartment(id);
  };
  const updatePermissions = (item?: DataItem) => {
    const mergerFn = addAdminsOrRoles === 'admins' ? getMergerAccount : getMergerRoles;
    if (item?.department || item?.user) {
      mergerFn(searchResult, guid, setSearchResult);
    } else if (tabKeyValue === 'recently') {
      mergerFn(recentData, guid, setRecentData);
    } else if (tabKeyValue === 'department') {
      if (item?.id === 1) {
        mergerFn(currentDepartment, guid, setCurrentDepartment);
      } else {
        mergerFn(subdepartments, guid, setSubdepartments);
        const data = departmentDataMap[clickDepId];
        if (data) {
          mergerFn(data, guid, (updatedData) => {
            setDepartmentDataMap((prev) => ({
              ...prev,
              [clickDepId]: updatedData,
            }));
          });
        }
      }
    }
  };
  const dropdownChangeAdmin = async (itemData: DataItem, info?: { key: string }, type?: string) => {
    if (info?.key === 'remove') {
      const isDepartment = type === 'department' || itemData.department;
      const id = itemData.id || (isDepartment ? (itemData.department?.id as number) : (itemData.user?.id as number));
      //删除
      const deleteConfig = {
        i18nText: {
          title: fm2('ShareCollaboration.confirmRemoveCollaborator'),
          content: fm2('ShareCollaboration.removeCollaborator', { name: itemData.name }),
          okText: fm2('ShareCollaboration.confirmRemove'),
          cancelText: fm2('ShareCollaboration.cancel'),
          success: fm2('ShareCollaboration.success'),
          error: fm2('ShareCollaboration.failed'),
        },
        data: { guid, id, name: itemData.name },
        callback: () => updatePermissions(itemData),
      };
      if (isDepartment) {
        deleteConfirm({ ...deleteConfig, api: () => deleteDepAdmin(guid, id) });
      } else {
        deleteConfirm({ ...deleteConfig, api: () => deleteAdmin(guid, id) });
      }
    }
    //添加
    if (!itemData.isAdmin) {
      const isDepartment = type === 'department' || itemData.department;
      const id = itemData.id || (isDepartment ? (itemData.department?.id as number) : (itemData.user?.id as number));
      const res = isDepartment
        ? await setDepAdmin(guid, id, { needNotice: switchCheckedValid })
        : await setAdmin(guid, id, { needNotice: switchCheckedValid });
      if (res?.status === 204) {
        updatePermissions(itemData);
        message.success(fm2('ShareCollaboration.addSuccess'));
      }
    }
  };
  const dropdownChangeRole = async (info: { key: string }, data: DataItem, type: string) => {
    const res = await ChangeRolePermission(guid, switchCheckedValid, info.key, data, type, canExit, meId ?? -1);
    if ((res && res.status === 204) || (res && res.status === 200)) {
      updatePermissions(data);
      if (info.key === 'remove') {
        message.success(fm2('ShareCollaboration.deleteSuccess'));
      } else {
        if (data.role) {
          message.success(fm2('ShareCollaboration.modifySuccess'));
        } else {
          message.success(fm2('ShareCollaboration.addSuccess'));
        }
      }
    }
  };
  const debouncedSearch = useRef(
    debounce((value) => {
      if (value.trim() !== '') {
        getSearchUser({
          limit: 100,
          keyword: value,
          filter: { user: { includeRecentContact: true, includeTeamMember: true }, department: {}, group: {} },
          fetchFileRoleByFileGuid: guid,
        }).then((res) => {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount(res.data?.results, guid, setSearchResult);
          } else {
            getMergerRoles(res.data?.results, guid, setSearchResult);
          }
        });
      }
    }, 300),
  ).current;
  //组织结构第归树部门
  const renderCollapseItems = (subdepartments: DataItem[]) => {
    return (
      <div>
        {subdepartments.length > 0 ? (
          subdepartments.map((item: DataItem) =>
            item?.handoverMenu ? (
              <div key={item.id} className="listItem">
                <div className="itemLeft">
                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                  <div className="itemRight">
                    <div className="itemName">{item.name}</div>
                    <Tooltip placement="top" title={item.email}>
                      <div className="emailText">{item.email}</div>
                    </Tooltip>
                  </div>
                </div>
                {addAdminsOrRoles === 'admins' ? (
                  <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                ) : (
                  <DropdownMenuItemRole
                    canManageCollaborator={canManageCollaborator}
                    data={item}
                    dropdownChangeRole={dropdownChangeRole}
                  />
                )}
              </div>
            ) : (
              <div key={item.id}>
                <Collapse
                  accordion
                  collapsible="icon"
                  expandIcon={({ isActive }) => <span>{isActive ? <DarkArrowRight /> : <ArrowTop />}</span>}
                  ghost={true}
                  items={[
                    {
                      key: item.id,
                      label: (
                        <div className="listItem">
                          <div className="itemLeft">
                            <Avatar icon={<Organization />} size={28} />
                            <div className="itemRight">
                              <div className="itemNameDep">{item.name}</div>
                            </div>
                          </div>
                          {addAdminsOrRoles === 'admins' ? (
                            <DropdownMenuItemAdmin
                              dropdownChangeAdmin={dropdownChangeAdmin}
                              itemData={item}
                              type={'department'}
                            />
                          ) : (
                            <DropdownMenuItemRole
                              canManageCollaborator={canManageCollaborator}
                              data={item}
                              dropdownChangeRole={dropdownChangeRole}
                              type={'department'}
                            />
                          )}
                        </div>
                      ),
                      children: departmentDataMap[item.id] ? renderCollapseItems(departmentDataMap[item.id]) : null,
                    },
                  ]}
                  onChange={(keys) => {
                    keys.forEach((key) => handleCollapseTwo(key));
                  }}
                />
              </div>
            ),
          )
        ) : (
          <div className="noDataDepartment">{fm2('ShareCollaboration.noChildDepartment')}</div>
        )}
      </div>
    );
  };
  const tabItems: TabsProps['items'] = [
    {
      key: 'recently',
      label: fm2('ShareCollaboration.recent'),
      children: (
        <div className="tabboxMaxH">
          {recentData.map((item: DataItem) => {
            return (
              <div key={item.id} className="listItem">
                <div className="itemLeft">
                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                  <div className="itemRight">
                    <div className="itemName">{item.name}</div>
                    <Tooltip placement="top" title={item.email}>
                      <div className="emailText">{item.email}</div>
                    </Tooltip>
                  </div>
                </div>
                {addAdminsOrRoles === 'admins' ? (
                  <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                ) : (
                  <DropdownMenuItemRole
                    canManageCollaborator={canManageCollaborator}
                    data={item}
                    dropdownChangeRole={dropdownChangeRole}
                  />
                )}
              </div>
            );
          })}
        </div>
      ),
    },
    {
      key: 'department',
      label: fm2('ShareCollaboration.organization'),
      children: (
        <Collapse
          className="CollapseDepartment"
          collapsible="icon"
          defaultActiveKey={['1']}
          expandIcon={({ isActive }) => (
            <span className="ant-collapse-arrow">
              <DarkArrowRight rotate={isActive ? 0 : 90} />
            </span>
          )}
          ghost={true}
          items={[
            {
              key: '1',
              label: (
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar icon={<TopLevel />} size={28} />
                    <div className="itemRight">
                      <div className="itemNameDep">{currentDepartment[0]?.name}</div>
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin
                      dropdownChangeAdmin={dropdownChangeAdmin}
                      itemData={currentDepartment[0]}
                      type={'department'}
                    />
                  ) : (
                    <DropdownMenuItemRole
                      canManageCollaborator={canManageCollaborator}
                      data={currentDepartment[0]}
                      dropdownChangeRole={dropdownChangeRole}
                      type={'department'}
                    />
                  )}
                </div>
              ),
              children: renderCollapseItems(subdepartments),
            },
          ]}
        />
      ),
    },
  ];
  useEffect(() => {
    debouncedSearch(inputValue);
    return () => debouncedSearch.cancel();
  }, [inputValue, debouncedSearch]);
  useEffect(() => {
    getRecent();
  }, [getRecent]);
  return (
    <div className="collaborationAdd">
      <div className="modalBodyInput">
        <Input
          placeholder={`${fm2('ShareCollaboration.clickHereToSearchAndAdd')}${
            addAdminsOrRoles === 'admins' ? fm2('ShareCollaboration.admin') : fm2('ShareCollaboration.coauthor')
          }`}
          prefix={<Search />}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onClear={() => setInputValue('')}
        />
      </div>
      {inputValue ? (
        <>
          <Tabs
            defaultActiveKey="1"
            items={[{ key: '1', label: fm2('ShareCollaboration.searchResult'), children: null }]}
            size="small"
          />
          <div className="searchBox">
            {searchResult.map((item) => (
              <div key={item.user?.id || item.department?.id}>
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar
                      icon={item.department ? <Organization /> : <UserOutlined />}
                      size={28}
                      src={item.user?.avatar}
                    />
                    <div className="itemRight">
                      <div className="itemName">{item.user?.name || item.department?.name}</div>
                      {item.user?.email && (
                        <Tooltip placement="top" title={item.user?.email}>
                          <div>{item.user?.email}</div>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                  ) : (
                    <DropdownMenuItemRole
                      canManageCollaborator={canManageCollaborator}
                      data={item}
                      dropdownChangeRole={dropdownChangeRole}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <Tabs className="itemTabs" defaultActiveKey="recently" items={tabItems} size="small" onChange={onChangeTab} />
      )}
      <div className="addBottom">
        <Switch
          checked={switchCheckedValid}
          className="switchMr"
          size="small"
          onChange={(checked) => {
            setSwitchCheckedValid(checked);
          }}
        />
        <span>{fm2('ShareCollaboration.sendNotificationToTheOther')}</span>
      </div>
    </div>
  );
};
