import { message } from 'antd';

import {
  addCollaboration,
  addCollaborationDepartment,
  deleteCollaboration,
  deleteCollaborationDepartment,
  getCollaborationDetail,
  getCollaborationList,
  getRecentContact,
  SharePasswordStatus,
  updateCollaboration,
  updateCollaborationDepartment,
  updateExpireTime,
  updateParentCollaboration,
  updateShareStatus,
} from '@/api/Collaboration';
import { fm2 } from '@/modules/Locale';

import type { DataItem, RoleItem } from '../types';

// 获取团队空间/文件夹/文件详情
export const getCollaborationDataInfo = async (guid: string) => {
  try {
    const res = await getCollaborationDetail(guid);
    const data = res.data;
    let shareUrl = '';
    let currentGuid = null;
    if (data?.isShortcut) {
      // 如果是快捷方式就要改变guid和url
      data.url = data.shortcutSource?.url || '';
      data.guid = data.shortcutSource?.guid || null;
      shareUrl = data.shortcutSource?.url || '';
      currentGuid = data.shortcutSource?.guid || null;
    } else {
      shareUrl = data.url || '';
      currentGuid = data.guid || null;
    }
    const copyUrl = `${new URL(shareUrl, window.location.href).href}/《${data.name}》`;
    return {
      data,
      copyUrl,
      currentGuid,
    };
  } catch (error) {
    console.error(error);
    return null;
  }
};
//获取协作者和管理者列表
export const fetchCollaborationInfo = async (guid: string) => {
  try {
    const result = await getCollaborationList(guid, {
      includeInherited: false,
      includeAdmin: true,
    });

    return {
      admins: result.data.admins,
      roles: result.data.roles,
    };
  } catch (error) {
    return { admins: [], roles: [] };
  }
};

// 分享开关的关闭和开启
export const handleUpdateShareStatus = async (guid: string, shareMode: string) => {
  await updateShareStatus(guid, { shareMode });
};
//获取或者更新密码
export const updateSharePasswordAndGet = async (
  guid: string,
  passwordProtected: boolean,
  reset: boolean,
): Promise<string | null> => {
  try {
    const res = await SharePasswordStatus(guid, { passwordProtected, reset });
    return res.data?.password || null;
  } catch (error) {
    return null;
  }
};
//更新有效期事件
export const updateShareExpire = async (guid: string, value: number): Promise<number> => {
  const res = await updateExpireTime(guid, { shareModeExpireDuration: value ? value * 86400 : 0 });
  return res.data?.shareModeExpiredAt || 0;
};
// 查询继承过来的人员或者组织
export const getInheritedCollaborators = async (guid: string) => {
  const res = await getCollaborationList(guid, { includeInherited: true, includeAdmin: false, includeSelf: false });
  return res.data?.roles || [];
};
//修改父级协作者权限，并更新父级继承者权限
export const updateParentCollaborationlist = async (guid: string, key: string) => {
  const updateResult = await updateParentCollaboration(guid, { parentRole: key });
  if (updateResult.status === 200) {
    const listResult = await getCollaborationList(guid, {
      includeInherited: true,
      includeAdmin: false,
      includeSelf: false,
    });
    return {
      parentRole: updateResult.data.parentRole,
      rolesList: listResult.data.roles,
    };
  }
  return {
    parentRole: null,
    rolesList: [],
  };
};
// 修改或者删除协作者（人员和部门）
interface ErrorResponse {
  data?: {
    code?: number;
  };
}
export const handleCollaborationAction = async (action: string, guid: string, item: RoleItem, callback: () => void) => {
  try {
    if (action === 'remove') {
      if (item?.departmentId) {
        await deleteCollaborationDepartment(guid, item.id);
      } else {
        await deleteCollaboration(guid, item.id);
      }
    } else {
      if (item.departmentId) {
        await updateCollaborationDepartment(guid, item.id, { role: action, needNotice: true });
      } else {
        await updateCollaboration(guid, item.id, { role: action });
      }
    }
    callback();
    return true;
  } catch (error) {
    const err = error as ErrorResponse;
    if (err?.data?.code === 60056) {
      message.warning(fm2('FilePasswordInput.cannotRemoveFileManager'));
    }
  }
};

//查询最近联系人列表
export const recentContact = async (): Promise<DataItem[]> => {
  try {
    const res = await getRecentContact();
    return res.data?.results || [];
  } catch (error) {
    return [];
  }
};

//添加页面对协作者的操作
const getTargetId = (data: DataItem): number => {
  return data.id ?? data?.department?.id ?? data?.user?.id ?? -1;
};
export const ChangeRolePermission = async (
  guid: string,
  switchCheckedValid: boolean,
  key: string,
  data: DataItem,
  type: string,
  canExit: boolean,
  meId: number,
) => {
  //协作者（最近联系，组织，搜索）三个列表删除 或者 添加修改协作者 都是用此函数
  const isRemove = key === 'remove';
  let isDepartment = (!data.avatar && !data?.handoverMenu) || data?.departmentId || type === 'department';
  if (data?.type === 'department') {
    isDepartment = true;
  } else if (data?.type === 'user') {
    isDepartment = false;
  }
  let res;
  if (isRemove) {
    if (meId === data.id && !canExit) {
      // 判断本人是否可以退出协作
      message.warning(fm2('ShareCollaboration.noPermissionCollaboration'));
      return false;
    }
    res = isDepartment
      ? await deleteCollaborationDepartment(guid, getTargetId(data))
      : await deleteCollaboration(guid, getTargetId(data));
  } else {
    res = isDepartment
      ? await addCollaborationDepartment(guid, getTargetId(data), { role: key, needNotice: switchCheckedValid })
      : //协作的修改和添加用不同接口
        data.role
        ? await updateCollaboration(guid, getTargetId(data), { role: key, needNotice: switchCheckedValid })
        : await addCollaboration(guid, { role: key, needNotice: switchCheckedValid, userId: getTargetId(data) });
  }
  return res;
};
