import type { ShareMode } from '../components/SelectOptions';

export interface CollaborationShareProps {
  visible: boolean;
  guid: string;
  onCancel: () => void;
  enterType?: string;
}

export interface CollaborationData {
  shareModeExpiredAt?: number;
  userId: number | string;
  password: string;
  passwordProtected: boolean;
  type: string;
  isSpace: boolean | undefined;
  role?: string;
  url?: string;
  guid?: string;
  parentId?: number;
  parentRole?: string;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  isFolder?: boolean;
  shareMode?: ShareMode;
  name?: string;
  permissionsAndReasons?: {
    canChangeShareMode: { value: boolean; reason: string };
    canExit?: { value: boolean; reason: string };
    canManageCollaborator?: { value: boolean; reason: string };
    canManageAdmin?: { value: boolean; reason: string };
  };
  isShortcut?: boolean;
  shortcutSource?: {
    url: string;
    guid: string;
  };
  shareModeExpireDuration: number;
}
export interface Role {
  role: string;
  email: string;
  departmentId?: number;
  id: number;
  avatar?: string;
  name?: string;
}
export interface UseCollaborationDetailResult {
  data: CollaborationData | null;
  constUrl: string;
  fullUrl: string;
  setFullUrl: (url: string) => void;
}

export interface RoleItem {
  id: number;
  name?: string;
  email?: string;
  avatar?: string;
  role: string;
  departmentId?: number;
  isInherited?: boolean;
}
interface UserItem {
  id: number;
  name: string;
  avatar?: string;
  email?: string;
  isAdmin?: boolean;
  departmentId?: string;
  handoverMenu?: boolean;
  role?: string;
  type?: 'user' | 'department';
  isInherited?: boolean;
  user?: UserItem;
  department?: DepartmentItem;
}

interface DepartmentItem {
  id: number;
  name: string;
  avatar?: string;
  email?: string;
  isAdmin?: boolean;
  departmentId?: string;
  handoverMenu?: boolean;
  role?: string;
  isInherited?: boolean;
  type?: 'user' | 'department';
  department?: DepartmentItem;
  user?: UserItem;
}

export type DataItem = UserItem | DepartmentItem;

interface SearchResultUser {
  user: {
    id: number;
    name: string;
    avatar?: string;
    email?: string;
  };
  department?: never;
}
interface SearchResultDepartment {
  department: {
    id: number;
    name: string;
  };
  user?: never;
}
export type SearchResultItem = SearchResultUser | SearchResultDepartment;
