import { UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Input, Select, Switch, Tooltip } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as Link16 } from '@/assets/images/svg/link16.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { ReactComponent as QuestionMark } from '@/assets/images/svg/questionMark.svg';
import { ReactComponent as Refresh } from '@/assets/images/svg/refresh.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { fm2 } from '@/modules/Locale';

import { copyLinkUrl, optionsDays, startCountdown, stopCountdown } from './components';
import { optionsClose, optionsOpen } from './components/SelectOptions';
import { usePermissions } from './hooks/usePermission';
import type { CollaborationData, Role } from './types';
import { handleUpdateShareStatus, updateShareExpire, updateSharePasswordAndGet } from './utils';
interface CollaborationProps {
  data: CollaborationData;
  guid: string;
  shareDisabled: boolean;
  CollaborationAdmins: Role[];
  CollaborationRoles: Role[];
  constUrl: string;
  setFullUrl: (url: string) => void;
  setListOpen: (open: boolean) => void;
  setAddOpen: (open: boolean) => void;
  setEnterAdd: (open: boolean) => void;
  setLinkCopyBtn: (btn: boolean) => void;
  setAddAdminsOrRoles: (type: 'admins' | 'roles') => void;
}
export const Collaboration: React.FC<CollaborationProps> = ({
  data,
  guid,
  constUrl,
  shareDisabled,
  CollaborationAdmins,
  CollaborationRoles,
  setFullUrl,
  setListOpen,
  setAddOpen,
  setEnterAdd,
  setLinkCopyBtn,
  setAddAdminsOrRoles,
}) => {
  type OptionType = { value: string; label: React.ReactNode };
  const [switchCheckedLink, setSwitchCheckedLink] = useState<boolean>(false);
  const [switchCheckedPassword, setSwitchCheckedPassword] = useState<boolean>(false);
  const [passwordText, setPasswordText] = useState<string>('');

  const [switchCheckedValid, setSwitchCheckedValid] = useState<boolean>(false);
  const [fullUrlPassword, setFullUrlPassword] = useState<string>('');

  const [shareStatusValue, setShareStatusValue] = useState<string>('');
  const [options, setOptions] = useState<OptionType[]>([]);

  const [defaultValueDays, setDefaultValueDays] = useState<number | null>(null);

  const [remainingTime, setRemainingTime] = useState<string>('00:00:00');
  const [countdownIntervalId, setCountdownIntervalId] = useState<number | null>(null);

  const {
    shareStatus,
    shareStatusVal,
    passwordStatus,
    password,
    shareTimeStatus,
    shareModeExpireDuration,
    shareModeExpiredAt,
  } = usePermissions(data);
  const handleCopyLink = () => {
    copyLinkUrl(fullUrlPassword);
  };
  const searchEnterAdd = () => {
    if (countdownIntervalId) {
      stopCountdown(countdownIntervalId);
    }
    setListOpen(true);
    setAddOpen(true);
    setAddAdminsOrRoles('roles');
    setEnterAdd(true);
  };
  const handleListOpen = () => {
    if (countdownIntervalId) {
      stopCountdown(countdownIntervalId);
    }
    setListOpen(true);
    setAddOpen(false);
    setEnterAdd(false);
  };
  //分享开关
  const checkedShareLink = async (checked: boolean) => {
    setSwitchCheckedLink(checked);
    setLinkCopyBtn(!checked);
    if (!checked) {
      await handleUpdateShareStatus(guid, 'private');
      setFullUrl(constUrl);
    } else {
      await handleUpdateShareStatus(guid, shareStatusValue);
      if (passwordText) {
        setFullUrlPassword(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${passwordText}`);
        setFullUrl(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${passwordText}`);
      } else {
        setFullUrlPassword(constUrl);
        setFullUrl(constUrl);
      }
    }
    setSwitchCheckedValid(false);
  };
  //下拉改变分享值
  const changeStatus = (value: string) => {
    setShareStatusValue(value);
    handleUpdateShareStatus(guid, value);
  };
  //分享密码开关
  const passwordButton = async (checked: boolean) => {
    const newPassword = await updateSharePasswordAndGet(guid, checked, false);
    if (newPassword !== null) {
      setPasswordText(newPassword);
    }
    if (checked) {
      setOptions(optionsOpen());
      setFullUrlPassword(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${newPassword}`);
      setFullUrl(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${newPassword}`);
    } else {
      setOptions(optionsClose());
      setFullUrlPassword(constUrl);
      setFullUrl(constUrl);
    }
    // setShareStatusValue('enterprise_readonly');//切换改不改变值，默认值设置不？？？
    setSwitchCheckedPassword(checked);
  };
  //更换密码
  const handleChangePassword = async () => {
    if (!shareDisabled) {
      const newPassword = await updateSharePasswordAndGet(guid, true, true);
      if (newPassword !== null) {
        setPasswordText(newPassword);
        setFullUrlPassword(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${newPassword}`);
        setFullUrl(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${newPassword}`);
      }
    }
  };
  //下拉改变 有效期
  const headleChangeDays = async (value: number) => {
    setDefaultValueDays(value);
    const shareModeExpiredAt = await updateShareExpire(guid, value);
    stopCountdown(countdownIntervalId);
    const id = startCountdown(shareModeExpiredAt, setRemainingTime);
    setCountdownIntervalId(id);
  };

  // 首次进来倒计时
  const getDays = useCallback(() => {
    const expireDays = shareModeExpireDuration ? Math.floor(shareModeExpireDuration / 86400) : 0;
    setDefaultValueDays(expireDays ?? null);
    const id = startCountdown(shareModeExpiredAt, setRemainingTime);
    setCountdownIntervalId(id);
  }, [shareModeExpireDuration, shareModeExpiredAt]);
  //分享有效期开关
  const changeValid = (checked: boolean): void => {
    stopCountdown(countdownIntervalId);
    setSwitchCheckedValid(checked);
    const days = checked ? 30 : 0;
    setDefaultValueDays(days);
    headleChangeDays(days);
  };
  useEffect(() => {
    //分享是否开启
    if (shareStatus) {
      setSwitchCheckedLink(true);
      setLinkCopyBtn(false);
      setShareStatusValue(shareStatusVal ?? '');
    } else {
      setSwitchCheckedLink(false);
      setLinkCopyBtn(true);
      setShareStatusValue('enterprise_readonly');
    }
    //分享密码是否开启(是否开启密码保护，跟有没有密码没关系)
    if (passwordStatus) {
      setSwitchCheckedPassword(true);
      setOptions(optionsOpen());
      if (password) {
        setPasswordText(password);
        setFullUrlPassword(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${password}`);
        setFullUrl(`${constUrl} ${fm2('ShareCollaboration.accessPassword')} ${password}`);
      } else {
        setFullUrlPassword(constUrl);
        setFullUrl(constUrl);
      }
    } else {
      setSwitchCheckedPassword(false);
      setPasswordText('');
      setOptions(optionsClose());
      setFullUrlPassword(constUrl);
      setFullUrl(constUrl);
    }
    if (shareTimeStatus) {
      setSwitchCheckedValid(true);
      getDays();
    } else {
      setSwitchCheckedValid(false);
    }
  }, [
    shareStatus,
    constUrl,
    setFullUrl,
    setLinkCopyBtn,
    password,
    shareStatusVal,
    passwordStatus,
    shareTimeStatus,
    getDays,
  ]);

  useEffect(() => {
    return () => {
      if (countdownIntervalId) {
        stopCountdown(countdownIntervalId);
      }
    };
  }, [countdownIntervalId]);
  return (
    <div>
      <div className="modalBodyInput">
        <Input placeholder={fm2('ShareCollaboration.searchAddCollab')} prefix={<Search />} onFocus={searchEnterAdd} />
      </div>
      <div className="userList" onClick={handleListOpen}>
        <div className="userItem">
          <div className="collaborator">{fm2('ShareCollaboration.coauthor')}</div>
          {CollaborationRoles.length > 0 ? (
            CollaborationRoles.slice(0, 5).map((item) => {
              return <Avatar key={item.id} icon={<UserOutlined />} size={24} src={item.avatar} />;
            })
          ) : (
            <div className="noDataCollaborator">
              <NoDataIcon />
              <span>{fm2('ShareCollaboration.noCollaborator')}</span>
            </div>
          )}
          <div className="manager">{fm2('ShareCollaboration.admin')}</div>
          {CollaborationAdmins.slice(0, 3).map((item) => {
            return <Avatar key={item.id} icon={<UserOutlined />} size={24} src={item.avatar} />;
          })}
        </div>
        <div className="userRightIcon">
          <ArrowRight />
        </div>
      </div>
      <div className="linkShareTitle">{fm2('ShareCollaboration.linkShare')}</div>
      <div className="switchBox">
        {/* 分享开关 */}
        {shareDisabled ? (
          <Tooltip title={fm2('ShareCollaboration.notSupportShare')}>
            <span>
              <Switch checked={switchCheckedLink} className="switchMr" disabled={shareDisabled} size="small" />
            </span>
          </Tooltip>
        ) : (
          <Switch
            checked={switchCheckedLink}
            className="switchMr"
            disabled={shareDisabled}
            size="small"
            onChange={(checked) => {
              checkedShareLink(checked);
            }}
          />
        )}

        <span> {switchCheckedLink ? fm2('ShareCollaboration.open') : fm2('ShareCollaboration.close')}</span>
      </div>
      {switchCheckedLink && (
        <div className="switchOpenShare">
          <div className="linkCopy">
            <div className="linkText">
              <Select
                disabled={shareDisabled}
                options={options}
                style={{ width: '100%' }}
                value={shareStatusValue}
                onChange={(value) => {
                  changeStatus(value);
                }}
              />
            </div>
            <Button icon={<Link16 />} onClick={handleCopyLink}>
              {fm2('ShareCollaboration.copyLink')}
              {switchCheckedPassword ? fm2('ShareCollaboration.linkWithPassword') : ''}
            </Button>
          </div>
          <div className="linkSetting">
            <div className="linkPassword">
              {/* 密码开关 */}
              <Switch
                checked={switchCheckedPassword}
                className="switchMr"
                disabled={shareDisabled}
                size="small"
                onChange={(checked) => {
                  passwordButton(checked);
                }}
              />
              {switchCheckedPassword ? (
                <>
                  <div>{fm2('ShareCollaboration.linkPassword')}</div>
                  <div className="linkPasswordInput">{passwordText}</div>
                  <Refresh />
                  <div className="changePassword" onClick={handleChangePassword}>
                    {fm2('ShareCollaboration.changePassword')}
                  </div>
                </>
              ) : (
                <div>{fm2('ShareCollaboration.needPassword')}</div>
              )}
            </div>
            <div className="linkValid">
              <div className="linkValidLeft">
                <Switch
                  checked={switchCheckedValid}
                  className="switchMr"
                  disabled={shareDisabled}
                  size="small"
                  onChange={(checked) => {
                    changeValid(checked);
                  }}
                />
                <span>{fm2('ShareCollaboration.linkExpiration')}</span>
                {switchCheckedValid ? (
                  <>
                    <Select
                      className="validSelect"
                      disabled={shareDisabled}
                      options={optionsDays()}
                      size="small"
                      value={defaultValueDays ?? undefined}
                      onChange={(value) => {
                        headleChangeDays(value);
                      }}
                    />
                    <Tooltip
                      placement="top"
                      title={
                        <div className="tooltipShare">
                          <div>{fm2('ShareCollaboration.switchOff')}</div>
                          <div>{fm2('ShareCollaboration.switchOn')}</div>
                        </div>
                      }
                    >
                      <QuestionMark />
                    </Tooltip>
                  </>
                ) : (
                  <span>({fm2('ShareCollaboration.expirationClose')})</span>
                )}
              </div>
              {switchCheckedValid && (
                <div>
                  {fm2('ShareCollaboration.remaining')}
                  {remainingTime}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
