import { CaretDownOutlined, CheckOutlined, PlusCircleOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Collapse, Dropdown, message, Space, Tooltip } from 'antd';
import { useState } from 'react';

import { deleteAdmin, deleteDepAdmin } from '@/api/Collaboration';
import listEmpty from '@/assets/images/common/listEmpty.png';
import { ReactComponent as BlueBottom } from '@/assets/images/svg/blueBottom.svg';
import { ReactComponent as BlueRight } from '@/assets/images/svg/blueRight.svg';
import { ReactComponent as CollaborativeArrow } from '@/assets/images/svg/collaborativeArrow.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as SuperiorOrganization } from '@/assets/images/svg/superiorOrganization.svg';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import { isDisabledAdmin, itemRole, itemsPrenet } from './components';
import type { CollaborationData, Role, RoleItem } from './types';
import { getInheritedCollaborators, handleCollaborationAction, updateParentCollaborationlist } from './utils';
interface CollaborationListProps {
  data: CollaborationData;
  canExit: boolean;
  canManageAdmin: boolean;
  canManageCollaborator: boolean;
  guid: string;
  parentId?: number;
  parentRole: string;
  getUserList: () => void;
  CollaborationAdmins: Role[];
  CollaborationRoles: Role[];
  setAddOpen: (open: boolean) => void;
  setAddAdminsOrRoles: (type: string) => void;
}
export const CollaborationList: React.FC<CollaborationListProps> = ({
  data,
  canExit,
  canManageAdmin,
  canManageCollaborator,
  guid,
  parentId,
  parentRole,
  getUserList,
  CollaborationAdmins,
  CollaborationRoles,
  setAddOpen,
  setAddAdminsOrRoles,
}) => {
  const [parentRoleStatus, setParentRoleStatus] = useState(parentRole);
  const [collapseOpenList, setCollapseOpenList] = useState<string[]>([]);
  const [inheritedUserList, setInheritedUserList] = useState<Role[]>([]);
  const meId = useMeStore((state) => state.me.id);
  interface ItemOption {
    key: string;
    label: string;
  }
  const getItemName = (itemsObj: ItemOption[], item: string | number): string | number => {
    const foundItem = itemsObj.find((e) => e.key === item);
    return foundItem ? foundItem.label : '';
  };
  //修改继承过来的权限
  const dropdownChangePerent = async (info: { key: string }) => {
    const result = await updateParentCollaborationlist(guid, info.key);
    const { parentRole, rolesList } = result;
    setParentRoleStatus(parentRole);
    setInheritedUserList(rolesList);
    getUserList();
    message.success(fm2('ShareCollaboration.modifySuccess'));
  };
  //折叠板展开
  const handleCollapse = async (key: string) => {
    if (!collapseOpenList.includes('superior')) {
      const roles = await getInheritedCollaborators(guid);
      setInheritedUserList(roles);
    }
    setCollapseOpenList(() => {
      if (collapseOpenList.includes(key)) {
        return collapseOpenList.filter((item) => item !== key);
      } else {
        return [...collapseOpenList, key];
      }
    });
  };
  const editCollaboration = (key: string, item: RoleItem) => {
    if (meId === item.id) {
      // 判断本人是否可以退出协作
      if (!canExit) {
        return key === 'remove';
      }
    }
  };
  // 修改或者删除协作者（人员和部门）
  const dropdownChange = async (info: { key: string }, item: RoleItem) => {
    const result = await handleCollaborationAction(info.key, guid, item, () => getUserList());
    if (result) {
      if (info.key === 'remove') {
        message.success(fm2('ShareCollaboration.deleteSuccess'));
      } else {
        message.success(fm2('ShareCollaboration.modifySuccess'));
      }
    }
  };
  // 管理者的操作（只有删除）
  const deleteAdminDep = (item: RoleItem) => {
    deleteConfirm({
      i18nText: {
        title: fm2('ShareCollaboration.confirmRemoveCollaborator'),
        content: fm2('ShareCollaboration.removeCollaborator', { name: item.name }),
        okText: fm2('ShareCollaboration.confirmRemove'),
        cancelText: fm2('ShareCollaboration.cancel'),
        success: fm2('ShareCollaboration.success'),
        error: fm2('ShareCollaboration.failed'),
      },
      data: { guid, id: item.id, name: item.name },
      api: () => (item.departmentId ? deleteDepAdmin(guid, item.id) : deleteAdmin(guid, item.id)),
      callback: () => getUserList(),
    });
  };
  const handleEnterAddRoles = () => {
    setAddOpen(true);
    setAddAdminsOrRoles('roles');
  };
  const handleEnterAddAdmins = () => {
    setAddOpen(true);
    setAddAdminsOrRoles('admins');
  };
  return (
    <div className="collaborationList">
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.coauthor')}</div>
        {canManageCollaborator && (
          <div className="listTitleRight" onClick={handleEnterAddRoles}>
            <PlusCircleOutlined />
            {fm2('ShareCollaboration.addCoauthor')}
          </div>
        )}
      </div>
      <div className="listBox marginBottom16">
        {CollaborationRoles.length > 0 || parentId ? (
          <>
            {parentId && (
              <Collapse
                activeKey={collapseOpenList}
                bordered={false}
                expandIcon={() => {
                  return '';
                }}
                items={[
                  {
                    key: 'superior',
                    label: (
                      <div className="listItem">
                        <div className="itemLeft">
                          <SuperiorOrganization />
                          <span className="superOrg">{fm2('ShareCollaboration.parentCoauthor')}</span>
                          <div className="collapseBtn" onClick={() => handleCollapse('superior')}>
                            {collapseOpenList.includes('superior') ? (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.collapse')}</span>
                                <BlueBottom />
                              </div>
                            ) : (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.expand')}</span>
                                <BlueRight />
                              </div>
                            )}
                          </div>
                        </div>
                        <Dropdown
                          menu={{
                            items: itemsPrenet().map((roleItem) => ({
                              key: roleItem.key,
                              label: (
                                <div className="dropdownItem">
                                  <div>{roleItem.label}</div>
                                  {parentRoleStatus === roleItem.key && <CheckOutlined />}
                                </div>
                              ),
                            })),
                            onClick: (info) => dropdownChangePerent(info),
                          }}
                          placement="bottomRight"
                          trigger={['click']}
                        >
                          <Space className="spaceCollaboration">
                            <Button
                              disabled={!canManageCollaborator}
                              icon={<CollaborativeArrow />}
                              iconPosition="end"
                              size="small"
                              type="text"
                            >
                              {getItemName(itemsPrenet(), parentRoleStatus)}
                            </Button>
                          </Space>
                        </Dropdown>
                      </div>
                    ),
                    children: (
                      <div>
                        {inheritedUserList.length > 0 ? (
                          inheritedUserList.map((item) => (
                            <div key={item.id} className="listItem">
                              <div className="itemLeft">
                                {item.departmentId ? (
                                  <>
                                    <Organization />
                                    <div className="ellipsis">{item.name}</div>
                                  </>
                                ) : (
                                  <>
                                    <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                                    <div className="itemRight">
                                      <div className="itemName">{item.name}</div>
                                      <Tooltip placement="top" title={item.email}>
                                        <div className="emailText">{item.email}</div>
                                      </Tooltip>
                                    </div>
                                  </>
                                )}
                              </div>
                              <Space className="spaceCollaboration">
                                <span className="noClick">{getItemName(itemsPrenet(), item.role)}</span>
                              </Space>
                            </div>
                          ))
                        ) : (
                          <div className="noDataInherited">{fm2('ShareCollaboration.noParentCollaborator')}</div>
                        )}
                      </div>
                    ),
                  },
                ]}
              />
            )}
            {CollaborationRoles.map((item) => {
              return (
                <div key={item.id} className="listItem">
                  <div className="itemLeft">
                    {item.departmentId ? (
                      <>
                        <Organization />
                        <div className="ellipsis">{item.name}</div>
                      </>
                    ) : (
                      <>
                        <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                        <div className="itemRight">
                          <div className="itemName">{item.name}</div>
                          <Tooltip placement="top" title={item.email}>
                            <div className="emailText">{item.email}</div>
                          </Tooltip>
                        </div>
                      </>
                    )}
                  </div>
                  <Dropdown
                    menu={{
                      items: itemRole().map((roleItem) => ({
                        key: roleItem.key,
                        danger: roleItem.danger,
                        label: (
                          <div className="dropdownItem">
                            <div>{roleItem.label}</div>
                            {item.role === roleItem.key && <CheckOutlined />}
                          </div>
                        ),
                        disabled: editCollaboration(roleItem.key, item),
                      })),
                      onClick: (info) => dropdownChange(info, item),
                    }}
                    placement="bottomRight"
                    trigger={['click']}
                  >
                    <Space className="spaceCollaboration">
                      <Button
                        disabled={!canManageCollaborator}
                        icon={<CaretDownOutlined />}
                        iconPosition="end"
                        size="small"
                        type="text"
                      >
                        {getItemName(itemRole(), item.role)}
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              );
            })}
          </>
        ) : (
          <div className="listEmpty">
            <img alt="" src={listEmpty} />
          </div>
        )}
      </div>
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.admin')}</div>
        {canManageAdmin && (
          <div className="listTitleRight" onClick={handleEnterAddAdmins}>
            <PlusCircleOutlined />
            {fm2('ShareCollaboration.addManager')}
          </div>
        )}
      </div>
      <div className="listBox">
        {CollaborationAdmins.map((item) => {
          return (
            <div key={item.id} className="listItem">
              <div className="itemLeft">
                {item.departmentId ? (
                  <>
                    <Organization />
                    <div className="ellipsis">{item.name}</div>
                  </>
                ) : (
                  <>
                    <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                    <div className="itemRight">
                      <div className="itemName">{item.name}</div>
                      <Tooltip placement="top" title={item.email}>
                        <div className="emailText">{item.email}</div>
                      </Tooltip>
                    </div>
                  </>
                )}
              </div>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'merger',
                      label: (
                        <div className="dropdownItem">
                          <div>{fm2('ShareCollaboration.admin')}</div>
                          <CheckOutlined />
                        </div>
                      ),
                    },
                    {
                      key: 'removeMerger',
                      danger: true,
                      label: <div>{fm2('ShareCollaboration.removeManager')}</div>,
                      onClick: () => deleteAdminDep(item),
                    },
                  ],
                }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space className="spaceCollaboration">
                  <Button
                    disabled={isDisabledAdmin(data, item, CollaborationAdmins)}
                    icon={<CaretDownOutlined />}
                    iconPosition="end"
                    size="small"
                    type="text"
                  >
                    {fm2('ShareCollaboration.admin')}
                  </Button>
                </Space>
              </Dropdown>
            </div>
          );
        })}
      </div>
    </div>
  );
};
