import './index.less';

import { Modal } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

import { Collaboration } from './Collaboration';
import { CollaborationAdd } from './CollaborationAdd';
import { CollaborationList } from './CollaborationList';
import { IndexFooter } from './components/IndexFooter';
import { IndexTitle } from './components/IndexTitle';
import { usePermissions } from './hooks/usePermission';
import { Share } from './Share';
import type { CollaborationData, CollaborationShareProps, Role } from './types';
import { fetchCollaborationInfo, getCollaborationDataInfo } from './utils';
const CollaborationShare: React.FC<CollaborationShareProps> = ({ visible, guid, onCancel, enterType }) => {
  const [data, setData] = useState<CollaborationData | null>(null);
  const [linkCopyBtn, setLinkCopyBtn] = useState(true);
  const [fullUrl, setFullUrl] = useState('');
  const [constUrl, setConstUrl] = useState('');
  const currentGuidRef = useRef<string>(guid);
  const [enterAdd, setEnterAdd] = useState(false);
  const [CollaborationAdmins, setCollaborationAdmins] = useState<Role[]>([]);
  const [CollaborationRoles, setCollaborationRoles] = useState<Role[]>([]);
  const { roleCheckable, shareDisabled, canManageCollaborator, canExit, canManageAdmin } = usePermissions(data);

  const getUserList = useCallback(async () => {
    const { admins, roles } = await fetchCollaborationInfo(currentGuidRef.current);
    setCollaborationAdmins(admins);
    setCollaborationRoles(roles);
  }, []);
  const getDataInfo = useCallback(
    async (guid: string) => {
      const result = await getCollaborationDataInfo(guid);
      if (result) {
        const { data, copyUrl, currentGuid } = result;
        setConstUrl(copyUrl);
        setFullUrl(copyUrl);
        setData(data);
        if (currentGuid) {
          currentGuidRef.current = currentGuid;
          getUserList();
        }
      }
    },
    [getUserList],
  );

  const [listOpen, setListOpen] = useState(false);
  const [addOpen, setAddOpen] = useState(false);
  const [addAdminsOrRoles, setAddAdminsOrRoles] = useState('');
  const hanleBack = () => {
    if (addOpen) {
      if (enterAdd) {
        setEnterAdd(false);
        setListOpen(false);
      } else {
        setListOpen(true);
        getUserList();
      }
      setAddOpen(false);
    } else {
      setListOpen(false);
      setEnterAdd(false);
      getDataInfo(currentGuidRef.current);
    }
  };
  useEffect(() => {
    if (visible) {
      getDataInfo(guid);
    }
    setListOpen(false);
    setAddOpen(false);
    if (enterType === 'setting') {
      setListOpen(true);
      setAddOpen(true);
      setAddAdminsOrRoles('roles');
    }
  }, [visible, enterType, getDataInfo, guid]);
  return (
    <Modal
      centered
      destroyOnClose
      className="collaborationModal"
      closable={roleCheckable}
      footer={[
        <IndexFooter
          key="footer"
          addOpen={addOpen}
          data={data}
          fullUrl={fullUrl}
          linkCopyBtn={linkCopyBtn}
          listOpen={listOpen}
        />,
      ]}
      open={visible}
      title={
        <IndexTitle
          addAdminsOrRoles={addAdminsOrRoles}
          addOpen={addOpen}
          data={data}
          enterType={enterType}
          hanleBack={hanleBack}
          listOpen={listOpen}
        />
      }
      width={480}
      onCancel={onCancel}
    >
      {roleCheckable ? (
        !listOpen ? (
          <Collaboration
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            constUrl={constUrl}
            data={data ?? ({} as CollaborationData)}
            guid={currentGuidRef.current}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
            setEnterAdd={setEnterAdd}
            setFullUrl={setFullUrl}
            setLinkCopyBtn={setLinkCopyBtn}
            setListOpen={setListOpen}
            shareDisabled={shareDisabled}
          />
        ) : !addOpen ? (
          <CollaborationList
            canExit={canExit}
            canManageAdmin={canManageAdmin}
            canManageCollaborator={canManageCollaborator}
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            data={data ?? ({} as CollaborationData)}
            getUserList={getUserList}
            guid={currentGuidRef.current}
            parentId={data?.parentId === 0 ? undefined : data?.parentId}
            parentRole={data?.parentRole || ''}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
          />
        ) : (
          <CollaborationAdd
            addAdminsOrRoles={addAdminsOrRoles}
            canExit={canExit}
            canManageCollaborator={canManageCollaborator}
            guid={currentGuidRef.current}
          />
        )
      ) : (
        <Share url={data?.url || ''} />
      )}
    </Modal>
  );
};

export default CollaborationShare;
