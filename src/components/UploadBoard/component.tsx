import 'overlayscrollbars/overlayscrollbars.css';

import { Button, message, Modal, Progress, Tooltip } from 'antd';
import { OverlayScrollbarsComponent } from 'overlayscrollbars-react';
import type { MouseEvent } from 'react';
import { useEffect, useState } from 'react';
import { flushSync } from 'react-dom';
import { history } from 'umi';

import { ReactComponent as CancelSvg } from '@/assets/images/upload/cancel.svg';
import { ReactComponent as CancelUploadSvg } from '@/assets/images/upload/cancelUpload.svg';
import { ReactComponent as CloseSvg } from '@/assets/images/upload/close.svg';
import { ReactComponent as DownSvg } from '@/assets/images/upload/down.svg';
import { ReactComponent as ErrorSvg } from '@/assets/images/upload/error.svg';
import { ReactComponent as FolderSvg } from '@/assets/images/upload/folder.svg';
import { ReactComponent as RetrySvg } from '@/assets/images/upload/retry.svg';
import { ReactComponent as RightSvg } from '@/assets/images/upload/right.svg';
import { ReactComponent as SuccessSvg } from '@/assets/images/upload/success.svg';
import { ReactComponent as UploadingSvg } from '@/assets/images/upload/uploading.svg';
import { fm } from '@/modules/Locale';
import type { StatusType, UploadItem } from '@/store/Upload';
import { useUploadStore } from '@/store/Upload';
import { getFileIcon } from '@/utils/file';
import { fileToGotoSdkPage, mimeToExtension } from '@/utils/mime_to_extension';

import styles from './index.less';
interface ListResult {
  icon?: JSX.Element;
  status: StatusType;
  title?: string;
}
export default function UploadBoard() {
  const i18nText = {
    uploadSuccessTitle: fm('UploadBoard.uploadSuccessTitle'),
    uploadingTitle: fm('UploadBoard.uploadingTitle'),
    uploadFailTitle: fm('UploadBoard.uploadFailTitle'),
    uploadCancelTitle: fm('UploadBoard.uploadCancelTitle'),
    uploadConfirmCancelTitle: fm('UploadBoard.uploadConfirmCancelTitle'),
    uploadConfirmCancelContent: fm('UploadBoard.uploadConfirmCancelContent'),
    uploadConfirmCancelOkText: fm('UploadBoard.uploadConfirmCancelOkText'),
    uploadConfirmCancelCancelText: fm('UploadBoard.uploadConfirmCancelCancelText'),
    uploadFailMessageText: fm('UploadBoard.uploadFailMessageText'),
    uploadFailTipTitleText: fm('UploadBoard.uploadFailTipTitleText'),
    uploadCopyFailMessageText: fm('UploadBoard.uploadCopyFailMessageText'),
    uploadCopySuccessText: fm('UploadBoard.uploadCopySuccessText'),
    uploadCheckFailMessageText: fm('UploadBoard.uploadCheckFailMessageText'),
    uploadRetryText: fm('UploadBoard.uploadRetryText'),
    uploadOpenFolderText: fm('UploadBoard.uploadOpenFolderText'),
    uploadStatusTipCancelText: fm('UploadBoard.uploadStatusTipCancelText'),
    uploadExpTipRetractText: fm('UploadBoard.uploadExpTipRetractText'), //收起
    uploadExpTipExpandText: fm('UploadBoard.uploadExpTipExpandText'), //展开
    uploadExpTipRetractErrorMessageText: fm('UploadBoard.uploadExpTipRetractErrorMessageText'), //收起错误信息
  };
  const [showContent, setShowContent] = useState(true);
  const { isShowBoard, uploadList, setIsShowBoard, setUploadList } = useUploadStore((state) => state);
  const [open, setOpen] = useState(isShowBoard);
  const [openFail, setOpenFail] = useState(false);
  const [fileList, setFileList] = useState<UploadItem[]>(uploadList || []);
  const [failList, setFailList] = useState<UploadItem[]>([]);
  const [uploadingList, setUploadingList] = useState<UploadItem[]>([]);
  const [cancelList, setCancelList] = useState<UploadItem[]>([]);
  const [successNum, setSuccessNum] = useState(0);
  const [listResultStatus, setListResultStatus] = useState<StatusType>('uploading');
  const statusMessage: any = {
    success: {
      title: i18nText.uploadSuccessTitle,
      icon: <SuccessSvg className={styles.titleStatusIcon} />,
    },
    uploading: { title: i18nText.uploadingTitle, icon: <UploadingSvg /> },
    fail: { title: i18nText.uploadFailTitle, icon: <ErrorSvg className={styles.titleStatusIcon} /> },
    cancel: {
      title: `${cancelList.length}${i18nText.uploadCancelTitle}`,
      icon: <SuccessSvg className={styles.titleStatusIcon} />,
    },
  };
  const failTipNum = fm('UploadBoard.failTipNum', { number: failList.length });
  const failTipTitle = fm('UploadBoard.failTipTitle', { number: failList.length });
  const [listResult, setListResult] = useState<ListResult>(statusMessage[listResultStatus]);

  useEffect(() => {
    if (fileList.length) {
      // 成功列表
      const successList = fileList.filter((item) => item.done && item.status === 'success');
      const successNumTemp = successList.length;
      setSuccessNum(successNumTemp);

      //失败列表
      const failList = fileList.filter((item) => item.done && item.status === 'fail');
      setFailList(failList);
      setOpenFail(!!failList.length);

      // 取消列表
      const cancelList = fileList.filter((item) => item.done && item.status === 'cancel');
      setCancelList(cancelList);
      statusMessage.cancel = {
        title: `${cancelList.length}${i18nText.uploadCancelTitle}`,
        icon: <SuccessSvg className={styles.titleStatusIcon} />,
      };

      //正在上传文件列表
      const uploadingList = fileList.filter((item) => item.status === 'uploading');
      setUploadingList(uploadingList);
      const listResultStatus = uploadingList.length
        ? 'uploading'
        : failList.length
          ? 'fail'
          : cancelList.length
            ? 'cancel'
            : 'success';
      setListResultStatus(listResultStatus);
      setListResult(statusMessage[listResultStatus]);
    }
  }, [fileList]);
  useEffect(() => {
    setOpen(isShowBoard);
  }, [isShowBoard]);
  useEffect(() => {
    // flushSync此方法是为了每次更新都会被检测到
    flushSync(() => {
      setFileList(uploadList || []);
    });
  }, [uploadList]);
  const goToCurrentFolderClick = (item: UploadItem, event: MouseEvent) => {
    event.stopPropagation();
    if (item.parentGuid === 'Desktop') {
      history.push('/desktop');
    } else {
      history.push(`/folder/${item.parentGuid}`);
    }
  };
  const { confirm } = Modal;
  const showConfirm = () => {
    confirm({
      className: 'closeUploadBoard',
      icon: null,
      centered: true,
      title: i18nText.uploadConfirmCancelTitle,
      content: i18nText.uploadConfirmCancelContent,
      okText: i18nText.uploadConfirmCancelOkText,
      cancelText: i18nText.uploadConfirmCancelCancelText,
      onOk: () => {
        if (uploadingList.length) {
          uploadingList.forEach((it) => {
            it.controller.abort();
          });
        }
        setIsShowBoard(false);
        setUploadList([]);
      },
    });
  };
  const closeConfirm = () => {
    setIsShowBoard(false);
  };
  const cancelSingleUpload = (item: UploadItem, event: MouseEvent) => {
    event.stopPropagation();

    if (item.controller) {
      item.controller.abort(); //取消上传
    }
    const list = uploadList;
    list[list.findIndex((it) => item.generateTempId === it.generateTempId)] = { ...item, done: true, status: 'cancel' };
    setUploadList([...list]);
  };

  //失败的全部重试
  const retry = () => {
    const list = uploadList;
    failList.forEach((item) => {
      list[list.findIndex((it) => item.generateTempId === it.generateTempId)] = { ...item, status: 'waiting' };
    });
    setUploadList([...list]);
  };

  const singleRetry = (item: UploadItem, event: MouseEvent) => {
    event.stopPropagation();
    //单个文件重新上传
    const list = uploadList;
    list[list.findIndex((it) => item.generateTempId === it.generateTempId)] = {
      ...item,
      controller: new AbortController(),
      status: 'waiting',
    };
    setUploadList([...list]);
  };

  const copyMessage = async () => {
    let failMessage = '';
    failList.forEach((item) => {
      failMessage += `<${item.name}>\n${i18nText.uploadFailMessageText}:${item.message}\n`;
    });
    await navigator.clipboard.writeText(failMessage);
    message.info(i18nText.uploadCopySuccessText);
  };

  const fileItemClick = (item: UploadItem) => {
    if (item.guid) {
      window.open(`/${fileToGotoSdkPage(item.type)}/${item.guid}`, '_blank');
    }
  };

  return (
    <div className={`${styles.styledPanelGroup} ${open ? styles.visible : styles.disvisible}`}>
      <div className={styles.panels}>
        <div className={styles.panelWrapper}>
          <section className={styles.header}>
            <div className={styles.left}>
              {listResult.icon}
              <span className={styles.listTitle}>{listResult.title}</span>

              <span className={styles.lightText}>
                （{listResultStatus === 'fail' ? failTipNum : `${successNum}/${fileList.length}`}）
              </span>
              <Tooltip
                style={{ display: 'flex' }}
                title={showContent ? i18nText.uploadExpTipRetractText : i18nText.uploadExpTipExpandText}
              >
                <Button
                  icon={showContent ? <DownSvg /> : <RightSvg />}
                  type="text"
                  onClick={() => setShowContent(!showContent)}
                />
              </Tooltip>
              {failList.length ? (
                <Button className={styles.checkBtn} type="text" onClick={() => setOpenFail(!openFail)}>
                  {!openFail ? i18nText.uploadCheckFailMessageText : i18nText.uploadExpTipRetractErrorMessageText}
                </Button>
              ) : null}
            </div>
            <div className={styles.right}>
              {failList.length ? <Button icon={<RetrySvg />} type="text" onClick={() => retry()} /> : null}
              <Button
                icon={<CloseSvg />}
                type="text"
                onClick={
                  fileList.some((item) => item.status === 'uploading' || item.status === 'waiting')
                    ? showConfirm
                    : closeConfirm
                }
              />
            </div>
          </section>
          <OverlayScrollbarsComponent
            className={`${styles.content} ${showContent ? styles.showContent : styles.noShowContent}`}
            options={{
              scrollbars: {
                autoHide: 'scroll', // 滚动时隐藏滚动条
                clickScroll: true, // 点击轨道滚动
              },
            }}
          >
            <div className={styles.scrollContent}>
              <div className={`${styles.failPop} ${openFail ? styles.visible : styles.disvisible}`}>
                <div className={styles.failTitle}>{failTipTitle}</div>
                <div className={styles.failList}>
                  {failList.map((item: any, index: number) => (
                    <div key={item.generateTempId || index} className={styles.failItem}>
                      <div className={styles.failItemTitle}>{`「${item.name}」`}</div>
                      <div className={styles.failItemContent}>
                        「{i18nText.uploadFailMessageText}: {item.message}」
                      </div>
                    </div>
                  ))}
                </div>
                <div className={styles.footer}>
                  <span>
                    <Button type="link" onClick={copyMessage}>
                      {i18nText.uploadCopyFailMessageText}
                    </Button>
                  </span>
                </div>
              </div>
              <div className={styles.list}>
                {fileList.map((item: any, index: number) => (
                  <div key={item.generateTempId || index} className={styles.item} onClick={() => fileItemClick(item)}>
                    <div className={styles.left}>
                      <img alt="" src={getFileIcon({ type: mimeToExtension(item.type) })} />
                      <div
                        className={`${styles.fileName} ${item.guid ? styles.haveUnderLine : styles.dontUnderLine} ${
                          item.status === 'cancel' ? styles.cancelText : ''
                        }`}
                      >
                        {item.name}
                      </div>
                    </div>
                    <div className={styles.right}>
                      <span className={styles.right1}>
                        <Tooltip placement="left" style={{ display: 'flex' }} title={i18nText.uploadOpenFolderText}>
                          <FolderSvg
                            className={`${styles.folder} ${styles.hoverShow} ${styles.itemFolderIcon}`}
                            onClick={(event) => goToCurrentFolderClick(item, event)}
                          />
                        </Tooltip>
                        <span className={styles.hoverHidden}>
                          {item.status === 'cancel' ? (
                            <span className={styles.cancelText}>{i18nText.uploadStatusTipCancelText}</span>
                          ) : null}
                        </span>
                      </span>
                      <div className={styles.right2}>
                        {item.status === 'uploading' || item.status === 'waiting' ? (
                          <span>
                            <span className={`${styles.hoverHidden} ${styles.myProgress}`}>
                              <Progress
                                percent={item.progress}
                                showInfo={false}
                                size={16}
                                strokeColor={'#5BA0E7'}
                                type="circle"
                              />
                            </span>
                            <Tooltip style={{ display: 'flex' }} title={i18nText.uploadConfirmCancelOkText}>
                              <Button
                                className={`${styles.hoverShow}`}
                                icon={<CancelUploadSvg />}
                                type="text"
                                onClick={(even) => cancelSingleUpload(item, even)}
                              />
                            </Tooltip>
                          </span>
                        ) : item.status === 'success' ? (
                          <SuccessSvg className={`${styles.itemStatusIcon}`} />
                        ) : (
                          <span>
                            {item.status === 'cancel' ? (
                              <CancelSvg className={`${styles.hoverHidden} ${styles.itemStatusIcon}`} />
                            ) : (
                              <ErrorSvg className={`${styles.hoverHidden} ${styles.itemStatusIcon}`} />
                            )}
                            <Tooltip style={{ display: 'flex' }} title={i18nText.uploadRetryText}>
                              <Button
                                className={`${styles.hoverShow}`}
                                icon={<RetrySvg />}
                                type="text"
                                onClick={(event) => singleRetry(item, event)}
                              />
                            </Tooltip>
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </OverlayScrollbarsComponent>
        </div>
      </div>
    </div>
  );
}
