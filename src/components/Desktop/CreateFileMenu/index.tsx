import { Popover, Tooltip } from 'antd';
import { useState } from 'react';

import { ReactComponent as ArrowSvg } from '@/assets/images/fileMenu/rightArrow.svg';
import { ReactComponent as TemplateSvg } from '@/assets/images/fileMenu/template.svg';
import type { FileItem } from '@/constants/fileList.config';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm } from '@/modules/Locale';

import FormMenu from '../FormMenu';
import styles from './index.less';

type CreateFileMenuProps = {
  fileMenuList: FileItem[][];
  onClose: (item: FileItem) => void;
  showTemplateModel: () => void;
};
const CreateFileMenu: React.FC<CreateFileMenuProps> = ({ fileMenuList, onClose, showTemplateModel }) => {
  const isOnline = useNetworkStatus();
  const [formOpen, setFormOpen] = useState(false);

  const itemClick = (item: FileItem) => {
    if (!isOnline) return;
    onClose(item);
  };

  const onCloseHandle = (item: FileItem) => {
    onClose(item);
    setFormOpen(false);
  };

  const siderMenuCreateTemplateText = fm('SiderMenu.siderMenuCreateTemplateText');
  const networkStatusTipText = fm('CreateFileMenu.networkStatusTipText');

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getFormContent = (formMenu: any[]) => {
    return <FormMenu formMenuList={formMenu} onClose={onCloseHandle} />;
  };

  const InnerItem = (item: FileItem, fileIndex: number, itemIndex: number) => {
    return (
      <Tooltip title={!isOnline && fileIndex === 1 && itemIndex === 2 ? networkStatusTipText : ''}>
        {item.children?.length ? (
          <Popover
            arrow={false}
            content={getFormContent(item.children)}
            open={formOpen}
            placement="rightTop"
            onOpenChange={setFormOpen}
          >
            <div className={`${styles.itemContainer}`} onClick={() => itemClick(item)}>
              <div className={styles.iconContainer}>
                <img src={item.src} />
              </div>
              <span className={styles.title}>{item.title}</span>
            </div>
          </Popover>
        ) : (
          <div
            className={`${styles.itemContainer}  ${
              !isOnline && fileIndex === 1 && itemIndex === 2 ? styles.itemDisabled : ''
            }`}
            onClick={() => itemClick(item)}
          >
            <div className={styles.iconContainer}>
              <img src={item.src} />
            </div>
            <span className={styles.title}>{item.title}</span>
          </div>
        )}
      </Tooltip>
    );
  };

  return (
    <div className={styles.fileMenu}>
      <section>
        {fileMenuList.map((fileItems, fileIndex) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={fileIndex} className={styles.groupContainer}>
            {fileItems
              .filter((it: FileItem) => it.hidden !== true)
              .map((item: FileItem, itemIndex) => (
                <div key={item.value}>{InnerItem(item, fileIndex, itemIndex)}</div>
              ))}
          </div>
        ))}
      </section>
      <div className={styles.createForm} onClick={showTemplateModel}>
        <div className={styles.wrapper}>
          <TemplateSvg />
          <span className={styles.formTitle}>{siderMenuCreateTemplateText}</span>
        </div>
        <ArrowSvg />
      </div>
    </div>
  );
};
export default CreateFileMenu;
