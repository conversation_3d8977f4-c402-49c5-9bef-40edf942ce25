/**
 * 登录表单功能，为登录页面或登录弹框提供登录的业务功能
 */
import { PlusOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Divider, Menu, Popover, Tooltip } from 'antd';
import Sider from 'antd/es/layout/Sider';
import { debounce } from 'lodash';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { history, useLocation, useParams } from 'umi';

import { ReactComponent as DesktopSvg } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as FavoritesSvg } from '@/assets/images/sidepanel/favorites.svg';
import { ReactComponent as RecentSvg } from '@/assets/images/sidepanel/recent.svg';
import { ReactComponent as ShareSvg } from '@/assets/images/sidepanel/share.svg';
import { ReactComponent as SilderLfSvg } from '@/assets/images/sidepanel/silderLf.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as TrashSvg } from '@/assets/images/sidepanel/trash.svg';
import { ReactComponent as UnionSvg } from '@/assets/images/sidepanel/Union.svg';
import CreateFileMenu from '@/components/Desktop/CreateFileMenu/index';
import { TemplateModal } from '@/components/TemplateModal';
import type { FileItem } from '@/constants/fileList.config';
import { FILE_LIST } from '@/constants/fileList.config';
import { useCreateFile } from '@/hooks/useCreateFile';
import { useCurrentPage } from '@/hooks/useCurrentPage';
import { RootType } from '@/model/File';
import type { SiderMenuItem } from '@/model/SiderMenuItem';
import { fm } from '@/modules/Locale';
import { useUserCheckPoint } from '@/service/Me';
import { useCurrentFolderSpaceInfo } from '@/store/CurrentFolderSpaceInfo';
import { useFileStore } from '@/store/File';
import { useSelectedMenuItemStore } from '@/store/Sider';

import styles from './index.less';

export type MenuItem = Required<MenuProps>['items'][number];
const getFirstPath = (pathname: string, rootType?: RootType) => {
  const parts = pathname.split('/');
  if (parts.length > 1) {
    const result = parts[1];
    if (result === 'folder') {
      if (rootType === RootType.Space) {
        return 'space';
      } else {
        return 'desktop';
      }
    }
    return result;
  } else {
    return '';
  }
};

function iskeyandlabel(value: MenuItem): value is SiderMenuItem {
  return (value as SiderMenuItem).label !== undefined && (value as SiderMenuItem).key !== undefined;
}

const SiderMenu: React.FC = () => {
  //国际化
  const siderMenuCreactText = fm('SiderMenu.siderMenuCreactText');
  const siderMenuRecentText = fm('SiderMenu.siderMenuRecentText');
  const siderMenuShareText = fm('SiderMenu.siderMenuShareText');
  const siderMenuFavoritesText = fm('SiderMenu.siderMenuFavoritesText');
  const siderMenuDesktopText = fm('SiderMenu.siderMenuDesktopText');
  const siderMenuSpaceText = fm('SiderMenu.siderMenuSpaceText');
  const siderMenuTrashText = fm('SiderMenu.siderMenuTrashText');
  const siderMenuBusinessText = fm('SiderMenu.siderMenuBusinessText');

  const rootType = useFileStore((state) => state.rootType);

  // 获取当前的Guid
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || 'Desktop';

  const items: MenuItem[] = [
    {
      key: 'recent',
      icon: <RecentSvg />,
      label: siderMenuRecentText,
    },
    {
      key: 'share',
      icon: <ShareSvg />,
      label: siderMenuShareText,
    },
    {
      key: 'favorites',
      icon: <FavoritesSvg />,
      label: siderMenuFavoritesText,
    },
    {
      type: 'divider',
    },
    { key: 'desktop', icon: <DesktopSvg />, label: siderMenuDesktopText },
    { key: 'space', icon: <SpaceSvg />, label: siderMenuSpaceText },
    {
      type: 'divider',
    },

    { key: 'trash', icon: <TrashSvg />, label: siderMenuTrashText },
  ];
  const [collapsed, setCollapsed] = useState(false);

  const sideMenuDataList: SiderMenuItem[] = items.filter(iskeyandlabel);
  const [isOpenTypePop, setIsOpenTypePop] = useState(false);
  const [isOpenTemplateModel, setIsOpenTemplateModel] = useState(false);
  const { createFile } = useCreateFile(guid);

  // 获取当前地址
  const location = useLocation();
  const pathName = location.pathname;
  const selectedKeys = getFirstPath(pathName, rootType);
  const setSelectedMenuItem = useSelectedMenuItemStore((state) => state.setSelectedMenuItem);
  const silderRef = useRef<HTMLDivElement>(null);
  const [showButton, setShowButton] = useState(false);

  const showTemplateModel = () => {
    setIsOpenTypePop(false);
    setIsOpenTemplateModel(true);
  };

  const { isNoFeaturePoint } = useUserCheckPoint();

  const getFileMenuList = useMemo(() => {
    const tempArr = FILE_LIST[0].map((item: FileItem) => {
      item.hidden = item.supportType && isNoFeaturePoint(item.supportType || '');
      return item;
    });
    const fileMenuList: FileItem[][] = FILE_LIST;
    fileMenuList[0] = tempArr; //数组第一个参数做权限处理
    return fileMenuList;
  }, [FILE_LIST, isNoFeaturePoint]);

  const content = (
    <CreateFileMenu
      fileMenuList={getFileMenuList}
      showTemplateModel={showTemplateModel}
      onClose={(item: FileItem) => {
        if (item?.disabled || item?.children?.length) {
          return;
        }
        setIsOpenTypePop(false);
        //开始创建文件
        createFile(item?.value || 'newdoc');
      }}
    />
  );

  const disableContent = (
    <div className={'disableContent'}>
      <div className={'disableContentTitle'}>{fm('SiderMenu.createDisablePopText')}</div>
    </div>
  );

  setSelectedMenuItem(
    sideMenuDataList.find((item) => item.key === selectedKeys) || {
      key: 'recent',
      label: siderMenuRecentText,
    },
  );

  // 鼠标移动事件处理函数
  const handleMouseMove = (event: MouseEvent) => {
    const topHeaderHeight: number = 48;
    silderRef.current?.style.setProperty('--silder--y', `${event.clientY - topHeaderHeight}px`);
  };

  /**
   * 控制收缩侧边栏的按钮
   */
  let timerId: any = null;
  const handleMouseenter = () => {
    timerId = setTimeout(() => {
      setShowButton(true);
    }, 500);
  };
  const handleMouseout = () => {
    if (timerId) {
      clearTimeout(timerId);
    }
    setShowButton(false);
  };

  // 使用 useDebounce 对 handleMouseenter 进行防抖处理，延迟时间为50 毫秒
  const debouncedMouseenter = debounce(handleMouseenter, 50);
  const debouncedMouseout = debounce(handleMouseout, 50);
  useEffect(() => {
    const element = silderRef.current;
    if (element) {
      // 为 ref 对应的 DOM 元素添加鼠标移动监听事件
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseenter', debouncedMouseenter);
      element.addEventListener('mouseleave', debouncedMouseout);
    }
    return () => {
      if (element) {
        // 组件卸载时，移除鼠标移动监听事件，避免内存泄漏
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseenter', debouncedMouseenter);
        element.removeEventListener('mouseleave', debouncedMouseout);
      }
    };
    //请求接口
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  //侧边栏点击事件
  const onClick: MenuProps['onClick'] = (e: MenuItem) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const path: any = e?.key;
    history.push(`/${path}` || '/');
  };

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpenTypePop(newOpen);
  };

  const goManagement = () => {
    window.open('/enterprise/members'); // 默认跳转通讯录
  };

  const handleCloseTemplate = () => {
    setIsOpenTemplateModel(false);
  };

  const folderSpaceInfo = useCurrentFolderSpaceInfo((state) => state.folderSpaceInfo);
  const { isFolderPage, isSpaceFolderPage } = useCurrentPage();
  const canNotCreateChildFile = useMemo(() => {
    return (isFolderPage() || isSpaceFolderPage()) && !folderSpaceInfo.permissionsAndReasons.canCreateChildFile.value;
  }, [folderSpaceInfo, isFolderPage, isSpaceFolderPage]);

  return (
    <Sider className={styles.siderStyle} width={collapsed ? 83 : 240}>
      <div className={styles.creatDiv}>
        <Popover
          arrow={false}
          content={content}
          open={isOpenTypePop}
          placement="bottomLeft"
          trigger="click"
          onOpenChange={handleOpenChange}
        >
          <Popover content={canNotCreateChildFile ? disableContent : ''} placement="right">
            <button
              className={`${styles.createBtn} driveSolidBtnClass`}
              disabled={canNotCreateChildFile}
              type="button"
              onClick={() => setIsOpenTypePop(!isOpenTypePop)}
            >
              {collapsed ? <PlusOutlined className={styles.plusIcon} /> : siderMenuCreactText}
            </button>
          </Popover>
        </Popover>
      </div>
      <div className={styles.menus}>
        <Menu
          defaultOpenKeys={['dashboard']}
          inlineCollapsed={collapsed}
          items={items}
          mode="inline"
          selectedKeys={[selectedKeys]}
          onClick={onClick}
        />
      </div>
      <div className={styles.footer}>
        <Divider />
        <div className={styles.footerDiv}>
          <Tooltip placement="right" title={collapsed ? siderMenuBusinessText : ''}>
            <div className={styles.business} onClick={goManagement}>
              <UnionSvg />
              {collapsed ? null : <span className={styles.title}>{siderMenuBusinessText}</span>}
            </div>
          </Tooltip>
        </div>
      </div>
      <div ref={silderRef} className={styles.rightSilder} />
      {showButton && silderRef.current
        ? ReactDOM.createPortal(
            <button
              className={`${styles.silderButton}  ${collapsed ? styles.rightSvg : ''}`}
              type="button"
              onClick={() => setCollapsed(!collapsed)}
            >
              <SilderLfSvg />
            </button>,
            silderRef.current,
          )
        : null}
      {isOpenTemplateModel && <TemplateModal closeTemplate={handleCloseTemplate} visible={isOpenTemplateModel} />}
    </Sider>
  );
};
export default React.memo(SiderMenu);
