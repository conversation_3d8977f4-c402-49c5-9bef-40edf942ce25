import type { FileItem } from '@/constants/fileList.config';

import styles from './index.less';

type FormMenuProps = {
  formMenuList: FileItem[];
  onClose: (item: FileItem) => void;
};
const FormMenu: React.FC<FormMenuProps> = ({ formMenuList, onClose }) => {
  const itemClick = (item: FileItem) => {
    onClose(item);
  };

  return (
    <div className={styles.formMenu}>
      {formMenuList.map((item: FileItem) => (
        <div key={item.value}>
          <div className={`${styles.itemContainer}`} onClick={() => itemClick(item)}>
            <div className={styles.iconContainer}>
              <img src={item.src} />
            </div>
            <span className={styles.title}>{item.title}</span>
          </div>
        </div>
      ))}
    </div>
  );
};
export default FormMenu;
