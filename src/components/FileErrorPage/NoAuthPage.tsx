import { history } from 'umi';

import img from '@/assets/images/svg/error-page-no-auth.svg';
import { useFormatMessage } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NoAuthPage = () => {
  const title = useFormatMessage('Error.noLogin');
  const subtitle = useFormatMessage('Error.noLoginDes');
  const btnContent = useFormatMessage('Error.loginText');

  return (
    <ErrorPageBase
      buttons={[
        {
          label: btnContent,
          onClick: () => {
            history.push('/login');
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NoAuthPage;
