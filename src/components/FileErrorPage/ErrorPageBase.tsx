import { Button } from 'antd';
import type { ButtonColorType, ButtonVariantType } from 'antd/es/button';
import classNames from 'classnames';
import React from 'react';

import styles from './ErrorPageBase.less';

interface ErrorPageBaseProps {
  imgSrc: string;
  title: string;
  subTitle: string;
  buttons?: {
    label: string;
    onClick: () => void;
    type?: 'primary' | 'plain' | 'link';
  }[];
}
//  todo 这里是配合全局样式更改
const buttonColorMap = {
  primary: 'primary',
  plain: 'default',
  link: 'link',
};

const buttonVariantMap = {
  primary: 'solid',
  plain: 'outlined',
  link: 'link',
};

const ErrorPageBase: React.FC<ErrorPageBaseProps> = (props) => {
  const { imgSrc, title, subTitle, buttons } = props;
  return (
    <div className={styles.errorPageLayouts}>
      <img src={imgSrc} />
      <h1>{title}</h1>
      <h2>{subTitle}</h2>
      {buttons?.map((button) => (
        <Button
          key={button.label}
          className={classNames({
            [styles.plainButton]: button.type === 'plain',
            [styles.linkButton]: button.type === 'link',
          })}
          color={buttonColorMap[button.type ?? 'primary'] as ButtonColorType}
          variant={buttonVariantMap[button.type ?? 'primary'] as ButtonVariantType}
          onClick={button.onClick}
        >
          {button.label}
        </Button>
      ))}
    </div>
  );
};

export default ErrorPageBase;
