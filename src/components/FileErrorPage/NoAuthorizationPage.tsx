import { history } from 'umi';

import img from '@/assets/images/svg/noAuthorization.svg';
import { useFormatMessage } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NoAuthorizationPage = () => {
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const switchAccount = useFormatMessage('Error.switchAccount');
  const title = useFormatMessage('Error.noPermission');
  const subtitle = useFormatMessage('Error.noPermissionDes');
  const applyForPermissionBtn = useFormatMessage('Error.applyForPermissionBtn');

  return (
    <ErrorPageBase
      buttons={[
        {
          label: applyForPermissionBtn,
          onClick: () => {
            // todo
          },
        },
        {
          label: goBackHomePage,
          type: 'plain',
          onClick: () => {
            history.push('/');
          },
        },
        {
          label: switchAccount,
          type: 'link',
          onClick: () => {
            location.href = '/api/v1/auth/logout';
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NoAuthorizationPage;
