.container {
  .ipt {
    cursor: pointer;
    margin-left: 12px;
    box-shadow: none;
    border: none;
    border-top: 2px solid transparent;
    border-bottom: 2px solid transparent;
    border-radius: 0;
    background-color: transparent;
    padding-right: 0;
    padding-left: 0;
    font-weight: 500;
    font-size: 14px;
    line-height: 1;
    height: 28px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--theme-text-color-default);

    &::placeholder {
      color: var(--theme-basic-color-black);
    }

    &:hover {
      box-shadow: none;
      border-bottom: 2px solid var(--theme-basic-color-guidance);
    }
  }
}

// 隐藏测量元素样式
.hiddenMeasure {
  position: absolute;
  top: 100%;
  left: 12px;
  visibility: hidden;
  font-weight: 700;
  font-size: 14px;
}
