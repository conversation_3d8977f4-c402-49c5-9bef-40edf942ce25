import type { InputRef } from 'antd';
import { Input, Tooltip } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';

import { CustomEventName } from '@/model/CustomEvent';
import { fm } from '@/modules/Locale';
import { onCustomEvent } from '@/utils/customEvent';

import css from './style.less';

interface Props {
  oldTitle: string;
  onTitleChange: (title: string) => void;
}

export function TitleInput({ oldTitle, onTitleChange }: Props) {
  const [title, setTitle] = useState('');
  const [titleWidth, setTitleWidth] = useState(46);
  const spanRef = useRef<HTMLSpanElement>(null);
  const inputRef = useRef<InputRef>(null);
  const titleRef = useRef(title);

  const inputPlaceholder = fm('Header.inputPlaceholder');

  useEffect(() => {
    setTitle(oldTitle);
  }, [oldTitle]);

  // 更新 titleRef 的值
  useEffect(() => {
    titleRef.current = title;
  }, [title]);

  // 根据输入内容调整宽度
  useEffect(() => {
    const current = spanRef.current;
    if (current) {
      if (!current.textContent) {
        setTitleWidth(46);
        return;
      }
      // 设置测量元素的内容
      current.textContent = title || inputPlaceholder;
      // 获取文本宽度，添加一些额外宽度用于输入框的padding
      const spanWidth = spanRef.current.offsetWidth + 5;
      // 确保宽度在46-240px之间
      const targetWidth = Math.max(46, Math.min(240, spanWidth));
      setTitleWidth(targetWidth);
    }
  }, [title, inputPlaceholder]);

  function handleFocus() {
    if (title === inputPlaceholder) {
      setTitle('');
    }
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const newValue = e.target.value;
    // 禁止第一个字符为空格
    if (newValue[0] === ' ') return;

    // 限制输入不超过100个字符
    if (newValue.length <= 100) {
      setTitle(newValue);
    }
  }

  // 重命名文件
  const handleTitleChange = useCallback(
    (newTitle: string) => {
      onTitleChange(newTitle);
    },
    [onTitleChange],
  );

  const handleTitleChangeRef = useRef(handleTitleChange);

  // 更新 handleTitleChangeRef 的值
  useEffect(() => {
    handleTitleChangeRef.current = handleTitleChange;
  }, [handleTitleChange]);

  // 处理失去焦点
  function handleBlur() {
    if (!title) {
      setTitle(inputPlaceholder);
      if (oldTitle !== inputPlaceholder) {
        handleTitleChange(inputPlaceholder);
      }
      return;
    }
    if (title !== oldTitle) {
      handleTitleChange(title);
    }
  }

  // 处理确认输入
  function handlePressEnter() {
    setTimeout(() => {
      inputRef.current?.blur();
    }, 0);
  }

  useEffect(() => {
    const removeListener = onCustomEvent<{ newTitle: string }>(CustomEventName.editorTitleChange, (detail) => {
      if (detail.newTitle !== titleRef.current) {
        handleTitleChangeRef.current(detail.newTitle);
      }
    });
    return () => {
      removeListener();
    };
  }, []);

  return (
    <span className={css.container}>
      <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={title}>
        <Input
          ref={inputRef}
          className={css.ipt}
          placeholder={inputPlaceholder}
          style={{ width: titleWidth || 'auto' }}
          value={title}
          onBlur={handleBlur}
          onChange={(e) => handleChange(e)}
          onFocus={handleFocus}
          onPressEnter={handlePressEnter}
        />
      </Tooltip>
      <span ref={spanRef} className={css.hiddenMeasure}>
        {title}
      </span>
    </span>
  );
}
