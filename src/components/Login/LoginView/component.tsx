/**
 * 登录表单功能，为登录页面或登录弹框提供登录的业务功能
 */
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import { useCallback, useState } from 'react';

import { useLoginByPassword } from '@/hooks/Authorization';
import { useFormatMessage } from '@/modules/Locale';
import { isEmail } from '@/utils/validate';

import styles from './style.less';

interface LoginViewProps {
  onSuccess?: () => void;
  onFailed?: () => void;
}

export default function LoginView(props: LoginViewProps) {
  const { onSuccess, onFailed } = props;
  const loginByPassword = useLoginByPassword();
  const [submitting, setSubmitting] = useState(false);

  const userNameInputPlaceholder = useFormatMessage('LoginView.userNameInputPlaceholder');
  const emailFormatInaccurate = useFormatMessage('LoginView.emailFormatInaccurate');
  const passwordInputPlaceholder = useFormatMessage('LoginView.passwordInputPlaceholder');
  const loginButtonText = useFormatMessage('LoginView.loginButtonText');
  const userNameInputLabel = useFormatMessage('LoginView.userNameInputLabel');
  const passwordInputLabel = useFormatMessage('LoginView.passwordInputLabel');

  const onFinishHandler = useCallback(
    async (values: { username: string; password: string }) => {
      setSubmitting(true);
      const success = await loginByPassword(values);
      setSubmitting(false);
      if (success) {
        onSuccess?.();
      } else {
        onFailed?.();
      }
    },
    [onSuccess, onFailed, loginByPassword, setSubmitting],
  );

  return (
    <Form
      disabled={submitting}
      initialValues={{ remember: true }}
      name="login"
      style={{ width: 324 }}
      onFinish={onFinishHandler}
    >
      <div className={styles.label}>{userNameInputLabel}</div>
      <Form.Item
        name="username"
        rules={[
          { required: true, message: userNameInputPlaceholder },
          {
            validator: (_, value) => {
              if (isEmail(value)) {
                return Promise.resolve();
              } else {
                return Promise.reject(emailFormatInaccurate);
              }
            },
          },
        ]}
        validateFirst={true}
        validateTrigger={['onBlur', 'submit']}
      >
        <Input placeholder={userNameInputPlaceholder} size="large" />
      </Form.Item>
      <div className={styles.label}>{passwordInputLabel}</div>
      <Form.Item name="password" rules={[{ required: true, message: passwordInputPlaceholder }]}>
        <Input.Password
          iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          placeholder={passwordInputPlaceholder}
          size="large"
          type="password"
        />
      </Form.Item>
      <Form.Item>
        <Button block htmlType="submit" loading={submitting} size="large" type="primary">
          {loginButtonText}
        </Button>
      </Form.Item>
    </Form>
  );
}
