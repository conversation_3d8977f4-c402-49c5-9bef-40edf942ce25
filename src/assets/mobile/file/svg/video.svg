<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_9485_17216)">
<mask id="path-1-outside-1_9485_17216" maskUnits="userSpaceOnUse" x="21" y="9" width="78" height="102" fill="black">
<rect fill="white" x="21" y="9" width="78" height="102"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.5442 10H22V110H98V32.4558L75.5442 10Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.5442 10H22V110H98V32.4558L75.5442 10Z" fill="white"/>
<path d="M22 10V9H21V10H22ZM75.5442 10L76.2513 9.29289L75.9584 9H75.5442V10ZM22 110H21V111H22V110ZM98 110V111H99V110H98ZM98 32.4558H99V32.0416L98.7071 31.7487L98 32.4558ZM22 11H75.5442V9H22V11ZM23 110V10H21V110H23ZM98 109H22V111H98V109ZM97 32.4558V110H99V32.4558H97ZM98.7071 31.7487L76.2513 9.29289L74.837 10.7071L97.2929 33.163L98.7071 31.7487Z" fill="#41464B" fill-opacity="0.1" mask="url(#path-1-outside-1_9485_17216)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.5442 12H24V108H96V32.4558L75.5442 12Z" fill="url(#paint0_linear_9485_17216)"/>
<g filter="url(#filter1_d_9485_17216)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M75.0451 10H75V33H98V32.9549L75.0451 10Z" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M72.75 59.6777L80.25 52.5V76.5L72.7986 69.3842L72.75 59.6777ZM39.75 52.5H72V76.5H39.75V52.5Z" fill="url(#paint1_linear_9485_17216)"/>
<defs>
<filter id="filter0_d_9485_17216" x="17" y="6" width="86" height="110" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9485_17216"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9485_17216" result="shape"/>
</filter>
<filter id="filter1_d_9485_17216" x="71" y="8" width="31" height="31" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9485_17216"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9485_17216" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9485_17216" x1="96" y1="108" x2="96" y2="12" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<linearGradient id="paint1_linear_9485_17216" x1="74.5281" y1="38.5264" x2="59.0525" y2="63.4577" gradientUnits="userSpaceOnUse">
<stop stop-color="#C5E2C3"/>
<stop offset="1" stop-color="#C7E2C1"/>
</linearGradient>
</defs>
</svg>
