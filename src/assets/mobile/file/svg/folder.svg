<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_148_9725)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M4.00001 6.1333C3.55818 6.1333 3.20001 6.49147 3.20001 6.9333V7.7333V12.1333C3.20001 12.5751 3.55818 12.9333 4.00001 12.9333H28C28.4418 12.9333 28.8 12.5751 28.8 12.1333V7.7333C28.8 7.29147 28.4418 6.9333 28 6.9333H11.2C11.2 6.49147 10.8418 6.1333 10.4 6.1333H4.00001Z" fill="url(#paint0_linear_148_9725)"/>
<g filter="url(#filter0_dii_148_9725)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2.00229 9.76793C1.9826 9.31296 2.34614 8.93335 2.80154 8.93335H29.1985C29.6539 8.93335 30.0174 9.31296 29.9977 9.76794L29.2542 26.9506C29.2449 27.1646 29.0687 27.3334 28.8545 27.3334H3.14548C2.93129 27.3334 2.75512 27.1646 2.74586 26.9506L2.00229 9.76793Z" fill="url(#paint1_linear_148_9725)"/>
</g>
</g>
<defs>
<filter id="filter0_dii_148_9725" x="-0.998474" y="7.93335" width="33.9969" height="24.3999" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_148_9725"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_148_9725" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.737255 0 0 0 0 0.803922 0 0 0 0 0.866667 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_148_9725"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_148_9725" result="effect3_innerShadow_148_9725"/>
</filter>
<linearGradient id="paint0_linear_148_9725" x1="11.5331" y1="6.1333" x2="11.5331" y2="8.50635" gradientUnits="userSpaceOnUse">
<stop stop-color="#AABBCE"/>
<stop offset="1" stop-color="#AABBCD"/>
</linearGradient>
<linearGradient id="paint1_linear_148_9725" x1="2" y1="8.93335" x2="2" y2="27.3334" gradientUnits="userSpaceOnUse">
<stop stop-color="#B2C2D5"/>
<stop offset="0.971281" stop-color="#9EB1C4"/>
</linearGradient>
<clipPath id="clip0_148_9725">
<rect width="32" height="32" fill="white"/>
</clipPath>
</defs>
</svg>
