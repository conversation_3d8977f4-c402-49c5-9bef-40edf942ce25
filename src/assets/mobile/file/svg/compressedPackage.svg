<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9477_16615)">
<g filter="url(#filter0_d_9477_16615)">
<path d="M75.5442 10H22V110H98V32.4558L75.5442 10Z" fill="white"/>
<path d="M98.5 110.5H21.5V9.5H75.751L98.5 32.249V110.5Z" stroke="#41464B" stroke-opacity="0.1"/>
</g>
<path d="M75.5442 12H24V108H96V32.4558L75.5442 12Z" fill="url(#paint0_linear_9477_16615)"/>
<g filter="url(#filter1_d_9477_16615)">
<path d="M75.0451 10H75V33H98V32.9549L75.0451 10Z" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M64.5 10.5H56.25V12.75H64.5V10.5ZM64.5 22.5H56.25V24.75H64.5V22.5ZM64.5 34.5H56.25V36.75H64.5V34.5ZM64.5 16.5H56.25V18.75H64.5V16.5ZM64.5 28.5H56.25V30.75H64.5V28.5ZM64.5 40.5H56.25V42.75H64.5V40.5ZM64.5 46.5H56.25V48.75H64.5V46.5ZM56.25 52.5H64.5V54.75H56.25V52.5ZM64.5 58.5H56.25V60.75H64.5V58.5Z" fill="url(#paint1_linear_9477_16615)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M64.5 64.5H56.25V66.75H64.5V64.5ZM56.1346 70.3235V66.75H53.7115C53.2811 66.75 52.5 67.5271 52.5 67.9412V85.8088C52.5 86.2229 53.2811 87 53.7115 87H67.0385C67.4689 87 68.25 86.2229 68.25 85.8088V67.9412C68.25 67.5271 67.4689 66.75 67.0385 66.75H64.6154V70.3235H56.1346ZM56.25 80.25V74.25H64.5V80.25H56.25Z" fill="url(#paint2_linear_9477_16615)"/>
</g>
<defs>
<filter id="filter0_d_9477_16615" x="13" y="3" width="94" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9477_16615"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9477_16615" result="shape"/>
</filter>
<filter id="filter1_d_9477_16615" x="67" y="4" width="39" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9477_16615"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9477_16615" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9477_16615" x1="96" y1="108" x2="96" y2="12" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<linearGradient id="paint1_linear_9477_16615" x1="60.5846" y1="-6.71268" x2="45.2477" y2="-3.67456" gradientUnits="userSpaceOnUse">
<stop stop-color="#D7D7D7"/>
<stop offset="1" stop-color="#C3C3C3"/>
</linearGradient>
<linearGradient id="paint2_linear_9477_16615" x1="52.5" y1="64.5" x2="52.5" y2="87" gradientUnits="userSpaceOnUse">
<stop stop-color="#D7D7D7"/>
<stop offset="1" stop-color="#BFBFBF"/>
</linearGradient>
<clipPath id="clip0_9477_16615">
<rect width="120" height="120" fill="white"/>
</clipPath>
</defs>
</svg>
