<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9493_14549)">
<g filter="url(#filter0_d_9493_14549)">
<path d="M75.5442 10H22V110H98V32.4558L75.5442 10Z" fill="white"/>
<path d="M98.5 110.5H21.5V9.5H75.751L98.5 32.249V110.5Z" stroke="#41464B" stroke-opacity="0.1"/>
</g>
<path d="M75.5442 12H24V108H96V32.4558L75.5442 12Z" fill="url(#paint0_linear_9493_14549)"/>
<g filter="url(#filter1_d_9493_14549)">
<path d="M75.0451 10H75V33H98V32.9549L75.0451 10Z" fill="white"/>
</g>
<circle cx="58.5" cy="67.5" r="27" fill="#D06545" fill-opacity="0.25"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.5 48.75C31.5 48.3358 31.8358 48 32.25 48H69.75C70.1642 48 70.5 48.3358 70.5 48.75V86.25C70.5 86.6642 70.1642 87 69.75 87H32.25C31.8358 87 31.5 86.6642 31.5 86.25V48.75Z" fill="#D06545" fill-opacity="0.75"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M52.1863 55.5H42V79.5H46.9666V70.895H52.1863C53.5993 70.895 54.8014 70.6765 55.7926 70.2395C56.7838 69.8025 57.5905 69.2255 58.2127 68.5084C58.8348 67.7913 59.2882 66.9678 59.5729 66.0378C59.8576 65.1078 60 64.1611 60 63.1975C60 62.2115 59.8576 61.2591 59.5729 60.3403C59.2882 59.4216 58.8348 58.6036 58.2127 57.8866C57.5905 57.1695 56.7838 56.5924 55.7926 56.1555C54.8014 55.7185 53.5993 55.5 52.1863 55.5ZM51.5192 66H46.9666V60H51.5192C52.0385 60 52.5385 60.0374 53.0192 60.1121C53.5 60.1869 53.9231 60.3318 54.2885 60.5467C54.6538 60.7617 54.9471 61.0654 55.1683 61.4579C55.3894 61.8505 55.5 62.3645 55.5 63C55.5 63.6355 55.3894 64.1495 55.1683 64.5421C54.9471 64.9346 54.6538 65.2383 54.2885 65.4533C53.9231 65.6682 53.5 65.8131 53.0192 65.8878C52.5385 65.9626 52.0385 66 51.5192 66Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_9493_14549" x="13" y="3" width="94" height="118" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9493_14549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9493_14549" result="shape"/>
</filter>
<filter id="filter1_d_9493_14549" x="67" y="4" width="39" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9493_14549"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9493_14549" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9493_14549" x1="96" y1="108" x2="96" y2="12" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_9493_14549">
<rect width="120" height="120" fill="white"/>
</clipPath>
</defs>
</svg>
