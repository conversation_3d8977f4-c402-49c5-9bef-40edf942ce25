<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_9244_28391)">
<rect x="12" y="23" width="96" height="72" fill="white"/>
<rect x="11.5" y="22.5" width="97" height="73" stroke="#41464B" stroke-opacity="0.1"/>
</g>
<rect x="14" y="25" width="92" height="68" fill="url(#paint0_linear_9244_28391)"/>
<rect opacity="0.05" x="12" y="39" width="20" height="56" fill="#41464B"/>
<rect x="12" y="23" width="96" height="16" fill="#BCDDC3" fill-opacity="0.6"/>
<path opacity="0.05" fill-rule="evenodd" clip-rule="evenodd" d="M30 51H106V53H30V51Z" fill="#41464B"/>
<path opacity="0.05" fill-rule="evenodd" clip-rule="evenodd" d="M12 37H108V39H12V37Z" fill="#41464B"/>
<path opacity="0.05" fill-rule="evenodd" clip-rule="evenodd" d="M30 65H106V67H30V65Z" fill="#41464B"/>
<path opacity="0.05" fill-rule="evenodd" clip-rule="evenodd" d="M30 79H106V81H30V79Z" fill="#41464B"/>
<rect opacity="0.05" x="30" y="23" width="2" height="72" fill="#41464B"/>
<rect opacity="0.05" x="56" y="25" width="2" height="68" fill="#41464B"/>
<rect opacity="0.05" x="82" y="25" width="2" height="68" fill="#41464B"/>
<g opacity="0.4">
<g opacity="0.5" filter="url(#filter1_d_9244_28391)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.8059 33.0125L42.3184 34.355H41.3809L43.4284 29H44.4259L46.4734 34.355H45.5359L45.0484 33.0125H42.8059ZM43.0684 32.2925H44.7859L43.9459 29.945H43.9159L43.0684 32.2925Z" fill="black" fill-opacity="0.5"/>
</g>
<g opacity="0.5" filter="url(#filter2_d_9244_28391)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M69.8409 34.355C70.4109 34.355 70.8609 34.25 71.1759 34.04C71.5434 33.785 71.7309 33.3875 71.7309 32.8475C71.7309 32.4875 71.6409 32.195 71.4684 31.985C71.2884 31.7675 71.0259 31.625 70.6734 31.5575C70.9434 31.4525 71.1459 31.31 71.2884 31.115C71.4309 30.905 71.5059 30.65 71.5059 30.35C71.5059 29.945 71.3634 29.6225 71.0859 29.3825C70.7859 29.1275 70.3659 29 69.8334 29H67.3809V34.355H69.8409ZM69.6009 31.25H68.2584V29.72H69.6159C69.9759 29.72 70.2309 29.78 70.3959 29.9C70.5459 30.0125 70.6284 30.2 70.6284 30.455C70.6284 30.7325 70.5459 30.935 70.3959 31.0625C70.2384 31.1825 69.9759 31.25 69.6009 31.25ZM68.2584 33.635H69.7134C70.0434 33.635 70.3059 33.5825 70.4934 33.4775C70.7334 33.3425 70.8534 33.1325 70.8534 32.8325C70.8534 32.525 70.7634 32.3075 70.5909 32.1725C70.4109 32.0375 70.1259 31.97 69.7359 31.97H68.2584V33.635Z" fill="black" fill-opacity="0.5"/>
</g>
<g opacity="0.5" filter="url(#filter3_d_9244_28391)">
<path d="M94.9459 34.565C95.5234 34.565 96.0184 34.4 96.4309 34.07C96.8734 33.7175 97.1509 33.23 97.2709 32.6H96.4159C96.3109 33.005 96.1309 33.3125 95.8759 33.515C95.6359 33.695 95.3209 33.7925 94.9384 33.7925C94.3534 33.7925 93.9184 33.605 93.6409 33.245C93.3859 32.9075 93.2584 32.42 93.2584 31.79C93.2584 31.175 93.3859 30.695 93.6484 30.35C93.9334 29.96 94.3534 29.7725 94.9159 29.7725C95.2909 29.7725 95.5984 29.8475 95.8384 30.0125C96.0859 30.1775 96.2509 30.4325 96.3409 30.785H97.1959C97.1134 30.245 96.8809 29.81 96.4909 29.495C96.0859 29.165 95.5609 29 94.9309 29C94.0759 29 93.4234 29.2775 92.9734 29.8475C92.5759 30.3425 92.3809 30.995 92.3809 31.79C92.3809 32.6 92.5684 33.245 92.9509 33.725C93.3859 34.28 94.0534 34.565 94.9459 34.565Z" fill="black" fill-opacity="0.5"/>
</g>
</g>
<g opacity="0.4">
<g opacity="0.7" filter="url(#filter4_d_9244_28391)">
<path d="M22.7204 49V45.2515H22.2899C22.1639 45.388 22.0064 45.5087 21.8174 45.6242C21.6284 45.7292 21.4552 45.8027 21.2872 45.8447V46.4117C21.6337 46.312 21.9224 46.1545 22.1482 45.9392V49H22.7204Z" fill="black" fill-opacity="0.5"/>
</g>
<g opacity="0.7" filter="url(#filter5_d_9244_28391)">
<path d="M23.4856 61.822V61.318H21.6113C21.6901 61.1133 21.9316 60.877 22.3358 60.6093C22.7086 60.3572 22.9658 60.1578 23.1128 60.0055C23.3543 59.7378 23.4803 59.4438 23.4803 59.1183C23.4803 58.7875 23.3648 58.5197 23.1391 58.3097C22.9081 58.0997 22.6088 58 22.2518 58C21.8528 58 21.5273 58.1312 21.2858 58.4042C21.0496 58.6562 20.9288 58.9923 20.9236 59.4123H21.4958C21.5063 59.1078 21.5693 58.882 21.6953 58.7245C21.8161 58.5617 21.9946 58.483 22.2308 58.483C22.4513 58.483 22.6246 58.5355 22.7401 58.6405C22.8556 58.7455 22.9133 58.903 22.9133 59.113C22.9133 59.3335 22.8241 59.5382 22.6561 59.7272C22.5511 59.8375 22.3726 59.9792 22.1206 60.1577C21.6953 60.4517 21.4118 60.6775 21.2753 60.8403C21.0233 61.1237 20.9026 61.4492 20.9026 61.822H23.4856Z" fill="black" fill-opacity="0.5"/>
</g>
<g opacity="0.7" filter="url(#filter6_d_9244_28391)">
<path d="M22.2098 74.7175C22.6088 74.7175 22.9396 74.602 23.1916 74.3815C23.4278 74.161 23.5486 73.8775 23.5486 73.531C23.5486 73.3105 23.4856 73.1268 23.3596 72.985C23.2441 72.8485 23.0761 72.7435 22.8556 72.6753C23.2651 72.5388 23.4751 72.2605 23.4751 71.851C23.4751 71.5308 23.3596 71.2788 23.1391 71.095C22.9081 70.9113 22.6088 70.822 22.2308 70.822C21.8633 70.822 21.5641 70.9218 21.3331 71.1318C21.0863 71.3418 20.9498 71.6305 20.9183 72.0033H21.4801C21.5011 71.7723 21.5798 71.599 21.7058 71.4835C21.8318 71.368 22.0103 71.3103 22.2361 71.3103C22.4566 71.3103 22.6298 71.3575 22.7453 71.4625C22.8503 71.5623 22.9081 71.704 22.9081 71.893C22.9081 72.082 22.8451 72.229 22.7296 72.3288C22.6141 72.4285 22.4408 72.481 22.2098 72.481H21.9421V72.9115H22.2151C22.4618 72.9115 22.6508 72.964 22.7768 73.069C22.9133 73.174 22.9816 73.3368 22.9816 73.5573C22.9816 73.7463 22.9133 73.8985 22.7873 74.0245C22.6403 74.161 22.4461 74.2345 22.2046 74.2345C21.9841 74.2345 21.8056 74.1715 21.6691 74.0455C21.5116 73.909 21.4328 73.7148 21.4223 73.4628H20.8448C20.8763 73.8828 21.0233 74.203 21.2858 74.4235C21.5168 74.6178 21.8266 74.7175 22.2098 74.7175Z" fill="black" fill-opacity="0.5"/>
</g>
<g opacity="0.7" filter="url(#filter7_d_9244_28391)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.0971 86.6523V87.466H22.5511V86.6523H20.7241V86.1115L22.5721 83.7175H23.0971V86.1955H23.6641V86.6523H23.0971ZM21.1914 86.1955H22.5511V84.442H22.5354L21.1914 86.1955Z" fill="black" fill-opacity="0.5"/>
</g>
</g>
<defs>
<filter id="filter0_d_9244_28391" x="3" y="16" width="114" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter1_d_9244_28391" x="39.3809" y="29" width="9.09253" height="9.35498" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter2_d_9244_28391" x="65.3809" y="29" width="8.34998" height="9.35498" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter3_d_9244_28391" x="90.3809" y="29" width="8.89001" height="9.56494" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter4_d_9244_28391" x="19.2871" y="45.2515" width="5.43335" height="7.74854" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter5_d_9244_28391" x="18.9026" y="58" width="6.58301" height="7.82202" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter6_d_9244_28391" x="18.8448" y="70.822" width="6.70374" height="7.89551" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<filter id="filter7_d_9244_28391" x="18.7241" y="83.7175" width="6.93994" height="7.74854" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9244_28391"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9244_28391" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9244_28391" x1="106" y1="93" x2="106" y2="25" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
</defs>
</svg>
