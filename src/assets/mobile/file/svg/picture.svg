<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_9484_16896)">
<rect x="12" y="24" width="96" height="72" fill="white"/>
<rect x="11.5" y="23.5" width="97" height="73" stroke="#41464B" stroke-opacity="0.1"/>
</g>
<rect x="14" y="26" width="92" height="68" fill="url(#paint0_linear_9484_16896)"/>
<path opacity="0.8" fill-rule="evenodd" clip-rule="evenodd" d="M106 86.5L86.3102 67.5L67.9846 78L40.4964 49.5L14 80.5V94H106V86.5Z" fill="#ABC7EE"/>
<circle opacity="0.8" cx="82" cy="44" r="8" fill="#ABC7EE"/>
<defs>
<filter id="filter0_d_9484_16896" x="3" y="17" width="114" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9484_16896"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9484_16896" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9484_16896" x1="106" y1="94" x2="106" y2="26" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
</defs>
</svg>
