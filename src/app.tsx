import ThemeWrapper from './components/ThemeWrapper';
import { initLocale } from './hooks/Locale';
import { isMobilePlatform } from './utils/platform';

if (isMobilePlatform) {
  require('./global.mobile.less');
}

if (process.env.PLATFORM === 'mobile') {
  require('./global.mobile.less');
} else {
  require('./global.pc.less');
}

export const rootContainer = (container: React.ReactNode) => {
  // 初始化当前的页面语言
  initLocale();
  return <ThemeWrapper>{container}</ThemeWrapper>;
};
