import type { ErrorCodes } from '@/utils/request/ErrorCodeMap';

import H5FileDeletePage from './pages/H5FileDeletePage';
import H5NetErrorPage from './pages/H5NetErrorPage';
import H5NoAuthorizationPage from './pages/H5NoAuthorizationPage';
import NoLoginPage from './pages/H5NoLoginPage';
import H5NoSeatsPage from './pages/H5NoSeatsPage';
import H5NotFoundPage from './pages/H5NotFoundPage';

interface H5ErrorPageRendererProps {
  errorCode?: ErrorCodes;
}

const H5ErrorPageRenderer = (props: H5ErrorPageRendererProps) => {
  const { errorCode } = props;

  switch (errorCode) {
    case 60093:
      return <NoLoginPage />;
    case 404:
    case 60001:
      return <H5NotFoundPage />;
    case 60002:
      return <H5FileDeletePage />;
    case 20025:
    case 403:
      return <H5NoAuthorizationPage />;
    case 1:
      return <H5NetErrorPage />;
    case 31000:
      return <H5NoSeatsPage />;
    default:
      return <H5NotFoundPage />;
  }
};

export default H5ErrorPageRenderer;
