.container {
  min-height: 100%;
  background: var(--theme-layout-color-bg-new-page);
}

form {
  display: flex;
  flex-direction: column;
}

.navBar {
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  padding: 4px 12px;
  margin-bottom: 20px;
  background: var(--theme-basic-color-bg-default);
}

.radioItem {
  margin: 8px 0;

  :global {
    .adm-radio-content {
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
      color: var(--theme-text-color-default);
    }

    .adm-form-item-label {
      color: var(--theme-text-color-default);
    }
  }
}

.submitBtn {
  margin: 0 30px;
  background: var(--theme-drive-button-color);
  color: var(--theme-button-color-default);
  border: none;
  font-size: 16px;
  height: 40px;
}

.permissionItem {
  padding: 12px 20px;

  :global {
    .adm-form-item-child-inner {
      display: flex;
      justify-content: space-between;
    }
  }
}

.remarkItem {
  margin: 20px 0;

  :global {
    .adm-text-area {
      padding: 0 20px;
      height: 120px;
    }

    .adm-text-area-element {
      font-size: 14px;
    }
  }
}
