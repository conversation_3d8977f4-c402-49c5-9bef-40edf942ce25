import { Button, Form, NavBar, Radio, TextArea } from 'antd-mobile';
import React, { useState } from 'react';

import { roleApply } from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { ReactComponent as RadioIcon } from '@/assets/images/svg/Radio.svg';
import { ReactComponent as RadioSelectedIcon } from '@/assets/images/svg/RadioSelected.svg';
import type { permissionType } from '@/constants/errorPage';
import { useFormatMessage } from '@/modules/Locale';
import { showErrorToast } from '@/utils-mobile/mobileToast';

import styles from './index.less';

interface H5AuthorizationApplyProps {
  handleApplyPermission: (showApply: boolean) => void;
  handleApplySend: (isSend: boolean) => void;
}

const H5AuthorizationApply: React.FC<H5AuthorizationApplyProps> = (props) => {
  const [selectedPermission, setSelectedPermission] = useState<permissionType>('reader');
  const [remark, setRemark] = useState<string>('');
  const OptionReadableLabel = useFormatMessage('Error.permissionReadable');
  const OptionCommentableLabel = useFormatMessage('Error.permissionCommentable');
  const OptionEditableLabel = useFormatMessage('Error.permissionEditable');
  const selectPermissionTypeLabel = useFormatMessage('Error.selectPermissionType');
  const narBarTextLabel = useFormatMessage('Error.applyForPermission');
  const addRemarkPlaceholder = useFormatMessage('Error.addRemarkPlaceholder');
  const sendApplicationBtn = useFormatMessage('Error.sendApplication');
  const applyTooFrequent = useFormatMessage('Error.applyTooFrequent');

  const permissionOptions = [
    { value: 'reader', label: OptionReadableLabel },
    { value: 'commentator', label: OptionCommentableLabel },
    { value: 'editor', label: OptionEditableLabel },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const [err, res] = await catchApiResult(
      roleApply({
        role: selectedPermission,
        comment: remark,
        fileGuid: 'N2A1MwmMn7TvEeAD', // todo fileGuid与其它页面对接时获取
      }),
    );
    if (res?.status === 200 || res?.status === 204) {
      setSelectedPermission('reader');
      setRemark('');
      props.handleApplySend(true);
      props.handleApplyPermission(false);
    }

    if (err?.status === 429) {
      showErrorToast(applyTooFrequent);
    } else {
      console.error(err);
      showErrorToast(err?.data?.msg || '');
    }
  };

  const onBack = () => {
    props.handleApplyPermission(false);
  };

  const setIcon = (checked: boolean) => (checked ? <RadioSelectedIcon /> : <RadioIcon />);

  return (
    <div className={styles.container}>
      <NavBar className={styles.navBar} onBack={onBack}>
        {narBarTextLabel}
      </NavBar>
      <form className={styles.form} onSubmit={handleSubmit}>
        <Form.Item className={styles.permissionItem} label={selectPermissionTypeLabel}>
          <Radio.Group value={selectedPermission} onChange={(val) => setSelectedPermission(val as permissionType)}>
            {permissionOptions.map((item) => (
              <Radio key={item.value} className={styles.radioItem} icon={setIcon} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item className={styles.remarkItem}>
          <TextArea maxLength={280} placeholder={addRemarkPlaceholder} rows={3} value={remark} onChange={setRemark} />
        </Form.Item>

        <Button className={styles.submitBtn} color="primary" type="submit">
          {sendApplicationBtn}
        </Button>
      </form>
    </div>
  );
};

export default H5AuthorizationApply;
