html body .h5ErrorPageLayouts {
  @text-color-default: var(--theme-text-color-default);
  @bg-color: var(--theme-basic-color-bg-default);

  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 52%;
    max-width: 320px;
    height: auto;
    margin-top: -5%;
  }

  h1 {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: @text-color-default;
    margin: 16px 0 8px;
    text-align: center;
  }

  h2 {
    font-size: 12px;
    color: @text-color-default;
    margin: 0 0 30px;
    font-weight: 400;
    line-height: 20px;
    text-align: center;
  }

  button {
    font-size: 14px;
    height: 32px;
    padding: 0 21px;
    margin-bottom: 10px;
    background: var(--theme-drive-button-color);
    color: var(--theme-button-color-default);
    border-radius: 2px;

    &.plainButton {
      background: none;
      background-color: @bg-color;
      color: @text-color-default;
      border-color: var(--theme-basic-color-light);
    }

    &.linkButton {
      font-size: 12px;
      color: var(--theme-button-color-primary-hover);
      font-weight: 400;
      background: @bg-color;
      border: none;
      margin-top: -4px;
    }
  }
}
