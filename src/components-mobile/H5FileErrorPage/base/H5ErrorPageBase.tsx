import { Button } from 'antd-mobile';
import classNames from 'classnames';
import React from 'react';

import styles from './H5ErrorPageBase.less';

interface H5ErrorPageBaseProps {
  imgSrc: string;
  title: string;
  subTitle: string;
  buttons?: {
    label: string;
    onClick: () => void;
    type?: 'primary' | 'plain' | 'link';
  }[];
}

const H5ErrorPageBase: React.FC<H5ErrorPageBaseProps> = (props) => {
  const { imgSrc, title, subTitle, buttons } = props;
  return (
    <div className={styles.h5ErrorPageLayouts}>
      <img alt="error" src={imgSrc} />
      <h1>{title}</h1>
      <h2>{subTitle}</h2>
      {buttons?.map((button) => (
        <Button
          key={button.label}
          className={classNames({
            [styles.plainButton]: button.type === 'plain',
            [styles.linkButton]: button.type === 'link',
          })}
          onClick={button.onClick}
        >
          {button.label}
        </Button>
      ))}
    </div>
  );
};

export default H5ErrorPageBase;
