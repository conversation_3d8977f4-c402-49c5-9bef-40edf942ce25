import { CheckCircleOutlined } from '@ant-design/icons';
import { SpinLoading, Toast } from 'antd-mobile';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';

import { queryRoleApply } from '@/api/File';
import { catchApiResult } from '@/api/Request';
import img from '@/assets/images/svg/noAuthorization.svg';
import { useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { showErrorToast } from '@/utils-mobile/mobileToast';

import H5ErrorPageBase from '../base/H5ErrorPageBase';
import styles from './index.less';

interface H5NoAuthorizationProps {
  handleApplyPermission: (showApply: boolean) => void;
  handleApplySend: (isSend: boolean) => void;
  isApplySend: boolean;
}

const H5NoAuthorization: React.FC<H5NoAuthorizationProps> = (props) => {
  const { handleApplyPermission, handleApplySend, isApplySend = false } = props;
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const switchAccount = useFormatMessage('Error.switchAccount');
  const title = useFormatMessage('Error.noPermission');
  const me = useMeStore((state) => state.me);
  const userName = me?.email || '';
  const subTitle = useFormatMessage('Error.noPermissionDesH5', { user: userName });
  const applySubTitle = useFormatMessage('Error.applyPermissionSent');
  const applyForPermissionBtn = useFormatMessage('Error.applyForPermissionBtn');
  const toastContent = useFormatMessage('Error.applyPermissionSentSuccess');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!isApplySend) {
      const checkPermissionApply = async () => {
        setLoading(true);
        const [err, res] = await catchApiResult(queryRoleApply({ fileGUID: 'N2A1MwmMn7TvEeAD' })); // todo fileGuid与其它页面对接时获取
        if (res?.status === 200) {
          handleApplySend(true);
        }
        if (err) {
          console.error(err);
          showErrorToast(err?.data.msg);
        }
        setLoading(false);
      };
      checkPermissionApply();
    }
  }, [isApplySend, handleApplySend]);

  useEffect(() => {
    if (isApplySend) {
      Toast.show({
        icon: <CheckCircleOutlined />,
        content: toastContent,
        duration: 2000,
        maskClassName: styles.noAuthorizationMaskH5,
      });
    }
  }, [isApplySend, toastContent]);

  return loading ? (
    <div className={styles.loadingContainer}>
      <SpinLoading />
    </div>
  ) : (
    <H5ErrorPageBase
      buttons={[
        {
          label: applyForPermissionBtn,
          type: 'plain',
          onClick: () => handleApplyPermission(true),
        },
        {
          label: goBackHomePage,
          onClick: () => {
            history.push('/');
          },
        },
        {
          label: switchAccount,
          type: 'link',
          onClick: () => {
            location.href = '/api/v1/auth/logout';
          },
        },
      ]}
      imgSrc={img}
      subTitle={isApplySend ? applySubTitle : subTitle}
      title={title}
    />
  );
};

export default H5NoAuthorization;
