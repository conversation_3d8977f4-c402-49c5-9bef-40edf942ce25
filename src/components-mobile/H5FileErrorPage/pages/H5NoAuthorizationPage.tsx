import { useCallback, useState } from 'react';

import H5AuthorizationApply from '../H5AuthorizationApply';
import H5NoAuthorization from '../H5NoAuthorization';

const NoAuthorizationPage = () => {
  const [showApplyPage, setShowApplyPage] = useState(false);
  const [isApplySend, setIsApplySend] = useState(false);

  const handleApplyPermission = useCallback((showApply: boolean) => {
    setShowApplyPage(showApply);
  }, []);

  const handleApplySend = useCallback((isSend: boolean) => {
    setIsApplySend(isSend);
  }, []);

  return showApplyPage ? (
    <H5AuthorizationApply handleApplyPermission={handleApplyPermission} handleApplySend={handleApplySend} />
  ) : (
    <H5NoAuthorization
      handleApplyPermission={handleApplyPermission}
      handleApplySend={handleApplySend}
      isApplySend={isApplySend}
    />
  );
};

export default NoAuthorizationPage;
