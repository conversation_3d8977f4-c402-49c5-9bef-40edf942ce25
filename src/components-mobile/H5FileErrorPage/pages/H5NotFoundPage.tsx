import { history } from 'umi';

import h5NotFoundImg from '@/assets/images/svg/error-page-404.svg';
import { useFormatMessage } from '@/modules/Locale';

import H5ErrorPageBase from '../base/H5ErrorPageBase';

const H5NotFoundPage = () => {
  const title = useFormatMessage('Error.notFound');
  const subtitle = useFormatMessage('Error.notFoundDes');
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');

  return (
    <H5ErrorPageBase
      buttons={[
        {
          label: goBackHomePage,
          onClick: () => {
            history.push('/');
          },
        },
      ]}
      imgSrc={h5NotFoundImg}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default H5NotFoundPage;
