import { history } from 'umi';

import img from '@/assets/images/svg/error-page-no-auth.svg';
import { useFormatMessage } from '@/modules/Locale';

import H5ErrorPageBase from '../base/H5ErrorPageBase';

const NoLoginPage = () => {
  const title = useFormatMessage('Error.noLogin');
  const subtitle = useFormatMessage('Error.noLoginDes');
  const btnContent = useFormatMessage('Error.loginText');

  return (
    <H5ErrorPageBase
      buttons={[
        {
          label: btnContent,
          onClick: () => {
            history.push('/login');
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NoLoginPage;
