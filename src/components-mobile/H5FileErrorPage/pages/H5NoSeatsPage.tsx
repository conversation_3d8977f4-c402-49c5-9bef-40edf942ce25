import { history } from 'umi';

import img from '@/assets/images/svg/error-page-404.svg';
import { useFormatMessage } from '@/modules/Locale';

import H5ErrorPageBase from '../base/H5ErrorPageBase';

const NoSeatsPage = () => {
  const noSeatsTitle = useFormatMessage('Error.noSeatsTitle');
  const noSeatsTitleDes = useFormatMessage('Error.noSeatsTitleDes');
  const contactAdmin = useFormatMessage('Error.contactAdmin');
  const purchaseSeats = useFormatMessage('Error.purchaseSeats');

  return (
    <H5ErrorPageBase
      buttons={[
        {
          label: contactAdmin,
          onClick: () => {
            history.push('/');
          },
        },
        {
          label: purchaseSeats,
          type: 'plain',
          onClick: () => {
            // history.push('/');
          },
        },
      ]}
      imgSrc={img}
      subTitle={noSeatsTitleDes}
      title={noSeatsTitle}
    />
  );
};

export default NoSeatsPage;
