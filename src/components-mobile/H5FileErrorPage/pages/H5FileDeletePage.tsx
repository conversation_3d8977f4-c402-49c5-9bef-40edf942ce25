import { history } from 'umi';

import img from '@/assets/images/svg/error-page-404.svg';
import { useFormatMessage } from '@/modules/Locale';

import H5ErrorPageBase from '../base/H5ErrorPageBase';

const FileDeletePage = () => {
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const fileDeleteTitle = useFormatMessage('Error.fileDeleteTitle');
  const fileDeleteSubTitle = useFormatMessage('Error.fileDeleteSubTitle');

  return (
    <H5ErrorPageBase
      buttons={[
        {
          label: goBackHomePage,
          type: 'plain',
          onClick: () => {
            history.push('/');
          },
        },
      ]}
      imgSrc={img}
      subTitle={fileDeleteSubTitle}
      title={fileDeleteTitle}
    />
  );
};

export default FileDeletePage;
