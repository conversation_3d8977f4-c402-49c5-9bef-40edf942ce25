import { useStore } from 'zustand';
import { createWithEqualityFn } from 'zustand/traditional';
import { shallow } from 'zustand/vanilla/shallow';

import type { CollaboratorsData } from '@/api/Collaboration.type';

import type { CollaborationData } from '../../../components/Collaboration/types';

interface CollaboratorManagementStore {
  collaborationData: CollaborationData | null;
  setCollaborationData: (collaborationData: CollaborationData) => void;
  share: ShareInfo | null;
  setShare: (share: ShareInfo) => void;
  guid: string;
  setGuid: (guid: string) => void;
  collaborators: CollaboratorsData;
  setCollaborators: (collaborators: CollaboratorsData) => void;
  // 添加协作者时，是否发送通知
  sendNotification: boolean;
  setSendNotification: (sendNotification: boolean) => void;
  refreshNum: number;
  refresh: () => void;
}

export const collaborationStore = createWithEqualityFn<CollaboratorManagementStore>()(
  (set) => ({
    collaborationData: null,
    setCollaborationData: (collaborationData: CollaborationData) => set(() => ({ collaborationData })),
    share: null,
    setShare: (share: ShareInfo) => set(() => ({ share })),
    guid: '',
    setGuid: (guid: string) => set(() => ({ guid })),
    collaborators: { roles: [], admins: [] },
    setCollaborators: (collaborators: CollaboratorsData) => set(() => ({ collaborators })),
    sendNotification: true,
    setSendNotification: (sendNotification: boolean) => set(() => ({ sendNotification })),
    refreshNum: 0,
    refresh: () => set((state) => ({ refreshNum: state.refreshNum + 1 })),
  }),
  shallow,
);

export const useCollaborationStore = <U>(selector: (state: CollaboratorManagementStore) => U) => {
  return useStore(collaborationStore, selector);
};

// 存放hooks中的数据
export type ShareInfo = {
  passwordStatus: boolean;
  shareTimeStatus?: boolean;
  password?: string;
  shareDisabled?: boolean;
  copyUrl?: string;
  canManageCollaborator?: boolean;
  canManageAdmin: boolean;
};
