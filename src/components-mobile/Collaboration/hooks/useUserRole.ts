import { useCallback, useEffect, useState } from 'react';

import { getCollaborationList, getOrgDepartment, getOrgDepartmentUser, getRecentContact } from '@/api/Collaboration';
import type { CollaboratorsData } from '@/api/Collaboration.type';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import { useCollaborationStore } from '../../../components-mobile/Collaboration/store/CollaboratorManagement';
import { MobileItemAddRole } from '../components/const';
import { isDepartment } from '../utils/mobile';

export enum FrontRole {
  // 管理者
  admin = 'admin',
  // 协作者
  collaborator = 'collaborator',
  // 没有权限
  none = 'none',
  // 上级目录协作者
  parent = 'parent',
}

export type RoleData = {
  id: number;
  showBtn: boolean;
  avatar: string;
  displayText: string;
  email: string;
  isDept: boolean;
  name: string;
  frontRole: FrontRole;
  role?: string;
};

// 树节点数据类型
export interface TreeNode {
  id: number;
  name: string;
  type: 'department' | 'user';
  level: number;
  avatar?: string;
  email?: string;
  children?: TreeNode[];
  hasChildren?: boolean;
  isLoaded?: boolean;
  isExpanded?: boolean;
  parentId?: number;
  // 按钮显示的内容
  displayText: string;
  // 显示按钮还是文字，true显示按钮
  showBtn: boolean;
  // 是否显示下拉图标
  icon: boolean;
  // 前端定义的角色
  frontRole: FrontRole;
  // 当前的权限(管理者、只能阅读。。。)
  role?: string;
}

export type OmitTreeNode = Omit<TreeNode, 'displayText' | 'showBtn' | 'icon' | 'frontRole' | 'role'>;

function addExtraProp(data: OmitTreeNode, { roles, admins }: CollaboratorsData, meId: number | null) {
  const admin = admins.find((admin) => admin.id === data.id);
  if (admin) {
    const showBtn = !(admin.isInherited || meId === admin.id);
    const displayText = fm2('ShareCollaboration.admin');

    return {
      ...data,
      showBtn,
      displayText,
      icon: true,
      frontRole: FrontRole.admin,
    };
  }

  const role = roles.find((role) => role.id === data.id);
  if (role) {
    const showBtn = !(role.isInherited || meId === role.id);
    const displayText =
      MobileItemAddRole().find((item) => item.key === role.role)?.label || fm2('ShareCollaboration.commentAndEdit');

    return {
      ...data,
      showBtn,
      displayText,
      icon: true,
      frontRole: FrontRole.collaborator,
      role: role.role,
    };
  }

  return {
    ...data,
    showBtn: true,
    displayText: fm2('ShareCollaboration.addPermission'),
    icon: false,
    frontRole: FrontRole.none,
  };
}

export function useUserRole() {
  const meId = useMeStore((state) => state.me.id);
  const { guid } = useCollaborationStore((state) => state);

  const [rootDepartments, setRootDepartments] = useState<TreeNode>();
  const [childrenDepartments, setChildrenDepartments] = useState<TreeNode[]>([]);
  const [userData, setUserData] = useState<CollaboratorsData>();

  // 创建基础节点数据（不包含权限信息）
  const createBaseTreeNode = useCallback(
    (data: OmitTreeNode): TreeNode => ({
      ...data,
      showBtn: true,
      displayText: fm2('ShareCollaboration.addPermission'),
      icon: false,
      frontRole: FrontRole.collaborator,
    }),
    [],
  );

  // 缓存部门原始数据
  const [rawDepartmentData, setRawDepartmentData] = useState<{
    root: OmitTreeNode;
    childrenData: OmitTreeNode[];
  } | null>(null);

  // 应用权限信息到节点
  const applyPermissionsToNodes = useCallback(
    (
      rawData: {
        root: OmitTreeNode;
        childrenData: OmitTreeNode[];
      },
      collaboratorsData?: CollaboratorsData,
    ) => {
      if (!collaboratorsData) {
        // 没有权限数据时，使用默认值
        const root = createBaseTreeNode(rawData.root);
        const childrenData = rawData.childrenData.map(createBaseTreeNode);
        return { root, childrenData };
      }

      // 有权限数据时，应用权限
      const { roles, admins } = collaboratorsData;
      const root = addExtraProp(rawData.root, { roles, admins }, meId);
      const childrenData = rawData.childrenData.map((node) => addExtraProp(node, { roles, admins }, meId));
      return { root, childrenData };
    },
    [createBaseTreeNode, meId],
  );

  // 加载原始部门数据（不包含权限处理）
  const loadRawDepartmentData = useCallback(async (departmentId: number, level: number = 0) => {
    const [usersResponse, departmentsResponse] = await Promise.all([
      getOrgDepartmentUser(departmentId, { perPage: 500, page: 1 }),
      getOrgDepartment(departmentId),
    ]);

    const departments = departmentsResponse.data.subdepartments.map((department) => ({
      id: department.id,
      name: department.name,
      type: 'department' as const,
      level: level,
      hasChildren: true,
      isLoaded: false,
      isExpanded: false,
      parentId: departmentId === 1 ? undefined : departmentId,
    }));

    const users = usersResponse.data.users.map((user) => ({
      id: user.id,
      name: user.name,
      type: 'user' as const,
      level: level,
      avatar: user.avatar,
      email: user.email,
      parentId: departmentId === 1 ? undefined : departmentId,
    }));

    const root = {
      id: 1,
      name: departmentsResponse.data.currentDepartment.name,
      type: 'department' as const,
      level: 0,
      hasChildren: true,
      isLoaded: false,
      isExpanded: false,
    };

    return {
      root,
      childrenData: [...departments, ...users],
    };
  }, []);

  // 并行加载数据
  useEffect(() => {
    if (!guid) return;

    const loadData = async () => {
      try {
        const [userDataResponse, rawDepartmentResponse] = await Promise.all([
          getCollaborationList(guid, { includeInherited: false, includeAdmin: true }),
          loadRawDepartmentData(1, 1),
        ]);

        // 缓存原始数据
        setRawDepartmentData(rawDepartmentResponse);
        setUserData(userDataResponse.data);

        // 应用权限并设置最终数据
        const { root, childrenData } = applyPermissionsToNodes(rawDepartmentResponse, userDataResponse.data);
        setRootDepartments(root);
        setChildrenDepartments(childrenData);
      } catch (error) {
        console.error('Failed to load data:', error);
      }
    };

    loadData();
  }, [guid, loadRawDepartmentData, applyPermissionsToNodes]);

  // 加载部门数据（用于展开子部门）
  const loadDepartmentData = useCallback(
    async (departmentId: number, level: number = 0) => {
      // 如果是根部门且已有缓存数据，直接使用缓存
      if (departmentId === 1 && rawDepartmentData) {
        return applyPermissionsToNodes(rawDepartmentData, userData);
      }

      // 否则重新加载
      const rawData = await loadRawDepartmentData(departmentId, level);
      return applyPermissionsToNodes(rawData, userData);
    },
    [rawDepartmentData, userData, loadRawDepartmentData, applyPermissionsToNodes],
  );

  return {
    rootDepartments,
    setRootDepartments,
    childrenDepartments,
    setChildrenDepartments,
    loadDepartmentData,
  };
}

export function useRecentUserRole() {
  const meId = useMeStore((state) => state.me.id);
  const { guid } = useCollaborationStore((state) => state);

  const [data, setData] = useState<TreeNode[]>([]);

  useEffect(() => {
    // 获取最近联系人，通过getCollaborationList获取联系人的权限信息
    Promise.all([getCollaborationList(guid, { includeInherited: false, includeAdmin: true }), getRecentContact()]).then(
      ([getUserData, res]) => {
        const list = res.data.results.map((item) => {
          const { roles, admins } = getUserData.data;
          const admin = admins.find((admin) => admin.id === item.id);
          if (admin) {
            const isDept = isDepartment(admin);
            const showBtn = !(admin.isInherited || meId === admin.id);
            const displayText = fm2('ShareCollaboration.admin');

            return {
              id: item.id,
              name: item.name,
              email: item.email,
              level: 0,
              avatar: item.avatar,
              isDept,
              showBtn,
              showParent: false,
              displayText,
              icon: true,
              frontRole: FrontRole.admin,
              type: 'user' as const,
            };
          }

          const role = roles.find((role) => role.id === item.id);
          if (role) {
            const isDept = isDepartment(admin);
            const showBtn = !(role.isInherited || meId === role.id);
            const displayText =
              MobileItemAddRole().find((item) => item.key === role.role)?.label ||
              fm2('ShareCollaboration.commentAndEdit');

            return {
              id: item.id,
              name: item.name,
              email: item.email,
              level: 0,
              avatar: item.avatar,
              isDept,
              showBtn,
              showParent: false,
              displayText,
              icon: true,
              frontRole: FrontRole.collaborator,
              type: 'user' as const,
            };
          }

          return {
            id: item.id,
            name: item.name,
            email: item.email,
            level: 0,
            avatar: item.avatar,
            isDept: false,
            showBtn: true,
            showParent: false,
            displayText: fm2('ShareCollaboration.addPermission'),
            icon: false,
            frontRole: FrontRole.none,
            type: 'user' as const,
          };
        });

        setData(list);
      },
    );
  }, [guid, meId]);

  return {
    data,
    setData,
  };
}
