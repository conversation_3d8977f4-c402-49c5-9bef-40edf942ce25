import { useCallback } from 'react';

import { getCollaborationList } from '@/api/Collaboration';
import { getCollaborationDataInfo } from '@/components/Collaboration/utils';

import { useCollaborationStore } from '../../../components-mobile/Collaboration/store/CollaboratorManagement';

export function useRefresh() {
  const { guid, setCollaborationData, setCollaborators, refresh } = useCollaborationStore((state) => state);

  const handleRefresh = useCallback(() => {
    if (guid) {
      getCollaborationDataInfo(guid).then((res) => {
        if (res) {
          setCollaborationData(res.data);
        }
      });
      getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
        setCollaborators(res.data);
      });
      refresh();
    }
  }, [guid, refresh, setCollaborationData, setCollaborators]);

  return {
    handleRefresh,
  };
}
