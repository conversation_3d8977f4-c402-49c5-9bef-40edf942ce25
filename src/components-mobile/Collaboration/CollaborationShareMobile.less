.collaborationShareMobile {
  :global(.adm-action-sheet-button-item) {
    padding: 0;
  }

  :global(.adm-action-sheet-button-item:active) {
    background-color: transparent;
  }

  :global(.adm-action-sheet-cancel) {
    padding-top: 5px;
  }

  :global(.adm-action-sheet-cancel .adm-action-sheet-button-item-name) {
    padding: 10px;
    text-align: center;
    color: var(--theme-text-color-medium);
    font-size: 14px;
  }
}

.userName {
  text-align: left;
}

.permissionItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.permissionItemLeft {
  display: flex;
  align-items: center;
  justify-content: center;
}

.permissionItemText {
  margin-left: 10px;
  font-size: 14px;
  color: var(--theme-text-color-default);
}

.permissionItemIcon {
  width: 20px;
  height: 20px;
}

.checkIcon {
  width: 18px;
  height: 18px;
}
