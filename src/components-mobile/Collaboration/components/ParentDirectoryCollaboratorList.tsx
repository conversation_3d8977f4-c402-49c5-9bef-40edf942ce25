import { useEffect, useState } from 'react';

import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { itemsPrenet } from '@/components/Collaboration/components';
import { getInheritedCollaborators } from '@/components/Collaboration/utils';

import { isDepartment } from '../utils/mobile';
import {
  CollarboratorCard,
  CollarboratorCardContent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';
import { ItemAvatar, ItemButton } from './CollaboratorItem';

const getItemName = (itemsObj: typeof itemsPrenet, item: string | number) => {
  const foundItem = itemsObj().find((e) => e.key === item);
  return foundItem?.label || '';
};

export function ParentDirectoryCollaboratorList({ guid }: { guid: string }) {
  const [data, setData] = useState<CollaboratorsData['roles']>([]);

  useEffect(() => {
    function loadData() {
      getInheritedCollaborators(guid).then((res) => {
        setData(res);
      });
    }

    if (guid) {
      loadData();
    }
  }, [guid]);

  return (
    <CollarboratorCard>
      {data.map((item) => {
        const isDept = isDepartment(item);
        const avatar = isDept ? <Organization height={32} width={32} /> : item.avatar;
        const displayText = getItemName(itemsPrenet, item.role);

        return (
          <CollarboratorCardContent key={item.id}>
            <CollarboratorCardLeft>
              <ItemAvatar avatar={avatar} />
              <CollarboratorCardName email={item.email} name={item.name} />
            </CollarboratorCardLeft>
            <ItemButton displayText={displayText} showBtn={false} />
          </CollarboratorCardContent>
        );
      })}
    </CollarboratorCard>
  );
}
