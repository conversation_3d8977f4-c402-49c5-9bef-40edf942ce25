import { message } from 'antd';
import { ActionSheet, Dialog } from 'antd-mobile';
import toast from 'antd-mobile/es/components/toast';
import { useCallback, useMemo } from 'react';

import { deleteAdmin, deleteDepAdmin, updateParentCollaboration } from '@/api/Collaboration';
import { catchApiResult } from '@/api/Request';
import { ReactComponent as CheckIcon } from '@/assets/images/svg/check.svg';
import type { RoleItem } from '@/components/Collaboration/types';
import { fm, fm2 } from '@/modules/Locale';

import styles from '../CollaborationShareMobile.less';
import { FrontRole } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import {
  isDepartment,
  setCollaboartionAdminAction,
  setCollaboartionPermissionAction,
  setCollaboartionRemoveAction,
} from '../utils/mobile';
import { CollarboratorCardContent, CollarboratorCardLeft, CollarboratorCardName } from './Collaborator';
import { ItemAvatar } from './CollaboratorItem';
import { MobileItemAddRole, MobileItemsAdmin, MobileItemsPrenet } from './const';

export type TUserInfo = {
  id?: number;
  avatar: string | React.ReactNode;
  name: string;
  email?: string;
  departmentId?: number;
  role: string;
};

export interface CloseProp {
  // 类型 管理者 or 协作者
  type: FrontRole;
  name: ReturnType<typeof MobileItemAddRole>[number]['key'];
}

function UserInfo({ data }: { data: TUserInfo }) {
  const isDept = isDepartment(data);

  return (
    <CollarboratorCardContent>
      <CollarboratorCardLeft>
        <ItemAvatar avatar={data.avatar} isDept={isDept} />
        <CollarboratorCardName className={styles.userName} email={data.email} name={data.name} />
      </CollarboratorCardLeft>
    </CollarboratorCardContent>
  );
}

function PermissionItem({
  icon,
  text,
  isChecked,
  onClick,
}: {
  onClick?: () => void;
  text: string;
  icon: React.ReactNode;
  isChecked?: boolean;
}) {
  return (
    <div className={styles.permissionItem} onClick={onClick}>
      <div className={styles.permissionItemLeft}>
        <div className={styles.permissionItemIcon}>{icon}</div>
        <div className={styles.permissionItemText}>{text}</div>
      </div>
      {isChecked && <CheckIcon className={styles.checkIcon} />}
    </div>
  );
}

function AdministratorList({ data, onClick }: { data: TUserInfo; onClick: (data: CloseProp) => void }) {
  const { guid } = useCollaborationStore((state) => state);

  function handleItemClick() {
    Dialog.confirm({
      title: fm2('ShareCollaboration.confirmRemoveCollaborator'),
      content: fm2('ShareCollaboration.removeCollaborator', { name: data.name }),
      cancelText: fm2('ShareCollaboration.cancel'),
      onConfirm: async () => {
        const fn = data.departmentId ? deleteDepAdmin : deleteAdmin;
        const [err] = await catchApiResult(fn(guid, data.id!));
        if (err) {
          toast.show({ icon: 'fail', content: err.data.msg });
          return;
        }
        onClick({
          type: FrontRole.collaborator,
          name: MobileItemAddRole().find((item) => item.key === 'editor')!.key,
        });
      },
    });
  }

  return (
    <div>
      {MobileItemsAdmin().map((item) => (
        <PermissionItem
          key={item.key}
          icon={item.icon}
          isChecked={item.key === 'merger'}
          text={item.label}
          onClick={handleItemClick}
        />
      ))}
    </div>
  );
}

function CollaboratorList({
  data,
  onClick,
  sendNotification,
}: {
  data: TUserInfo;
  onClick: (data: CloseProp) => void;
  sendNotification: boolean;
}) {
  const { guid } = useCollaborationStore((state) => state);

  const handleClick = useCallback(
    async (name: string, item: RoleItem) => {
      const isDept = isDepartment(item);

      let flag = true;
      if (name === 'merger') {
        flag = await setCollaboartionAdminAction({
          isDept,
          guid,
          id: item.id,
          sendNotification,
        });
      } else if (name === 'remove') {
        flag = await setCollaboartionRemoveAction({
          isDept,
          guid,
          id: item.id,
        });
      } else {
        flag = await setCollaboartionPermissionAction({
          isDept,
          guid,
          id: item.id,
          key: name,
          sendNotification,
        });
      }
      if (flag) {
        onClick({
          type: FrontRole.collaborator,
          name,
        });
      }
    },
    [guid, onClick, sendNotification],
  );

  return (
    <div>
      {MobileItemAddRole().map((item) => (
        <PermissionItem
          key={item.key}
          icon={item.icon}
          isChecked={item.key === data.role}
          text={item.label}
          onClick={handleClick.bind(null, item.key, data as RoleItem)}
        />
      ))}
    </div>
  );
}

function ParentCollaboratorList({ data, onClick }: { data: TUserInfo; onClick: (data: CloseProp) => void }) {
  const { guid } = useCollaborationStore((state) => state);

  const handleClick = useCallback(
    async (name: string) => {
      const updateResult = await updateParentCollaboration(guid, { parentRole: name });
      if (updateResult.status === 200) {
        message.success(fm2('ShareCollaboration.modifySuccess'));
        onClick({
          type: FrontRole.parent,
          name: MobileItemsPrenet().find((item) => item.key === name)!.key,
        });
      }
    },
    [guid, onClick],
  );

  return (
    <div>
      {MobileItemsPrenet().map((item) => (
        <PermissionItem
          key={item.key}
          icon={item.icon}
          isChecked={item.key === data.role}
          text={item.label}
          onClick={handleClick.bind(null, item.key)}
        />
      ))}
    </div>
  );
}

function getList(close: (data: CloseProp) => void, data: TUserInfo, sendNotification: boolean, type?: FrontRole) {
  switch (type) {
    case 'admin':
      return <AdministratorList data={data} onClick={close} />;
    case 'collaborator':
      return <CollaboratorList data={data} sendNotification={sendNotification} onClick={close} />;
    case 'parent':
      return <ParentCollaboratorList data={data} onClick={close} />;

    default:
      return null;
  }
}

export function AddPermissionSettings({
  visible,
  close,
  data,
  type,
}: {
  visible: boolean;
  close: (data?: CloseProp) => void;
  data: TUserInfo;
  type?: FrontRole;
}) {
  const closeText = fm('ShareCollaborationMobile.close');
  const { sendNotification } = useCollaborationStore((state) => state);

  const actions = useMemo(() => {
    const list = getList(close, data, sendNotification, type);

    return [
      {
        key: 'linkSharingSwitch',
        text: <UserInfo data={data} />,
      },
      {
        key: 'shareSettings',
        text: list,
      },
    ];
  }, [close, data, sendNotification, type]);

  return (
    <ActionSheet
      actions={actions}
      cancelText={closeText}
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={close}
    />
  );
}
