import { Checkbox, Tabs } from 'antd-mobile';

import { ReactComponent as CheckboxIcon } from '@/assets/images/svg/checkbox.svg';
import { ReactComponent as CheckedIcon } from '@/assets/images/svg/checked-box.svg';
import { fm } from '@/modules/Locale';

import { useRecentUserRole } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { CollarboratorCard } from './Collaborator';
import styles from './CollaboratorManagement.less';
import { CollaboratorStruct } from './CollaboratorStruct';
import { CollaboratorDeptUerItem } from './CollaboratorUserItem';
import { SearchPopup } from './SearchPopup';

export function AddCollaborator() {
  const recentText = fm('ShareCollaborationMobile.recentContacts');
  const organizationText = fm('ShareCollaborationMobile.organizationStructure');
  const sendNotificationText = fm('ShareCollaborationMobile.sendNotificationWhenAdding');

  const { data } = useRecentUserRole();
  const { sendNotification, setSendNotification } = useCollaborationStore((state) => state);

  return (
    <>
      {/* 搜索框 */}
      <SearchPopup />

      <Tabs className={styles.addCollaboratorTabs}>
        <Tabs.Tab key="recently" title={recentText}>
          {data.length > 0 && (
            <CollarboratorCard>
              {data.map((item) => (
                <CollaboratorDeptUerItem key={item.id} node={item} />
              ))}
            </CollarboratorCard>
          )}
        </Tabs.Tab>
        <Tabs.Tab key="organization" title={organizationText}>
          <CollaboratorStruct />
        </Tabs.Tab>
      </Tabs>
      <div className={styles.addCollaboratorFooter}>
        <Checkbox
          checked={sendNotification}
          icon={(checked) => (checked ? <CheckedIcon /> : <CheckboxIcon />)}
          onChange={setSendNotification}
        >
          <span className={styles.addCollaboratorFooterText}>{sendNotificationText}</span>
        </Checkbox>
      </div>
    </>
  );
}
