import { EyeOutlined, MinusCircleOutlined } from '@ant-design/icons';

import { ReactComponent as CommentIcon } from '@/assets/images/svg/comment.svg';
import { ReactComponent as DeleteIcon } from '@/assets/images/svg/delete.svg';
import { ReactComponent as EditIcon } from '@/assets/images/svg/edit.svg';
import { ReactComponent as MergerIcon } from '@/assets/images/svg/merger.svg';
import { ReactComponent as StructIcon } from '@/assets/images/svg/struct.svg';
import { fm2 } from '@/modules/Locale';

// 移动端下，组织架构的权限
export const MobileItemsPrenet = () => [
  { key: 'inherited', label: fm2('ShareCollaboration.inheritPermission'), icon: <StructIcon /> },
  { key: 'reader', label: fm2('ShareCollaboration.readOnly'), icon: <EyeOutlined /> },
  { key: 'commentator', label: fm2('ShareCollaboration.comment'), icon: <CommentIcon /> },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit'), icon: <EditIcon /> },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess'), icon: <MinusCircleOutlined /> },
];

// 移动端下，管理者的权限
export const MobileItemsAdmin = () => [
  { key: 'merger', label: fm2('ShareCollaboration.admin'), icon: <MergerIcon /> },
  { key: 'remove', label: fm2('ShareCollaboration.removeManager'), danger: true, icon: <DeleteIcon /> },
];

// 移动端下，添加协作者的选项
export const MobileItemAddRole = () => [
  { key: 'merger', label: fm2('ShareCollaboration.admin'), icon: <MergerIcon /> },
  { key: 'reader', label: fm2('ShareCollaboration.readOnly'), icon: <EyeOutlined /> },
  { key: 'commentator', label: fm2('ShareCollaboration.comment'), icon: <CommentIcon /> },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit'), icon: <EditIcon /> },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess'), icon: <MinusCircleOutlined /> },
  { key: 'remove', label: fm2('ShareCollaboration.removePermission'), danger: true, icon: <DeleteIcon /> },
];
