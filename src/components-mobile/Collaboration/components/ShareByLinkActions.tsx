import { message } from 'antd';
import { Radio } from 'antd-mobile';
import type { RadioValue } from 'antd-mobile/es/components/radio';
import classNames from 'classnames';
import { useEffect, useState } from 'react';

import { OptionPermission, optionsDays } from '@/components/Collaboration/components';
import { updateShareExpire } from '@/components/Collaboration/utils';
import { fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { ShareContentDefault, ShareContentMedium } from './ShareAction';
import styles from './ShareAction.less';
import { CustomSwitch, SmallRadio } from './ShareControllActions';

export function ShareByLinkSharingSwitch({
  checked,
  onChange,
}: {
  checked: boolean;
  onChange: (open: boolean) => void;
}) {
  const { share } = useCollaborationStore((state) => state);
  const disabled = share?.shareDisabled;

  function handleClick(value: boolean) {
    if (disabled) {
      message.error({ content: fm2('ShareCollaboration.notSupportShare') });
      return;
    }
    onChange(value);
  }

  return (
    <div className={styles.shareAction}>
      {checked ? (
        <ShareContentMedium title="通过链接添加协作（未开启）">获得链接的人可以加入协作</ShareContentMedium>
      ) : (
        <ShareContentDefault title="通过链接添加协作（未开启）">开启后，获得链接的人可以加入协作</ShareContentDefault>
      )}
      <CustomSwitch checked={checked} className={disabled ? styles.shareActionDisable : ''} onChange={handleClick} />
    </div>
  );
}

export function ShareByLinkExpirySettings() {
  const { share, guid, collaborationData } = useCollaborationStore((state) => state);

  const [value, setValue] = useState(7);

  function handleChange(value: RadioValue) {
    updateShareExpire(guid, value as number);
    setValue(value as number);
  }

  useEffect(() => {
    const shareModeExpireDuration = collaborationData?.shareModeExpireDuration;
    const expireDays = shareModeExpireDuration ? Math.floor(shareModeExpireDuration / 86400) : undefined;

    setValue(expireDays || 7);
  }, [collaborationData?.shareModeExpireDuration]);

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接有效期" />
      </div>
      <div className={styles.linkExpirySettingsExtra}>
        <Radio.Group disabled={share?.shareDisabled} value={value} onChange={handleChange}>
          {optionsDays().map((item) => (
            <SmallRadio key={item.value} label={item.label} value={item.value} />
          ))}
        </Radio.Group>
      </div>
    </div>
  );
}

export function LinkPermissionSettings() {
  const { share, collaborationData } = useCollaborationStore((state) => state);

  const [value, setValue] = useState('editor');

  function handleChange(value: RadioValue) {
    setValue(value as string);
  }

  useEffect(() => {
    const shareMode = collaborationData?.shareMode;

    setValue(shareMode || 'editor');
  }, [collaborationData?.shareMode]);

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接权限" />
      </div>
      <div className={styles.linkExpirySettingsExtra}>
        <Radio.Group disabled={share?.shareDisabled} value={value} onChange={handleChange}>
          {OptionPermission().map((item) => (
            <SmallRadio key={item.value} label={item.label} value={item.value} />
          ))}
        </Radio.Group>
      </div>
    </div>
  );
}
