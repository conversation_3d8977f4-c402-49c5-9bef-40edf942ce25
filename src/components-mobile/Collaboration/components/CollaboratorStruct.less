.collaboratorStruct {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--theme-layout-color-bg-editor);
}

.searchSection {
  padding: 10px 17px;
  background-color: var(--theme-basic-color-bg-default);
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin-top: 17px;
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);
}

.searchIcon {
  width: 20px;
  height: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.searchInput {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: var(--theme-text-color-default);

  &::placeholder {
    color: var(--theme-text-color-secondary);
  }
}

.treeContainer {
  flex: 1;
  overflow-y: auto;
  background: var(--theme-basic-color-bg-default);
}

.treeItem {
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.expandIcon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: var(--theme-text-color-secondary);
  cursor: pointer;

  &:hover {
    color: var(--theme-text-color-default);
  }
}

.departmentName.departmentName {
  margin-left: 8px;
}

.content.content:last-child {
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}
