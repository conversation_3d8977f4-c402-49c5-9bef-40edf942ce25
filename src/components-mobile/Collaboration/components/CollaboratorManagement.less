.collaboratorManagement {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 11.5px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  position: relative;
}

.headerLeft {
  width: 24px;
  display: flex;
  justify-content: center;
  color: var(--theme-text-color-default);
}

.backIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  justify-content: center;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-color-default);
  position: absolute;
  left: 60px;
  right: 60px;
  display: flex;
  justify-content: center;
}

.content {
  background: var(--theme-layout-color-bg-editor);
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.searchSection {
  padding: 10px 17px;
  background-color: var(--theme-basic-color-bg-default);
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin-top: 17px;
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);
}

.searchIcon {
  width: 20px;
  height: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.addOptions {
  margin-top: 20px;
  background-color: var(--theme-basic-color-bg-default);
}

.addOption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.addOptionLeft {
  display: flex;
  align-items: center;
}

.addOptionIcon {
  width: 32px;
  height: 32px;
}

.addOptionText {
  margin-left: 12px;
  font-size: 14px;
  color: var(--theme-text-color-default);
}

.addOptionArrow {
  width: 16px;
  height: 16px;
}

.section {
  background-color: var(--theme-basic-color-bg-default);
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.sectionTitleWithIcon {
  display: flex;
  align-items: center;
}

.sectionTitle {
  font-size: 12px;
  color: var(--theme-text-color-default);
}

.userItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}

.userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex: none;
}

.userDetails {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
  color: var(--theme-text-color-default);
  overflow: hidden;
}

.userName {
  font-size: 14px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-word;
}

.userEmail {
  font-size: 12px;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-word;
}

.userRole {
  font-size: 12px;
  color: var(--theme-text-color-disabled);
}

.emptyState {
  padding: 5px 16px;
  // border-top: 1px solid var(--theme-separator-color-lighter);
  // border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.emptyText {
  font-size: 12px;
  color: var(--theme-text-color-disabled);
}

.modal {
  :global(.adm-modal-content) {
    padding: 0 16px 20px;
    border-bottom: 1px solid var(--theme-separator-color-lighter);
  }

  :global(.adm-modal-footer) {
    padding-top: 11.5px;
  }

  :global(.adm-button:not(.adm-button-default).adm-button-fill-none) {
    color: var(--theme-basic-color-notice);
  }
}

.itemButton {
  color: var(--theme-text-color-guidance);
  font-size: 12px;
  padding: 5px 6px;
  // border: 1px solid var(--theme-basic-color-lighter);
  // border-radius: 2px;
}

.userRoleIcon {
  margin-left: 6px;
  width: 16px;
  height: 16px;
  justify-content: center;
}

.watchUser {
  font-size: 12px;
  color: var(--theme-text-color-guidance);
}

.addCollaboratorFooter {
  display: flex;
  flex: none;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.addCollaboratorTabs {
  flex: 1;
  overflow: hidden;

  :global(.adm-tabs-tab) {
    font-size: 13px;
    color: var(--theme-text-color-deep);
  }

  :global(.adm-tabs-tab-line) {
    background: var(--theme-chart-tip-color-text);
  }

  :global(.adm-tabs-content) {
    padding: 0;
    height: calc(100% - 36px);
  }
}

.addCollaboratorFooterText {
  font-size: 14px;
  color: var(--theme-text-color-default);
}

.searchContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.searchBarContainer {
  padding: 6px 10px;
  background: #f7f7f7;

  :global(.adm-search-bar) {
    height: 28px;
  }

  :global(.adm-search-bar .adm-search-bar-input-box) {
    border: 0;
    border-radius: 2px;
    background: #fff;
  }

  :global(.adm-search-bar-suffix) {
    margin-left: 8px;
  }

  :global(.adm-search-bar .adm-search-bar-cancel-button.adm-button) {
    color: var(---Default100, #41464b);
    font-size: 13px;
    padding: 4px 6px;
  }
}

.searchReultContainer {
  flex: 1;
  overflow: auto;
}

.searchEmpty {
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中（可选） */
  height: 100%;
}

.searchEmptyContent {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.searchEmptyText {
  margin-top: 10px;
  color: var(--theme-text-color-disabled);
  font-size: 12px;
  line-height: 20px;
}
