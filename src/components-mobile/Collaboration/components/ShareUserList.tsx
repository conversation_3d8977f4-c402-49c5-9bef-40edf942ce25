import { Avatar } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationList } from '@/api/Collaboration';
import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm, fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { AddCollaborator } from './AddCollaborator';
import { CollaboratorManagement } from './CollaboratorManagement';
import { CollaboratorManagementPopup } from './CollaboratorManagementPopup';
import { ParentDirectoryCollaboratorList } from './ParentDirectoryCollaboratorList';
import styles from './ShareUserList.less';

function AvatarList({ avatars }: { avatars: CollaboratorsData['roles'] }) {
  return (
    <div className={styles['shareUserList-avatars']}>
      {avatars.slice(0, 5).map((item) => {
        if (item.departmentId) {
          return <Organization key={item.id} height={24} width={24} />;
        }
        return <Avatar key={item.id} src={item.avatar} />;
      })}
    </div>
  );
}

export const getPopInfo = () =>
  ({
    index: {
      title: fm2('ShareCollaboration.collaborator'),
      type: 'index',
    },
    parent: {
      title: fm2('ShareCollaborationMobile.parentDirectoryCollaborator'),
      type: 'parent',
    },
    add: {
      title: fm2('ShareCollaboration.collaborator'),
      type: 'add',
    },
  }) as const;

export type CloseInfo = {
  type: keyof ReturnType<typeof getPopInfo>;
  extra?: string;
};

export function ShareUserList({ guid }: { guid: string }) {
  const { collaborators: data, setCollaborators: setData } = useCollaborationStore((state) => state);

  const { isOpen, open, close } = useDisclosure(false);
  const [popInfo, setPopInfo] = useState<ReturnType<typeof getPopInfo>[keyof ReturnType<typeof getPopInfo>]>(
    getPopInfo().index,
  );
  const [parentId, setParentId] = useState<string>();

  function handleClose({ type, extra }: CloseInfo) {
    setPopInfo(getPopInfo()[type]);
    if (extra) {
      setParentId(extra);
    }
  }

  function handlePopClose() {
    if (popInfo.type === getPopInfo().index.type) {
      close();
    } else {
      setPopInfo(getPopInfo().index);
    }
  }

  useEffect(() => {
    if (guid && popInfo.type === getPopInfo().index.type) {
      getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
        setData(res.data);
      });
    }
  }, [guid, popInfo.type, setData]);

  const hasCollaborators = useMemo(() => data?.roles && data.roles.length > 0, [data]);

  return (
    <>
      <div className={styles.shareUserList} onClick={open}>
        <div className={styles['shareUserList-content']}>
          {hasCollaborators ? (
            <AvatarList avatars={data!.roles} />
          ) : (
            <div className={styles['shareUserList-empty']}>
              <NoDataIcon className={styles['shareUserList-empty-icon']} />
              {fm('ShareCollaboration.noRoles')}
            </div>
          )}
        </div>
        <div className={styles['shareUserList-action']}>
          {fm('ShareCollaboration.addRoles')}
          <ArrowRight />
        </div>
      </div>
      <CollaboratorManagementPopup isOpen={isOpen} title={popInfo.title} onClose={handlePopClose}>
        {popInfo.type === getPopInfo().index.type && <CollaboratorManagement data={data} showOtherPop={handleClose} />}
        {popInfo.type === getPopInfo().parent.type && <ParentDirectoryCollaboratorList guid={parentId!} />}
        {popInfo.type === getPopInfo().add.type && <AddCollaborator />}
      </CollaboratorManagementPopup>
    </>
  );
}
