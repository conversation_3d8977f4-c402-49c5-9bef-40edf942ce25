import { Modal } from 'antd-mobile';
import { useState } from 'react';

import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as AvatarBlueIcon } from '@/assets/images/svg/avatar-blue.svg';
import { ReactComponent as LinkShareIcon } from '@/assets/images/svg/link-share.svg';
import { ReactComponent as QuestionCircleIcon } from '@/assets/images/svg/question-circle.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm, fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { AdminList, CollaboratorList } from './CollaboratorItem';
import styles from './CollaboratorManagement.less';
import { SearchPopup } from './SearchPopup';
import { ShareByLink } from './ShareByLink';
import type { CloseInfo } from './ShareUserList';
import { getPopInfo } from './ShareUserList';

interface CollaboratorManagementProps {
  data?: CollaboratorsData;
  showOtherPop: ({ type, extra }: CloseInfo) => void;
}

export function SectionHeader({ title, onClick }: { title: string; onClick?: () => void }) {
  return (
    <div className={styles.sectionHeader}>
      <div className={styles.sectionTitleWithIcon} onClick={onClick}>
        <span className={styles.sectionTitle}>{title}</span>
        <QuestionCircleIcon />
      </div>
    </div>
  );
}

export function CollaboratorManagement({ data, showOtherPop }: CollaboratorManagementProps) {
  const { guid, share } = useCollaborationStore((state) => state);

  const { isOpen, open, close } = useDisclosure(false);

  const [visible, setVisible] = useState(false);
  const [modalInfo, setModalInfo] = useState({
    title: '',
    content: '',
  });
  const [checked, setChecked] = useState(!share?.shareDisabled);

  const handleAddFromContacts = () => {
    if (!share?.canManageAdmin) {
      return;
    }
    showOtherPop({
      type: getPopInfo().add.type,
    });
  };

  const handleOpen = () => {
    if (!share?.canManageAdmin) {
      return;
    }
    open();
  };

  const handleParentDirectoryClick = (id: string) => {
    showOtherPop({
      type: 'parent',
      extra: id,
    });
  };

  const openAdminInfo = () => {
    setModalInfo({
      title: fm2('ShareCollaborationMobile.adminTitle'),
      content: fm2('ShareCollaborationMobile.adminDescription'),
    });
    setVisible(true);
  };

  const openRolesInfo = () => {
    setModalInfo({
      title: fm2('ShareCollaborationMobile.collaboratorTitle'),
      content: fm2('ShareCollaborationMobile.collaboratorDescription'),
    });
    setVisible(true);
  };

  return (
    <>
      {/* 搜索框 */}
      <SearchPopup />

      {/* 添加协作者选项 */}
      <div className={styles.addOptions}>
        <div className={styles.addOption} onClick={handleAddFromContacts}>
          <div className={styles.addOptionLeft}>
            <AvatarBlueIcon className={styles.addOptionIcon} />
            <span className={styles.addOptionText}>{fm('ShareCollaboration.addFromContacts')}</span>
          </div>
          <ArrowRight className={styles.addOptionArrow} />
        </div>

        <div className={styles.addOption} onClick={handleOpen}>
          <div className={styles.addOptionLeft}>
            <LinkShareIcon className={styles.addOptionIcon} />
            <span className={styles.addOptionText}>{fm('ShareCollaboration.shareByLink')}</span>
          </div>
          <ArrowRight className={styles.addOptionArrow} />
        </div>
      </div>

      <SectionHeader title={fm('ShareCollaboration.administrators')} onClick={openAdminInfo} />

      {/* 管理者部分 */}
      <AdminList data={data?.admins} />

      <SectionHeader title={fm('ShareCollaboration.collaborators')} onClick={openRolesInfo} />

      {/* 协作者部分 */}
      <CollaboratorList data={data?.roles} open={handleParentDirectoryClick} />

      <Modal
        closeOnAction
        actions={[
          {
            key: 'confirm',
            text: '知道了',
          },
        ]}
        bodyClassName={styles.modal}
        content={
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                fontSize: '16px',
                fontWeight: 500,
                color: 'var(--theme-text-color-header)',
                marginBottom: '10px',
              }}
            >
              {modalInfo.title}
            </div>
            <div
              style={{
                fontSize: '14px',
                color: 'var(--theme-text-color-medium)',
                textAlign: 'center',
                lineHeight: '20px',
              }}
            >
              {modalInfo.content}
            </div>
          </div>
        }
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
      />
      <ShareByLink guid={guid} open={checked} setOpen={setChecked} visible={isOpen} onClose={close} />
    </>
  );
}
