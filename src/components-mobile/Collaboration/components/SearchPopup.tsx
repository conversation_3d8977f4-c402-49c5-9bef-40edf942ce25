import { Popup, SearchBar } from 'antd-mobile';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationList, getSearchUser } from '@/api/Collaboration';
import { ReactComponent as SearchIcon } from '@/assets/images/svg/search.svg';
import { ReactComponent as SearchEmptyIcon } from '@/assets/images/svg/search-empty.svg';
import { isDisabledAdmin } from '@/components/Collaboration/components';
import { fm2 } from '@/modules/Locale';

import type { FrontRole, TreeNode } from '../hooks/useUserRole';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { CollarboratorCard } from './Collaborator';
import styles from './CollaboratorManagement.less';
import { CollaboratorDeptUerItem } from './CollaboratorUserItem';
import { MobileItemAddRole } from './const';

export function SearchPopup() {
  const { guid, collaborationData } = useCollaborationStore((state) => state);

  const [visible, setVisible] = useState(false);
  const [input, setInput] = useState('');
  const [searchResult, setSearchResult] = useState('');
  const [data, setData] = useState<TreeNode[]>([]);

  const handleSearch = () => {
    setVisible(true);
  };

  const debouncedSearch = useMemo(
    () =>
      debounce(async (value) => {
        if (value.trim() !== '') {
          const [userDataResponse, searchRes] = await Promise.all([
            getCollaborationList(guid, { includeInherited: false, includeAdmin: true }),
            getSearchUser({
              limit: 100,
              keyword: value,
              filter: { user: { includeRecentContact: true, includeTeamMember: true }, department: {}, group: {} },
              fetchFileRoleByFileGuid: guid,
            }),
          ]);

          const roleAdmins = [
            ...userDataResponse.data.roles.map((item) => ({
              ...item,
              frontRole: 'collaborator',
            })),
            ...userDataResponse.data.admins.map((item) => ({
              ...item,
              frontRole: 'admin',
            })),
          ];

          const sorted = searchRes.data.results.sort((a, b) => {
            if (a.type === b.type) return 0;
            if (a.type === 'department') return -1;
            return 1;
          });

          const roleList = sorted.map((item) => {
            if (item.type === 'department') {
              const role = roleAdmins.find((role) => role.id === item.department!.id);
              const frontRole = (role ? role.frontRole : 'none') as FrontRole;

              return {
                id: item.department!.id,
                name: item.department!.name,
                type: 'department' as const,
                level: 0,
                displayText: MobileItemAddRole().find((k) => k.key === role?.role || 'none')!.label,
                showBtn: role ? !isDisabledAdmin(collaborationData!, role, roleAdmins) : true,
                icon: false,
                frontRole,
              };
            }
            const role = roleAdmins.find((role) => role.id === item.user!.id);
            const frontRole = (role ? role.frontRole : 'none') as FrontRole;

            return {
              id: item.user!.id,
              name: item.user!.name,
              type: 'user' as const,
              avatar: item.user!.avatar,
              level: 0,
              displayText: MobileItemAddRole().find((k) => k.key === role?.role || 'none')!.label,
              showBtn: role ? !isDisabledAdmin(collaborationData!, role, roleAdmins) : true,
              icon: false,
              frontRole,
            };
          });

          setData(roleList);
          setSearchResult(value);
        }
      }, 300),
    [collaborationData, guid],
  );

  useEffect(() => {
    if (input) {
      debouncedSearch(input);
    } else {
      setSearchResult('');
      setData([]);
    }
  }, [input, debouncedSearch]);

  return (
    <>
      <div className={styles.searchSection} onClick={handleSearch}>
        <div className={styles.searchIcon}>
          <SearchIcon />
        </div>
        <span className={styles.searchPlaceholder}>{fm2('ShareCollaborationMobile.searchPlaceholder')}</span>
      </div>
      <Popup
        destroyOnClose
        bodyStyle={{ height: searchResult ? '100%' : 'auto' }}
        position="top"
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <div className={styles.searchContainer}>
          <div className={styles.searchBarContainer}>
            <SearchBar
              autoFocus
              placeholder={fm2('ShareCollaborationMobile.searchPlaceholder')}
              searchIcon={<SearchIcon />}
              showCancelButton={() => true}
              value={input}
              onCancel={() => {
                setVisible(false);
              }}
              onChange={setInput}
            />
          </div>
          {searchResult && (
            <div className={styles.searchReultContainer}>
              {data.length > 0 ? (
                <CollarboratorCard>
                  {data.map((item) => (
                    <CollaboratorDeptUerItem key={item.id} node={item} />
                  ))}
                </CollarboratorCard>
              ) : (
                <div className={styles.searchEmpty}>
                  <div className={styles.searchEmptyContent}>
                    <SearchEmptyIcon />
                    <span className={styles.searchEmptyText}>{fm2('ShareCollaborationMobile.noSearchResult')}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Popup>
    </>
  );
}
