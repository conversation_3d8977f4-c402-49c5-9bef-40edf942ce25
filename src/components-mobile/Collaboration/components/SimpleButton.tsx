import { Button } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { fm, fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { copyLink, handleCopyLinkShare } from '../utils/mobile';
import styles from './ShareAction.less';

export function SimpleButton({
  onClick,
  children,
  disabled,
  ...rest
}: { onClick: () => void; children: React.ReactNode; disabled?: boolean } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={styles.simple} {...rest}>
      <Button className={styles.simpleButton} disabled={disabled} fill="none" onClick={onClick}>
        {children}
      </Button>
    </div>
  );
}

export function CopyLink({ onClick, disabled }: { onClick: () => void; disabled?: boolean }) {
  const copyText = fm('ShareCollaborationMobile.copyLink');

  const { collaborationData: data, share } = useCollaborationStore((state) => state);
  const [text, setText] = useState(copyText);

  function handleClick() {
    if (data?.url && data?.name) {
      const url = copyLink({ url: data.url, name: data.name });
      const copyUrl = share?.passwordStatus
        ? `${url} ${fm2('ShareCollaboration.accessPassword')} ${share?.password}`
        : url;
      handleCopyLinkShare(copyUrl);
      onClick();
    }
  }

  useEffect(() => {
    if (share?.passwordStatus) {
      setText(fm2('ShareCollaborationMobile.copyLinkAndPassword'));
    } else {
      setText(copyText);
    }
  }, [copyText, share?.passwordStatus]);

  return (
    <SimpleButton disabled={disabled} onClick={handleClick}>
      {text}
    </SimpleButton>
  );
}

export function CloseAction({
  onClick,
  title,
  ...rest
}: {
  onClick: () => void;
  title?: string;
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <SimpleButton onClick={onClick} {...rest}>
      {title || fm2('ShareCollaborationMobile.close')}
    </SimpleButton>
  );
}
