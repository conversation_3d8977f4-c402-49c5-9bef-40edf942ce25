import { useUserRole } from '../hooks/useUserRole';
import styles from './CollaboratorStruct.less';
import { CollaboratorDeptTreeItem } from './CollaboratorUserItem';

export function CollaboratorStruct() {
  const { rootDepartments, childrenDepartments, loadDepartmentData } = useUserRole();

  return (
    <div className={styles.collaboratorStruct}>
      {/* 组织架构树 */}
      <div className={styles.treeContainer}>
        {rootDepartments && (
          <div key={rootDepartments.id} className={styles.treeItem}>
            <CollaboratorDeptTreeItem
              defaultExpanded
              defaultChildrenData={childrenDepartments}
              loadDepartmentData={loadDepartmentData}
              node={rootDepartments}
            />
          </div>
        )}
      </div>
    </div>
  );
}
