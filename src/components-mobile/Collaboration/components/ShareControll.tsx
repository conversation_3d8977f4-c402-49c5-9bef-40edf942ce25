import { ActionSheet } from 'antd-mobile';
import { useCallback, useMemo } from 'react';

import { CloseList } from '@/components/Collaboration/components/SelectOptions';
import type { CollaborationData } from '@/components/Collaboration/types';
import { handleUpdateShareStatus } from '@/components/Collaboration/utils';
import { fm2 } from '@/modules/Locale';

import styles from '../CollaborationShareMobile.less';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { LinkExpirySettings, LinkPasswordSettings, LinkSharingSwitch, ShareSettings } from './ShareControllActions';

interface IProps {
  visible: boolean;
  toggle: () => void;
  onClose: () => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  guid: string;
}

export function ShareControll({ visible, toggle, guid, onClose, open, setOpen }: IProps) {
  const { setCollaborationData, collaborationData } = useCollaborationStore((state) => state);

  const handleShare = useCallback(
    (status: boolean) => {
      const shareMode = status ? CloseList[0].value : 'private';
      handleUpdateShareStatus(guid, shareMode);
      setOpen(status);
      setCollaborationData({ ...(collaborationData as CollaborationData), shareMode });
    },
    [collaborationData, guid, setCollaborationData, setOpen],
  );

  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: <LinkSharingSwitch checked={open} onChange={handleShare} />,
      },
      ...(open
        ? [
            {
              key: 'shareSettings',
              text: <ShareSettings toggleParentVisible={toggle} />,
            },
            {
              key: 'linkExpirySettings',
              text: <LinkExpirySettings />,
            },
            {
              key: 'linkPasswordSettings',
              text: <LinkPasswordSettings />,
            },
          ]
        : []),
    ];
  }, [handleShare, open, toggle]);

  return (
    <ActionSheet
      actions={actions}
      cancelText={fm2('ShareCollaborationMobile.close')}
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
