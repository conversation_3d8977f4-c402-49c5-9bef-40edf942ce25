import { CaretDownOutlined, CheckOutlined } from '@ant-design/icons';
import { Button, Dropdown, Space } from 'antd';
import React from 'react';

import { ReactComponent as DarkPlusSign } from '@/assets/images/svg/darkPlusSign.svg';
import { itemsAdmin } from '@/components/Collaboration/components';
import type { DataItem } from '@/components/Collaboration/types';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

interface DropdownMenuItemAdminProps {
  dropdownChangeAdmin: (itemData: DataItem, info?: { key: string }, type?: string) => void;
  itemData: DataItem;
  type?: string;
}
interface ItemAdmin {
  key: string;
  danger?: boolean;
  label: string;
}

const DropdownMenuItemAdmin: React.FC<DropdownMenuItemAdminProps> = ({ dropdownChangeAdmin, itemData, type }) => {
  const meId = useMeStore((state) => state.me.id);
  return (
    <Dropdown
      menu={{
        items: itemsAdmin().map((item: ItemAdmin) => ({
          key: item.key,
          danger: item.danger,
          label: (
            <div className="dropdownItem">
              <div>{item.label}</div>
              {item.key === 'merger' && <CheckOutlined />}
            </div>
          ),
        })),
        onClick: (info) => dropdownChangeAdmin(itemData, info, type ?? ''),
        style: { width: 200 },
      }}
      placement="bottomRight"
      trigger={itemData?.isAdmin ? ['click'] : []}
    >
      <Space>
        <Button
          disabled={itemData?.isInherited || meId === itemData?.id || meId === itemData?.user?.id}
          icon={itemData?.isInherited || itemData?.isAdmin || itemData?.role ? <CaretDownOutlined /> : <DarkPlusSign />}
          iconPosition="end"
          size="small"
          type="text"
          onClick={() => {
            dropdownChangeAdmin(itemData, undefined, type ?? '');
          }}
        >
          {itemData?.isAdmin ? fm2('ShareCollaboration.admin') : fm2('ShareCollaboration.setManager')}
        </Button>
      </Space>
    </Dropdown>
  );
};

export default DropdownMenuItemAdmin;
