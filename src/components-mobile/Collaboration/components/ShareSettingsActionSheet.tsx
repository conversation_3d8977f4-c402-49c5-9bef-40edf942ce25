import { CheckOutlined } from '@ant-design/icons';
import { ActionSheet } from 'antd-mobile';
import { useCallback, useMemo, useState } from 'react';

import type { ShareMode } from '@/components/Collaboration/components/SelectOptions';
import { CloseList, OpenList } from '@/components/Collaboration/components/SelectOptions';
import { handleUpdateShareStatus } from '@/components/Collaboration/utils';
import { fm2 } from '@/modules/Locale';

import styles from '../CollaborationShareMobile.less';
import { useCollaborationStore } from '../store/CollaboratorManagement';
import { ShareSettings } from './ShareAction';

export function ShareSettingsActionSheet({ visible, onClose }: { visible: boolean; onClose: () => void }) {
  const { collaborationData, share } = useCollaborationStore((state) => state);
  const [shareMode, setShareMode] = useState<ShareMode | undefined>(collaborationData?.shareMode || CloseList[0].value);

  const handleShareModeChange = useCallback(
    (shareMode: ShareMode) => {
      setShareMode(shareMode);
      handleUpdateShareStatus(collaborationData!.guid!, shareMode);
      onClose();
    },
    [collaborationData, onClose],
  );

  const actions = useMemo(() => {
    const list = share?.passwordStatus ? OpenList : CloseList;

    return list.map((item) => ({
      key: item.value,
      text: (
        <ShareSettings
          highlightText={item.highlightText}
          title={item.text()}
          onClick={() => handleShareModeChange(item.value)}
        >
          {shareMode === item.value && <CheckOutlined />}
        </ShareSettings>
      ),
    }));
  }, [handleShareModeChange, share?.passwordStatus, shareMode]);

  return (
    <ActionSheet
      actions={actions}
      cancelText={fm2('ShareCollaboration.cancel')}
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
