import { message } from 'antd';
import type { SwitchProps } from 'antd-mobile';
import { Radio, Switch } from 'antd-mobile';
import type { RadioValue } from 'antd-mobile/es/components/radio';
import classNames from 'classnames';
import { useEffect, useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as Refresh } from '@/assets/images/svg/refresh-no-color.svg';
import { optionsDays } from '@/components/Collaboration/components';
import { updateShareExpire, updateSharePasswordAndGet } from '@/components/Collaboration/utils';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm2 } from '@/modules/Locale';

import { useCollaborationStore } from '../store/CollaboratorManagement';
import { ShareContentDefault, ShareContentMedium } from './ShareAction';
import styles from './ShareAction.less';
import { ShareSettingsActionSheet } from './ShareSettingsActionSheet';

export function CustomSwitch(props: SwitchProps) {
  return (
    <Switch
      {...props}
      style={{
        '--checked-color': 'var(--theme-link-button-color)',
        '--height': '24px',
        '--width': '42px',
      }}
    />
  );
}

export function LinkSharingSwitch({ checked, onChange }: { checked: boolean; onChange: (open: boolean) => void }) {
  const { share } = useCollaborationStore((state) => state);
  const disabled = share?.shareDisabled;

  function handleClick(value: boolean) {
    if (disabled) {
      message.error({ content: fm2('ShareCollaboration.notSupportShare') });
      return;
    }
    onChange(value);
  }

  return (
    <div className={styles.shareAction}>
      {checked ? (
        <ShareContentMedium title="链接分享已开启" />
      ) : (
        <ShareContentDefault title="链接分享已关闭">文件的协作者和管理员仍可访问</ShareContentDefault>
      )}
      <CustomSwitch checked={checked} className={disabled ? styles.shareActionDisable : ''} onChange={handleClick} />
    </div>
  );
}

export function ShareSettings({ toggleParentVisible }: { toggleParentVisible: () => void }) {
  const { isOpen, open, close } = useDisclosure(false);
  const { share } = useCollaborationStore((state) => state);
  const disabled = share?.shareDisabled;

  function handleClick() {
    if (disabled) {
      return;
    }
    open();
    toggleParentVisible();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        <ShareContentMedium title="分享设置">
          企业内获得链接的人<span className={styles.shareActionTextSpan}>只能阅读</span>
        </ShareContentMedium>
        <ArrowRight className={disabled ? styles.shareActionDisable : ''} />
      </div>
      <ShareSettingsActionSheet visible={isOpen} onClose={handleClose} />
    </>
  );
}

export function SmallRadio({ value, label }: { value: RadioValue; label: string }) {
  return (
    <Radio
      style={{
        '--icon-size': '12px',
        '--font-size': '12px',
        '--gap': '9px',
      }}
      value={value}
    >
      {label}
    </Radio>
  );
}

export function LinkExpirySettings() {
  const { share, guid, collaborationData } = useCollaborationStore((state) => state);

  const [checked, setChecked] = useState(share?.shareTimeStatus);
  const [value, setValue] = useState(30);

  function handleSwitchChange(checked: boolean) {
    updateShareExpire(guid, checked ? 30 : 0);
    setChecked(checked);
  }

  function handleChange(value: RadioValue) {
    updateShareExpire(guid, value as number);
    setValue(value as number);
  }

  useEffect(() => {
    setChecked(share?.shareTimeStatus);
    const shareModeExpireDuration = collaborationData?.shareModeExpireDuration;
    const expireDays = shareModeExpireDuration ? Math.floor(shareModeExpireDuration / 86400) : undefined;

    setValue(expireDays || 30);
  }, [share?.shareTimeStatus, collaborationData?.shareModeExpireDuration]);

  return (
    <div className={classNames(styles.linkExpirySettings)}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接有效期">{checked ? '' : '已关闭，链接永久有效'}</ShareContentMedium>
        <CustomSwitch checked={checked} disabled={share?.shareDisabled} onChange={handleSwitchChange} />
      </div>
      {checked && (
        <div className={styles.linkExpirySettingsExtra}>
          <Radio.Group disabled={share?.shareDisabled} value={value} onChange={handleChange}>
            {optionsDays().map((item) => (
              <SmallRadio key={item.value} label={item.label} value={item.value} />
            ))}
          </Radio.Group>
        </div>
      )}
    </div>
  );
}

export function LinkPasswordSettings() {
  const { share, guid, setShare } = useCollaborationStore((state) => state);

  async function handleChangePassword() {
    if (!share?.shareDisabled) {
      const newPassword = await updateSharePasswordAndGet(guid, true, true);
      if (newPassword !== null) {
        setShare({
          ...share,
          password: newPassword,
          passwordStatus: true,
        });
      }
    }
  }

  async function handleSwitchChange(checked: boolean) {
    if (!share?.shareDisabled) {
      const newPassword = await updateSharePasswordAndGet(guid, checked, false);
      setShare({
        ...share,
        password: newPassword || '',
        passwordStatus: checked,
      });
    }
  }

  return (
    <div className={styles.linkExpirySettings}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接密码" />
        <CustomSwitch checked={share?.passwordStatus} disabled={share?.shareDisabled} onChange={handleSwitchChange} />
      </div>
      {share?.passwordStatus && (
        <div className={styles.linkExpirySettingsExtra}>
          <div className={styles.linkPassword}>密码: {share?.password}</div>
          <div className={styles.changePassword} onClick={handleChangePassword}>
            <Refresh className={styles.refreshIcon} />
            更换密码
          </div>
        </div>
      )}
    </div>
  );
}
