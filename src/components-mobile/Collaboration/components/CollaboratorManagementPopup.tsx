import { LeftOutlined } from '@ant-design/icons';
import { Popup } from 'antd-mobile';

import styles from './CollaboratorManagement.less';

interface CollaboratorManagementPopupProps {
  title: string;
  onClose: () => void;
  children: React.ReactNode;
  isOpen: boolean;
}

export function CollaboratorManagementPopup({ title, isOpen, onClose, children }: CollaboratorManagementPopupProps) {
  return (
    <Popup
      bodyStyle={{ height: '100%', borderTopLeftRadius: 0, borderTopRightRadius: 0 }}
      visible={isOpen}
      onClose={close}
      onMaskClick={close}
    >
      <div className={styles.collaboratorManagement}>
        {/* 标题栏 */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <LeftOutlined className={styles.backIcon} height={24} width={24} onClick={onClose} />
          </div>
          <div className={styles.title}>{title}</div>
        </div>

        <div className={styles.content}>{children}</div>
      </div>
    </Popup>
  );
}
