.shareAction {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &Content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    &TitleDefault {
      font-size: 13px;
      color: var(--theme-text-color-default);
    }

    &Text {
      font-size: 12px;
      color: var(--theme-text-color-secondary);
      margin-top: 5px;
    }
  }

  &Title {
    font-size: 13px;
    color: var(--theme-text-color-medium);
  }

  &TextSpan {
    color: var(--theme-text-color-guidance);
  }
}

.simple {
  padding: 10px;
  text-align: center;
  color: var(--theme-text-color-medium);

  &But<PERSON> {
    width: 100%;
    font-size: 14px;
    color: var(--theme-chart-tip-color-text);
    padding: 0;
  }
}

.shareSettings {
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shareSettingsContent {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}

.shareSettingsTitle {
  font-size: 14px;
  color: var(--theme-chart-tip-color-text);
}

.shareSettingsTextSpan {
  font-size: 14px;
  color: var(--theme-text-color-guidance);
}

.shareSettingsDisable {
  .shareSettingsTitle,
  .shareSettingsTextSpan {
    color: var(--theme-text-color-disabled);
  }
}

.linkExpirySettings {
  padding: 10px 15px;
}

.linkExpirySettingsContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.linkExpirySettingsExtra {
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.linkPassword {
  font-size: 13px;
  color: var(--theme-text-color-medium);
}

.changePassword {
  font-size: 12px;
  color: var(--theme-text-color-guidance);
  display: flex;
}

.refreshIcon {
  margin-right: 4px;
}

.shareActionDisable {
  opacity: 0.5;
}
