.shareUserList {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;

  &-avatars {
    display: flex;
    align-items: center;

    :global(.adm-avatar) {
      width: 24px;
      height: 24px;
      margin-left: -4px;
      border-radius: 50%;
      border: 1px solid var(--theme-text-color-white);

      &:first-child {
        margin-left: 0;
      }
    }
  }

  &-empty {
    color: var(--theme-text-color-disabled);
    font-size: 12px;
    display: flex;
    align-items: center;

    &-icon {
      margin-right: 2px;
      width: 21px;
      height: 21px;
      padding: 1.5px;
    }
  }

  &-action {
    color: var(--theme-text-color-secondary);
    font-size: 10px;
    display: flex;
  }
}
