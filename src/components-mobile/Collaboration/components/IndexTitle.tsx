import { ReactComponent as ArrowLeft22 } from '@/assets/images/svg/arrowLeft22.svg';
import { fm2 } from '@/modules/Locale';
interface DataProps {
  role?: string;
}
interface TitleProps {
  data?: DataProps | null;
  listOpen?: boolean;
  enterType?: string;
  addOpen?: boolean;
  addAdminsOrRoles?: string;
  hanleBack?: () => void;
}

export const IndexTitle: React.FC<TitleProps> = ({
  data,
  listOpen,
  enterType,
  addOpen,
  addAdminsOrRoles,
  hanleBack,
}) => {
  return (
    <div>
      {data?.role && data?.role !== 'none' ? (
        <div onClick={hanleBack}>
          {!listOpen ? (
            <span>{fm2('ShareCollaboration.title')}</span>
          ) : (
            <div className="modlaArrow">
              {enterType === 'setting' ? null : <ArrowLeft22 />}
              {!addOpen ? (
                <span>{fm2('ShareCollaboration.back')}</span>
              ) : (
                <span>
                  {fm2('ShareCollaboration.add')}
                  {addAdminsOrRoles === 'roles' ? fm2('ShareCollaboration.coauthor') : fm2('ShareCollaboration.admin')}
                </span>
              )}
            </div>
          )}
        </div>
      ) : (
        <span>{fm2('ShareCollaboration.linkShare')}</span>
      )}
    </div>
  );
};
