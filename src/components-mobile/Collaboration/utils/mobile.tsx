import { CheckCircleOutlined } from '@ant-design/icons';
import { Toast } from 'antd-mobile';

import {
  addCollaboration,
  addCollaborationDepartment,
  deleteCollaboration,
  deleteCollaborationDepartment,
  setAdmin,
  setDepAdmin,
  updateCollaboration,
} from '@/api/Collaboration';
import { catchApiResult } from '@/api/Request';
import type { CollaborationData } from '@/components/Collaboration/types';
import { fm2 } from '@/modules/Locale';

import type { TreeNode } from '../hooks/useUserRole';

export const copyLinkUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      Toast.show({
        icon: <CheckCircleOutlined />,
        content: fm2('ShareCollaboration.copySuccess'),
      });
    })
    .catch(() => {
      Toast.show({
        icon: 'error',
        content: fm2('ShareCollaboration.copyFail'),
      });
    });
};

export const handleCopyLinkShare = (copyUrl: string) => {
  if (copyUrl) {
    copyLinkUrl(copyUrl);
  }
};

export const copyLink = (data?: { url: string; name: string }) => {
  if (data) {
    return `${new URL(data.url, window.location.href).href}/《${data.name}》`;
  } else {
    return '';
  }
};

export const isDepartment = (data?: { departmentId?: number }) => {
  return !!data?.departmentId;
};

export const isParentCollaborator = (data: CollaborationData | null) => {
  return data?.parentId === 0 ? undefined : data?.parentId;
};

export async function setDefaultPermission(guid: string, node: TreeNode, sendNotification: boolean) {
  // 部门和人员调不同的接口
  if (node.type === 'department') {
    await addCollaborationDepartment(guid, node.id, {
      role: 'editor',
      needNotice: sendNotification,
    });
  } else {
    await addCollaboration(guid, {
      userId: node.id,
      role: 'editor',
      needNotice: sendNotification,
    });
  }
}

// 协作者设置管理者权限
export async function setCollaboartionAdminAction({
  isDept,
  guid,
  id,
  sendNotification,
}: {
  isDept: boolean;
  guid: string;
  id: number;
  sendNotification: boolean;
}) {
  const fn = isDept ? setDepAdmin : setAdmin;
  const [err] = await catchApiResult(fn(guid, id, { needNotice: sendNotification }));
  if (err) {
    Toast.show({ icon: 'fail', content: err.data.msg });
  } else {
    Toast.show({ icon: 'success', content: '设置成功' });
  }
  return !err;
}

// 协作者移除管理者权限
export async function setCollaboartionRemoveAction({
  isDept,
  guid,
  id,
}: {
  isDept: boolean;
  guid: string;
  id: number;
}) {
  const fn = isDept ? deleteCollaborationDepartment : deleteCollaboration;
  const [err] = await catchApiResult(fn(guid, id));
  if (err) {
    Toast.show({ icon: 'fail', content: err.data.msg });
  } else {
    Toast.show({ icon: 'success', content: '移除成功' });
  }
  return !err;
}

// 协作者设置权限
export async function setCollaboartionPermissionAction({
  isDept,
  guid,
  id,
  key,
  sendNotification,
}: {
  isDept: boolean;
  guid: string;
  id: number;
  key: string;
  sendNotification: boolean;
}) {
  const fn = isDept ? addCollaborationDepartment : updateCollaboration;
  const [err] = await catchApiResult(fn(guid, id, { role: key, needNotice: sendNotification }));
  if (err) {
    Toast.show({ icon: 'fail', content: err.data.msg });
  } else {
    Toast.show({ icon: 'success', content: '添加成功' });
  }
  return !err;
}
