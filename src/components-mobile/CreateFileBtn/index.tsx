import { Button, Popup } from 'antd-mobile';
import { useMemo, useState } from 'react';

import addFileBtn from '@/assets/images/svg/addFileBtn.svg';
import type { FileItem } from '@/constants/fileList.config';
import { FILE_LIST_H5 } from '@/constants/fileList.config';
import { fm } from '@/modules/Locale';
import { useUserCheckPoint } from '@/service/Me';

import FileMenuList from './components/FileMenuList';
import styles from './index.less';

export default () => {
  const [visible, setVisible] = useState(false);
  const i18n_cancel = fm('SiderMenu.cancel');

  const { isNoFeaturePoint } = useUserCheckPoint();

  const fileMenuList = useMemo(() => {
    return FILE_LIST_H5.map((item: FileItem) => {
      item.hidden = item.supportType && isNoFeaturePoint(item.supportType || '');
      return item;
    });
  }, [isNoFeaturePoint]);
  return (
    <div>
      <img
        className={styles.fixedButton}
        src={addFileBtn}
        onClick={() => {
          setVisible(true);
        }}
      />

      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <FileMenuList config={fileMenuList} />
        <Button block className={styles.cancelBtn} onClick={() => setVisible(false)}>
          {i18n_cancel}
        </Button>
      </Popup>
    </div>
  );
};
