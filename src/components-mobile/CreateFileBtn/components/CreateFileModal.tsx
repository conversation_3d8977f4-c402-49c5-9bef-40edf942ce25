import { Input, Modal } from 'antd-mobile';
import classNames from 'classnames';
import { useState } from 'react';
import { createRoot } from 'react-dom/client';

import { fm2 } from '@/modules/Locale';

import styles from './CreateFileModal.less';

const i18n_cancel = fm2('SiderMenu.cancel');
const i18n_confirm = fm2('SiderMenu.confirm');
const i18n_inputName = fm2('SiderMenu.inputName');

interface CreateFileModalProps {
  open?: boolean;
  title?: string;
  content?: React.ReactNode;
  onClose?: () => void;
  onConfirm?: (filename: string) => void;
}

const CreateFileModal = ({ open, title, content, onClose = () => {}, onConfirm = () => {} }: CreateFileModalProps) => {
  const [filename, setFilename] = useState('');
  const [visible, setVisible] = useState(open);

  const handleClose = () => {
    setVisible(false);
    onClose();
  };

  const handleConfirm = () => {
    if (!title) return;
    onConfirm(filename.trim() || title);
    setFilename('');
    setVisible(false);
  };

  const modalContent = title ? (
    <div>
      <div className={styles.contentLayout}>
        <div className={styles.modalTitle}>{title}</div>
        <Input
          clearable
          className={styles.input}
          placeholder={i18n_inputName}
          value={filename}
          onChange={setFilename}
        />
      </div>
      <div className={styles.footerBtnLayout}>
        <div className={classNames(styles.btn)} onClick={() => handleClose()}>
          {i18n_cancel}
        </div>
        <div className={styles.divider} />
        <div
          className={classNames(styles.btn, styles.btnConfirm, {
            [styles.disabled]: !filename.trim(),
          })}
          onClick={() => filename.trim() && handleConfirm()}
        >
          {i18n_confirm}
        </div>
      </div>
    </div>
  ) : (
    content
  );

  return (
    <Modal
      closeOnMaskClick
      className={styles.createFileModal}
      content={modalContent}
      visible={visible}
      onClose={handleClose}
    />
  );
};

CreateFileModal.show = (props: CreateFileModalProps) => {
  const div = document.createElement('div');
  document.body.appendChild(div);
  const root = createRoot(div);

  const destroy = () => {
    root.unmount();
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  };

  root.render(
    <CreateFileModal
      {...props}
      open={true}
      onClose={() => {
        props.onClose?.();
        destroy();
      }}
      onConfirm={(filename) => {
        props.onConfirm?.(filename);
        destroy();
      }}
    />,
  );
};

export default CreateFileModal;
