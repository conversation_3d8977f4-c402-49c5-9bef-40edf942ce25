import { ErrorBlock } from 'antd-mobile';

import EmptySvg from '@/assets/mobile/file/svg/empty.svg';
import { fm } from '@/modules/Locale';

import style from './index.less';
type Props = {
  image?: string;
  text?: string;
};
export const FileEmpty = ({ image, text }: Props) => {
  return (
    <div className={style.emptyBox}>
      <ErrorBlock
        fullPage
        description=""
        image={image || EmptySvg}
        status="default"
        title={text || fm('File.noRecentlyUsedFiles')}
      />
    </div>
  );
};
