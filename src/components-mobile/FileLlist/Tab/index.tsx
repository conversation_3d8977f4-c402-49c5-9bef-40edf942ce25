import { memo, useCallback, useState } from 'react';

import style from './index.less';
type Props = {
  defaultValue?: string;
  changeTab?: (val: string) => void;
  list: {
    name: string;
    type: string;
  }[];
};

export const FileHeaderTab = memo(({ list, changeTab, defaultValue }: Props) => {
  const [tabValue, setTabValue] = useState(defaultValue || list?.[0].type);
  const changeVal = useCallback(
    (type: string) => {
      setTabValue(type);
      changeTab?.(type);
    },
    [changeTab],
  );
  return (
    <div className={style.tabList}>
      {list.map((item) => {
        return (
          <span
            key={item.type}
            className={`${style.tabItem} ${tabValue === item.type ? style.active : null}`}
            onClick={() => changeVal(item.type)}
          >
            {item.name}
          </span>
        );
      })}
    </div>
  );
});
