import { Skeleton } from 'antd-mobile';
import { memo } from 'react';

import style from './index.less';
export const FileLoadingCardItem = memo(() => {
  return (
    <div className={style.item}>
      <div className={style.itemTop}>
        <Skeleton.Paragraph animated lineCount={1} />
      </div>
      <div className={style.name}>
        <Skeleton.Paragraph animated lineCount={1} />
      </div>
      <div className={style.img}>
        <Skeleton animated style={{ width: '84px', height: '84px' }} />
      </div>
    </div>
  );
});
