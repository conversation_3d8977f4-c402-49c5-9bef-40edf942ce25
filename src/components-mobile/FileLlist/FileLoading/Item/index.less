.skeletonBoxItem {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  content-visibility: auto;
  min-height: 68px;
  overflow: hidden;
  padding: 0 20px;
  background-color: var(--theme-layout-color-bg-white);

  :global {
    .adm-skeleton.adm-skeleton-paragraph-line:last-child {
      width: 100%;
    }

    .adm-skeleton.adm-skeleton-paragraph-line {
      margin-top: 0;
      margin-bottom: 0;
    }
  }
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 18px;
  padding: 10px 0;
  border-bottom: 1px solid var(--theme-basic-color-lighter);
}

.contentLeft {
  line-height: normal;
  width: 100%;
}

.contentLeftName {
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
}

.contentLeftUpdatedAt {
  color: rgba(65, 70, 75, 40%);
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  margin-top: 8px;
  line-height: 13px;
}
