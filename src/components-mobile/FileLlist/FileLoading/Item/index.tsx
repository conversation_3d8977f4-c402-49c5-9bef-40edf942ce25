import { Skeleton } from 'antd-mobile';
import { memo } from 'react';

import style from './index.less';

export const FileLoadingItem = memo(() => {
  return (
    <div className={style.skeletonBoxItem}>
      <Skeleton animated style={{ width: '42px', height: '42px' }} />
      <div className={style.content}>
        <div className={style.contentLeft}>
          <div className={style.contentLeftName}>
            <Skeleton.Paragraph animated lineCount={1} />
          </div>
          <div className={style.contentLeftUpdatedAt}>
            <Skeleton.Paragraph animated lineCount={1} />
          </div>
        </div>
      </div>
    </div>
  );
});
