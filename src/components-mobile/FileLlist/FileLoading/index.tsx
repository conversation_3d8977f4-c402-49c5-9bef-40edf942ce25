import { memo } from 'react';

import { useVisibleHeightObserver } from '@/hooks/useVisibleHeightObserver';
import { ModeTypeEnum } from '@/model/FileList';

import { FileLoadingCardItem } from './Card';
import style from './index.less';
import { FileLoadingItem } from './Item';
type Props = {
  type: ModeTypeEnum;
};
export const FileLoading = memo(({ type }: Props) => {
  const { ref, visibleHeight } = useVisibleHeightObserver();
  const listArr = Array(Math.floor(visibleHeight / 68)).fill({});
  const cardArr = Array(Math.floor(visibleHeight / 164) * 2).fill({});
  const isList = type === ModeTypeEnum.list;
  const List = () => {
    if (isList) {
      return listArr.map((item) => <FileLoadingItem key={item} />);
    } else {
      return cardArr.map((item) => <FileLoadingCardItem key={item} />);
    }
  };
  return (
    <div ref={ref} className={`${isList ? style.skeletonListBox : style.skeletonCardBox}`}>
      <List />
    </div>
  );
});
