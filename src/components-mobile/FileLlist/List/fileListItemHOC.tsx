import { memo, useCallback } from 'react';
import type { AutoSizerProps, GridCellProps, GridProps, ListProps, ListRowProps } from 'react-virtualized';
import { AutoSizer, Grid, List } from 'react-virtualized';

import { ModeTypeEnum } from '@/model/FileList';

const XGrid = Grid as unknown as React.ComponentType<GridProps>;
const XList = List as unknown as React.ComponentType<ListProps>;
const XAutoSizer = AutoSizer as unknown as React.ComponentType<AutoSizerProps>;

interface FileListHOCProps<T> extends Readonly<Omit<ListProps, 'rowRenderer' | 'width'>> {
  list: T[];
  children: (data: T) => React.ReactNode;
}
export const FileListItemHOC = memo(<T,>({ children, list, ...defaultProp }: FileListHOCProps<T>) => {
  const type: ModeTypeEnum = defaultProp.mode ?? ModeTypeEnum.list;
  const { clickItem, noRowsRenderer } = defaultProp;
  const rowHeight = 169;
  const rowRenderer = useCallback(
    ({ index, style, key }: ListRowProps) => {
      const data = list[index];
      if (!data) return null;
      return (
        <div
          key={key}
          style={{
            ...style,
            boxSizing: 'border-box',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onClick={() => clickItem(data)}
        >
          {children(data)}
        </div>
      );
    },
    [list, children, clickItem],
  );

  const cellRenderer = useCallback(
    ({ columnIndex, rowIndex, key, style }: GridCellProps) => {
      const index = rowIndex + columnIndex;
      if (index >= list.length) return null;
      const data = list[index];
      return (
        <div
          key={key}
          style={{
            ...style,
            boxSizing: 'border-box',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onClick={() => clickItem(data)}
        >
          {children(data)}
        </div>
      );
    },
    [list, children, clickItem],
  );

  const getVirtualizedCom = useCallback(
    (props: ListProps | GridProps): Record<ModeTypeEnum, React.ReactNode> => ({
      [ModeTypeEnum.list]: <XList {...(props as ListProps)} />,
      [ModeTypeEnum.card]: list.length ? (
        <XGrid {...(props as GridProps)} />
      ) : (
        <div style={{ width: `${props.width}px` }}>{noRowsRenderer()}</div>
      ),
    }),
    [list.length, noRowsRenderer],
  );

  const calculateColumns = useCallback(
    (
      width: number,
      height: number,
    ): {
      columnCount: number;
      columnWidth: number;
      rowCount: number;
      height: number;
    } => {
      const maxColumns = Math.floor(width / 135);
      const columnCount = Math.min(maxColumns, list.length === 1 ? 2 : list.length);
      const columnWidth = Math.floor(width / columnCount);
      const rowCount = Math.ceil(list.length / columnCount);
      const gridHeight = Math.min(rowCount * rowHeight, height);
      return { columnCount, columnWidth, rowCount, height: gridHeight };
    },
    [list.length],
  );
  return (
    <XAutoSizer>
      {({ width, height }) => {
        const props: Record<ModeTypeEnum, ListProps | GridProps> = {
          [ModeTypeEnum.list]: {
            height,
            id: 'id',
            overscanRowCount: 8,
            rowCount: list.length,
            rowHeight: defaultProp.rowHeight || 68,
            rowRenderer,
            style: {
              paddingBottom: '120px',
            },
            width,
            ...defaultProp,
          },
          [ModeTypeEnum.card]: {
            cellRenderer,
            rowHeight: rowHeight,
            width,
            id: 'id',
            style: {
              paddingBottom: '120px',
            },
            ...defaultProp,
            ...calculateColumns(width, height),
          },
        };
        const typedProps = props[type];
        return getVirtualizedCom(typedProps)?.[type];
      }}
    </XAutoSizer>
  );
});
