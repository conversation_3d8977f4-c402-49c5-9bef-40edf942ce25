import { Ellipsis } from 'antd-mobile';
import { memo, useMemo } from 'react';

import { ReactComponent as MoreSvg } from '@/assets/mobile/file/svg/more.svg';
import { FileTypeImage } from '@/components-mobile/FileTypedimage';
import { fm } from '@/modules/Locale';
import type { FileItemProps } from '@/pages/pc/Desktop/model';
import { useMeStore } from '@/store/Me';
import { useFormatTime } from '@/utils/file';

import style from './index.less';
export const FileCardItem = memo((prop: FileItemProps) => {
  const { data } = prop;
  const { formatTime } = useFormatTime();
  const id = useMeStore((state) => state.me.id);
  const updatedAtText = useMemo(() => {
    return `${formatTime(data.updatedAt * 1000)} ${data.updatedUser.id === id ? fm('SearchCenter.me') : data.updatedUser.name} ${fm('SearchCenter.update')}`;
  }, [formatTime, data.updatedAt, data.updatedUser.name, data.updatedUser.id, id]);

  return (
    <div className={style.item}>
      <div className={style.itemTop}>
        <div className={style.itemTopLeft}>
          <Ellipsis content={updatedAtText} direction="end" />
        </div>
        <div className={style.itemTopRight}>
          <MoreSvg />
        </div>
      </div>
      <div className={style.name}>
        <Ellipsis content={data.name} direction="end" />
      </div>
      <div className={style.img}>
        <FileTypeImage fit="contain" type={data.subType || data.type} width={84} />
      </div>
    </div>
  );
});
