import { Ellipsis } from 'antd-mobile';
import { memo, useMemo } from 'react';

import { FileTypeImage } from '@/components-mobile/FileTypedimage';
import { fm } from '@/modules/Locale';
import type { FileItemProps } from '@/pages/pc/Desktop/model';
import { useMeStore } from '@/store/Me';
import { useFormatTime } from '@/utils/file';

import style from './item.less';
export const FileListItem = memo((prop: FileItemProps) => {
  const { data } = prop;
  const { formatTime } = useFormatTime();
  const id = useMeStore((state) => state.me.id);
  const updatedAtText = useMemo(() => {
    return `${formatTime(data.updatedAt * 1000)} ${data.updatedUser.id === id ? fm('SearchCenter.me') : data.updatedUser.name} ${fm('SearchCenter.update')}`;
  }, [formatTime, data.updatedAt, data.updatedUser.name, data.updatedUser.id, id]);
  return (
    <div className={style.item}>
      <FileTypeImage fit="contain" type={data.subType || data.type} width={42} />
      <div className={style.content}>
        <div className={style.contentLeft}>
          <div className={style.contentLeftName}>
            <Ellipsis content={data.name} direction="end" />
          </div>
          <div className={style.contentLeftUpdatedAt}>
            <Ellipsis content={updatedAtText} direction="start" />
          </div>
        </div>
        <div className={style.contentRight} />
      </div>
    </div>
  );
});
