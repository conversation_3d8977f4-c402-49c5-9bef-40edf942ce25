.item {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  content-visibility: auto;
  min-height: 68px;
  overflow: hidden;
  background-color: var(--theme-layout-color-bg-white);
}

.content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-left: 18px;
  padding: 10px 15px 10px 0;
  border-bottom: 1px solid var(--theme-basic-color-lighter);
}

.contentLeft {
  line-height: normal;
  width: calc(100% - 22px);
}

.contentRight {
  width: 40px;
  height: 36px;
  margin-right: -20px;
  background: url('@/assets/mobile/file/svg/more.svg') no-repeat left center;
}

.contentLeftName {
  color: var(--theme-basic-color-primary);
  font-size: 15px;
  font-style: normal;
  font-weight: 400;

  :global {
    .adm-ellipsis {
      line-height: 15px;
    }
  }
}

.contentLeftUpdatedAt {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  margin-top: 8px;
  line-height: 13px;
}
