import React from 'react';
import type { GridProps, ListProps } from 'react-virtualized';

import { FileListItemHOC } from './fileListItemHOC';
import style from './index.less';
type Props<T> = { data: T };

export function withFileListHOC<R extends { data: unknown }>(Component: React.ComponentType<Props<R['data']>>) {
  return function WrappedComponent({
    list,
    ...props
  }: { list: R['data'][] } & { clickItem: (data: R['data']) => void } & Omit<ListProps, 'width'> &
    Omit<GridProps, 'width'>) {
    return (
      <div className={style.virtualizedBox}>
        <FileListItemHOC {...props} list={list}>
          {(item) => <Component data={item as R['data']} />}
        </FileListItemHOC>
      </div>
    );
  };
}
