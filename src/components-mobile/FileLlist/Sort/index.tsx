import { memo } from 'react';

import type { SortModel, SortType } from '@/model/FileList';
import { fm } from '@/modules/Locale';

import style from './index.less';
type Props = {
  setSortData: (data: SortModel & { name?: string }) => void;
  sortData: SortModel;
  setVisible: (visible: boolean) => void;
};
export const Sort = memo(({ setVisible, setSortData, sortData }: Props) => {
  const list = [
    {
      name: fm('File.defaultSort'),
      type: 'default',
    },
    {
      name: fm('File.sortByFileName'),
      type: 'name',
    },
    {
      name: fm('File.sortByCreationTime'),
      type: 'createTime',
    },
    {
      name: fm('File.byNewnessSortBy'),
      type: 'updateTime',
    },
  ];
  return (
    <div className={style.box}>
      {list.map((item) => {
        return (
          <div
            key={item.type}
            className={style.boxItem}
            onClick={() =>
              setSortData({
                ...sortData,
                name: item.name,
                isReversed: item.type === sortData.selectedType ? !sortData.isReversed : sortData.isReversed,
                selectedType: item.type as SortType,
              })
            }
          >
            <div
              className={`${style.boxItemLeft} ${sortData.selectedType === item.type ? (sortData.isReversed ? style.up : style.down) : ''}`}
            >
              {item.name}
            </div>
            {sortData.selectedType === item.type && <div className={style.boxItemRight} />}
          </div>
        );
      })}

      <div
        className={style.folderIndex}
        onClick={() =>
          setSortData({
            ...sortData,
            isFolderPinned: !sortData.isFolderPinned,
          })
        }
      >
        <div className={style.boxItemLeft}>{fm('File.folderAtTop')}</div>
        {sortData.isFolderPinned && <div className={style.boxItemRight} />}
      </div>

      <div className={`${style.folderIndex} ${style.cancel}`} onClick={() => setVisible(false)}>
        取消
      </div>
    </div>
  );
});
