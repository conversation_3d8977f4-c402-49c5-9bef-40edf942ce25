.box {
  width: 100%;
}

.boxItem {
  width: 100%;
  flex-shrink: 0;
  height: 52px;
  padding-right: 10px;
  background: var(--theme-layout-color-bg-white);
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  color: var(--theme-basic-color-primary);

  &::after {
    content: '';
    position: absolute;
    box-sizing: border-box;
    pointer-events: none;
    transform-origin: center;
    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    transform: scale(0.5);
    border-top: 1px solid var(--theme-separator-color-lighter);
  }

  &:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
}

.up {
  background: url('@/assets/mobile/file/svg/up.svg') no-repeat 15px center;
  background-size: 24px;
}

.down {
  background: url('@/assets/mobile/file/svg/down.svg') no-repeat 15px center;
  background-size: 24px;
}

.boxItemLeft {
  padding-left: 45px;
}

.boxItemRight {
  width: 50px;
  height: 50px;
  background: url('@/assets/mobile/file/svg/select.svg') no-repeat center center / 24px;
}

.folderIndex:extend(.boxItem) {
  margin-top: 5px;

  &::after {
    border-top: none;
  }
}

.cancel {
  justify-content: center;
  font-size: 15px;
}
