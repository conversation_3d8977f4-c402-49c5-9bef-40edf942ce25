import { Button } from 'antd-mobile';
import { memo, useCallback, useState } from 'react';

import { ReactComponent as ArrowDown } from '@/assets/mobile/file/svg/arrow-down.svg';
import CardSvg from '@/assets/mobile/file/svg/card.svg';
import ListSvg from '@/assets/mobile/file/svg/list.svg';
import type { SortModel } from '@/model/FileList';

import { FilePopup } from '../Popup';
import { Sort } from '../Sort';
import style from './index.less';
type Props = {
  sortData: SortModel;
  changeMode: () => void;
  setSortData: (data: SortModel) => void;
};
export const FileSort = memo(({ sortData, changeMode, setSortData }: Props) => {
  const [visible, setVisible] = useState(false);
  const openSortDialog = useCallback(() => {
    setVisible(true);
  }, []);

  const changeSortData = useCallback(
    (data: SortModel) => {
      setSortData(data);
      setVisible(false);
    },
    [setSortData],
  );
  return (
    <div className={style.header}>
      <Button className={style.headerText} fill="none" size="mini" onClick={openSortDialog}>
        {sortData.name}
        <ArrowDown style={{ marginLeft: '5px' }} />
      </Button>
      <div
        className={style.modeType}
        style={{
          background: `url(${sortData.layout ? CardSvg : ListSvg}) center center / contain`,
        }}
        onClick={changeMode}
      />
      <FilePopup setVisible={setVisible} visible={visible}>
        <Sort setSortData={changeSortData} setVisible={setVisible} sortData={sortData} />
      </FilePopup>
    </div>
  );
});
