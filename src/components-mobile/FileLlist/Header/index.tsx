import { NavBar, Space } from 'antd-mobile';
import type { ReactNode } from 'react';

import FindSvg from '@/assets/mobile/file/svg/find.svg';
import HeaderMoreSvg from '@/assets/mobile/file/svg/header-more.svg';
import LeftSvg from '@/assets/mobile/file/svg/left.svg';
import type { FileDataModel } from '@/pages/pc/Desktop/model';

import { FileTypeImage } from '../../FileTypedimage';
import style from './index.less';
type Props = {
  back: () => void;
  data?: Partial<FileDataModel> | null;
  guid?: string;
  children?: ReactNode;
};
export const FileHeader = ({ back, data, guid, children }: Props) => {
  const right = (
    <div style={{ fontSize: 24 }}>
      <Space className={style.spaceGap}>
        <FileTypeImage fit="contain" src={FindSvg} width={21} />
        {guid && <FileTypeImage fit="contain" src={HeaderMoreSvg} width={24} />}
      </Space>
    </div>
  );

  return (
    <div className={style.header}>
      {guid ? (
        <NavBar backIcon={<FileTypeImage fit="contain" src={LeftSvg} width={24} />} right={right} onBack={back}>
          {data?.name}
        </NavBar>
      ) : (
        <NavBar back={children} backIcon={false} right={right} />
      )}
    </div>
  );
};
