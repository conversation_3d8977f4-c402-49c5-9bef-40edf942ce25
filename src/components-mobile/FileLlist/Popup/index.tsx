import { Popup } from 'antd-mobile';
import React, { memo } from 'react';
type Props = {
  visible: boolean;
  children: React.ReactNode;
  setVisible: (visible: boolean) => void;
};
export const FilePopup = memo(({ visible, setVisible, children }: Props) => {
  return (
    <Popup
      destroyOnClose
      bodyStyle={{
        borderTopLeftRadius: '8px',
        borderTopRightRadius: '8px',
        background: 'var(--theme-layout-color-bg-editor)',
      }}
      visible={visible}
      onMaskClick={() => setVisible(false)}
    >
      {children}
    </Popup>
  );
});
