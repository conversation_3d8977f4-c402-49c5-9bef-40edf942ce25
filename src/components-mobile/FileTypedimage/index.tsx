import type { ImageProps } from 'antd-mobile';
import { Image as AntdImage } from 'antd-mobile';
import { memo, useEffect, useState } from 'react';

import compressedPackageImg from '@/assets/mobile/file/svg/compressedPackage.svg';
import documentImg from '@/assets/mobile/file/svg/document.svg';
import fileDefaultImg from '@/assets/mobile/file/svg/file-default.svg';
import folderImg from '@/assets/mobile/file/svg/folder.svg';
import moDocImg from '@/assets/mobile/file/svg/mo-doc.svg';
import moFormImg from '@/assets/mobile/file/svg/mo-form.svg';
import moMindmapImg from '@/assets/mobile/file/svg/mo-mindmap.svg';
import moNewDocImg from '@/assets/mobile/file/svg/mo-newdoc.svg';
import moPresentationImg from '@/assets/mobile/file/svg/mo-presentation.svg';
import moSheetImg from '@/assets/mobile/file/svg/mo-sheet.svg';
import moTableImg from '@/assets/mobile/file/svg/mo-table.svg';
import moBordImg from '@/assets/mobile/file/svg/mo-whitebord.svg';
import musicImg from '@/assets/mobile/file/svg/music.svg';
import pictureImg from '@/assets/mobile/file/svg/picture.svg';
import slideImg from '@/assets/mobile/file/svg/slide.svg';
import videoImg from '@/assets/mobile/file/svg/video.svg';

import type { ImgType, TypedImageProps } from './model';
type ImageModel = { img: string; ext: string[] };

const imageMap: Record<ImgType, ImageModel> = {
  /** 文件夹 */
  folder: {
    img: folderImg,
    ext: [],
  },
  newdoc: {
    img: moNewDocImg,
    ext: [],
  },
  modoc: {
    img: moDocImg,
    ext: [],
  },
  mosheet: {
    img: moSheetImg,
    ext: [],
  },
  table: {
    img: moTableImg,
    ext: [],
  },
  presentation: {
    img: moPresentationImg,
    ext: [],
  },
  form: {
    img: moFormImg,
    ext: [],
  },
  board: {
    img: moBordImg,
    ext: [],
  },
  mindmap: {
    img: moMindmapImg,
    ext: [],
  },

  /** 文档 */
  document: {
    img: documentImg,
    ext: ['doc', 'docx', 'md', 'rtf', 'wps', 'wpt', 'txt', 'pdf', 'ofd'],
  },
  sheet: {
    img: moSheetImg,
    ext: ['xls', 'xlsx', 'csv'],
  },
  /** 幻灯片 */
  slide: {
    img: slideImg,
    ext: ['ppt', 'pptx'],
  },
  /** 图片 */
  picture: {
    img: pictureImg,
    ext: ['png', 'jpg', 'tiff', 'gif'],
  },
  /** 视频 */
  video: {
    img: videoImg,
    ext: ['mp4', 'mov', 'qt'],
  },
  /** 音频 */
  music: {
    img: musicImg,
    ext: ['mp3'],
  },
  /** 压缩包 */
  compressedPackage: {
    img: compressedPackageImg,
    ext: ['rar', 'zip'],
  },
  fileDefaultImg: {
    img: fileDefaultImg,
    ext: [],
  },
};

// 预加载图片缓存
const preloadedImages = new Set<string>();

// 预加载图片
const preloadImage = (item: ImageModel) => {
  if (!preloadedImages.has(item.img)) {
    const img = new Image();
    img.src = item.img;
    preloadedImages.add(item.img);
  }
};

// 预加载所有图片(只加载未加载过的)
Object.values(imageMap).forEach(preloadImage);

export const FileTypeImage = memo((props: TypedImageProps & ImageProps) => {
  const [src, setSrc] = useState<string | undefined>(undefined);
  const { type } = props;

  const getImgPath = (type: string) => {
    if (type === 'folder') return imageMap.folder.img;
    if (imageMap[type as ImgType]) {
      return imageMap[type as ImgType].img;
    } else {
      for (const key in imageMap) {
        if (imageMap[key as ImgType].ext.includes(type)) {
          return imageMap[key as ImgType].img;
        }
      }
    }
    return imageMap.fileDefaultImg.img;
  };
  useEffect(() => {
    if (type) {
      const loadImage = async () => {
        setSrc(getImgPath(type));
      };
      loadImage();
    }
  }, [type]);

  return <AntdImage src={src} {...props} />;
});
