export interface ThirdDepartment {
  id: string;
  name: string;
}

export interface ThirdRootDepartmentResponse {
  domain: string;
  requestId: string;
  data: ThirdDepartment;
}

export interface ThirdSubdepartmentsResponse {
  domain: string;
  requestId: string;
  data: {
    departments: ThirdDepartment[];
  };
}

export interface ThirdUser {
  id: string;
  name: string;
  avatar: string;
  email: string;
}

export interface ThirdUserListResponse {
  domain: string;
  requestId: string;
  data: {
    users: ThirdUser[];
    totalCount: number;
  };
}

export interface ThirdCountSeatResponse {
  domain: string;
  requestId: string;
  data: {
    creator: null | ThirdUser;
    currentUser: ThirdUser;
    externalCount: number;
    seatCount: number;
  };
}

export interface ThirdScopeResponse {
  domain: string;
  requestId: string;
  data: {
    blacklistUsers: ThirdUser[];
    departments: ThirdDepartment[];
    users: ThirdUser[];
  };
}

export interface searchUserAndDepartmentResponse {
  domain: string;
  requestId: string;
  data: {
    departments: ThirdDepartment[];
    users: ThirdUser[];
  };
}

export interface GetScopeDiffResponse {
  domain: string;
  requestId: string;
  data: {
    activateUsers: [];
    disableUsers: [];
  };
}

export interface ActiveRangeState {
  selectedBlacklist: ThirdUser[];
  selectedDepartments: ThirdDepartment[];
  selectedUsers: ThirdUser[];
  selectedSeatCount: number;
  unselectedCreator: ThirdUser | null;
  unselectedCurrentUser: ThirdUser | null;
  getThirdCountSeatLoading: boolean;
  searchUserList: ThirdUser[];
  searchDepartmentList: ThirdDepartment[];
}

export type Action =
  | {
      type: 'setSelectedBlacklist';
      payload: {
        selectedBlacklist: ThirdUser[];
      };
    }
  | {
      type: 'setSelectedDepartments';
      payload: {
        selectedDepartments: ThirdDepartment[];
      };
    }
  | {
      type: 'setSelectedUsers';
      payload: {
        selectedUsers: ThirdUser[];
      };
    }
  | {
      type: 'setSelectedSeatCount';
      payload: {
        selectedSeatCount: number;
      };
    }
  | {
      type: 'setGetThirdCountSeatLoading';
      payload: {
        getThirdCountSeatLoading: boolean;
      };
    }
  | {
      type: 'setSearchUserList';
      payload: {
        searchUserList: ThirdUser[];
      };
    }
  | {
      type: 'setSearchDepartmentList';
      payload: {
        searchDepartmentList: ThirdDepartment[];
      };
    }
  | {
      type: 'setUnselectedCreator';
      payload: {
        unselectedCreator: ThirdUser | null;
      };
    }
  | {
      type: 'setUnselectedCurrentUser';
      payload: {
        unselectedCurrentUser: ThirdUser | null;
      };
    };
