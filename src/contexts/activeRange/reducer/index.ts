import type { Action, ActiveRangeState } from '../type';

export const activeRangeReducer: (state: ActiveRangeState, action: Action) => ActiveRangeState = (state, action) => {
  switch (action.type) {
    case 'setSelectedBlacklist': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSelectedDepartments': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSelectedUsers': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSelectedSeatCount': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setGetThirdCountSeatLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchUserList': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchDepartmentList': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUnselectedCreator': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUnselectedCurrentUser': {
      return {
        ...state,
        ...action.payload,
      };
    }

    default:
      throw Error('unknown actin.type');
  }
};
