import type { ReactNode } from 'react';
import React, { createContext, useReducer } from 'react';

import { getThirdCountSeat } from '@/api/ActiveRange';

import { activeRangeReducer } from './reducer';
import type { Action, ActiveRangeState } from './type';

type SaveSelectedSeatCount = (
  teamId: number,
  data: {
    departments: string[];
    users: string[];
    userBlacklist: string[];
  },
) => Promise<void>;

const initState: ActiveRangeState = {
  selectedBlacklist: [],
  selectedDepartments: [],
  selectedUsers: [],
  selectedSeatCount: 0,
  unselectedCreator: null,
  unselectedCurrentUser: null,
  getThirdCountSeatLoading: false,
  searchDepartmentList: [],
  searchUserList: [],
};

export const ActiveRangeContext = createContext<ActiveRangeState>(initState);
export const ActiveRangeDispatchContext = createContext<{
  dispatch: React.Dispatch<Action> | null;
  saveCountSeat: SaveSelectedSeatCount | null;
}>({
  dispatch: null,
  saveCountSeat: null,
});

export const ActiveRangeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(activeRangeReducer, initState);

  const saveCountSeat: SaveSelectedSeatCount = async (teamId, data) => {
    dispatch({
      type: 'setGetThirdCountSeatLoading',
      payload: {
        getThirdCountSeatLoading: true,
      },
    });
    try {
      const {
        data: { seatCount, creator, currentUser },
      } = await getThirdCountSeat(teamId, data);

      dispatch({
        type: 'setSelectedSeatCount',
        payload: {
          selectedSeatCount: seatCount,
        },
      });
      dispatch({
        type: 'setUnselectedCreator',
        payload: {
          unselectedCreator: creator,
        },
      });
      dispatch({
        type: 'setUnselectedCurrentUser',
        payload: {
          unselectedCurrentUser: currentUser,
        },
      });
    } catch (error) {}
    dispatch({
      type: 'setGetThirdCountSeatLoading',
      payload: {
        getThirdCountSeatLoading: false,
      },
    });
  };

  return (
    <ActiveRangeContext.Provider value={state}>
      {
        // eslint-disable-next-line react/jsx-no-constructed-context-values
        <ActiveRangeDispatchContext.Provider value={{ dispatch, saveCountSeat }}>
          {children}
        </ActiveRangeDispatchContext.Provider>
      }
    </ActiveRangeContext.Provider>
  );
};
