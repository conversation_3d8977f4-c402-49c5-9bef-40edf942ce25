import type { Me } from '@/model/Me';

import { DefaultTruthPermissionItemKeys } from './type';

export const isCreator = (user: Me): boolean => {
  return user.teamRole === 'creator';
};

export const isAdmin = (user: Me): boolean => {
  return user.teamRole === 'admin';
};

export const getManagePerformance = ({ performance, me }: { performance: boolean; me: Me }) => {
  const isAdminCreator = isAdmin(me) || isCreator(me);
  const hasTruthKey = DefaultTruthPermissionItemKeys.includes('manage_performance');
  // if (__RUNTIME_ENV__.PRIVATE_DEPLOY) {
  //   return isAdminCreator && hasTruthKey;
  // }
  return performance && isAdminCreator && hasTruthKey;
};

export const getManageAudit = ({ enterpriseAudit, me }: { enterpriseAudit: boolean; me: Me }) => {
  const isAdminCreator = isAdmin(me) || isCreator(me);
  return enterpriseAudit && isAdminCreator && DefaultTruthPermissionItemKeys.includes('manage_audit');
};

export const getEnableManageKitSeat = ({ enableManageKitSeat, me }: { enableManageKitSeat: boolean; me: Me }) => {
  const isAdminCreator = isAdmin(me) || isCreator(me);
  return enableManageKitSeat && isAdminCreator && DefaultTruthPermissionItemKeys.includes('enable_manage_kit_seat');
};

export const getEnableManageWhitelistSeat = ({
  enableManageWhitelistSeat,
  me,
}: {
  enableManageWhitelistSeat: boolean;
  me: Me;
}) => {
  const isAdminCreator = isAdmin(me) || isCreator(me);
  return (
    enableManageWhitelistSeat &&
    isAdminCreator &&
    DefaultTruthPermissionItemKeys.includes('enable_manage_whitelist_seat')
  );
};

export const getManageOrder = ({ me }: { me: Me }) => {
  const isAdminCreator = isAdmin(me) || isCreator(me);

  return (
    // !__RUNTIME_ENV__.PRIVATE_DEPLOY &&
    // __RUNTIME_ENV__.PLATFORM !== 'huawei-cloud' &&
    isAdminCreator && DefaultTruthPermissionItemKeys.includes('manage_audit')
  );
};

export const getShowTeamMembers = ({ showTeamMembers }: { showTeamMembers: boolean }) => {
  // if (__RUNTIME_ENV__.PLATFORM === 'rong-cloud') {
  //   return false;
  // }
  return showTeamMembers;
};

/**
 * 获取管理通讯录权限
 */
export const getManageTeamMembers = ({ manageTeamMembers }: { manageTeamMembers: boolean }) => {
  return manageTeamMembers;
};
