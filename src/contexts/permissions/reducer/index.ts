import type { Action, PermissionState } from '../type';

export function permissionReducer(state: PermissionState, action: Action) {
  switch (action.type) {
    case 'setState': {
      return action.payload;
    }
    case 'setIsDingtalkOrWework': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setTeamInfo': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSyncThirdState': {
      return {
        ...state,
        ...action.payload,
      };
    }
    default: {
      throw Error('Unknown action in permissionReducer');
    }
  }
}
