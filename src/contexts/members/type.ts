export interface Department {
  id: number;
  name: string;
  teamId: number;
  allMemberCount: number;
  canBother: boolean;
  parentId?: number;
}

export enum TeamRole {
  /** 企业创建者 */
  creator = 'creator',
  /** 企业管理员 */
  admin = 'admin',
  /** 成员 */
  member = 'member',
  /** 禁用成员 */
  disabled = 'disabled',
  /** 待确认 */
  pending = 'pending',
}

export interface ShadowSubtreeResponse {
  currentDepartment: Department;
  subdepartments: Department[];
  rootVisible: boolean;
}

export interface UserItem {
  id: number;
  teamId: number;
  teamRole: TeamRole;
  team_role?: TeamRole;
  name: string;
  avatar: string;
  email: string;
  canBother: boolean;
  isSeat: number;
  mobile?: string;
  mobileAccount?: string;
  status: number;
  handoverMenu?: {
    hasHistory: boolean;
    token?: string;
  };
}

export interface InviteStateResponse {
  inviteGuid: string;
}

export interface UserResponse {
  departmentUserIDs: number[];
  total: number;
  users: UserItem[];
}

export interface AddMembersByAccountResponse {
  invitedAccounts: string[];
  invitedIDs: number[];
}

export interface OuterSiderFile {
  role: string;
  url: string;
  isDelete: number;
  namePinyin: string;
  parentId: number;
  passwordProtected: boolean;
  shareMode: string;
  teamId: number;
  userId: number;
  deletedBy: null;
  updatedAt: string;
  updatedBy: number;
  createdAt: string;
  id: number;
  guid: string;
  name: string;
  name_pinyin: string;
  password_protected: boolean;
  share_mode: string;
  type: number;
  is_delete: number;
  user_id: number;
  deleted_by: null;
  updated_by: number;
  team_id: number;
  parent_id: number;
  created_at: string;
  updated_at: string;
  isFromSVC: boolean;
  is_folder: boolean;
  isFolder: boolean;
  collaboratorCount: number;
  departmentCollaboratorCount: number;
  isSpace: boolean;
  isLocked: boolean;
  parentRole: string;
  isShortcut: boolean;
  isLegacy: boolean;
  hasChildren: boolean;
  isFileAdmin: boolean;
  isCloudFile: boolean;
  isDesktop: boolean;
  fileSize: number;
  sortName: null;
  starred: boolean;
  tags: null;
  marked: boolean;
}
export type OutsiderFilesResponse = OuterSiderFile[];

export interface ItemSource extends Department {
  key: React.Key;
  children: ItemSource[];
  level?: number;
  parentId?: number;
}

export enum OtherListKeys {
  /** 未分配部门成员 */
  OnlyOnRoot = 'OnlyOnRoot',
  /** 外部协作者 */
  OuterSider = 'OuterSider',
  /** 已禁用成员 */
  Disabled = 'Disabled',
  DingOuters = 'DingOuters',
  /** 激活的成员 */
  Activated = 'Activated',
  /** 未激活的成员 */
  Inactivated = 'Inactivated',
}

export interface SpecialUserItem {
  id: number;
  name: string;
  email: string;
  avatar: string;
  mobile: string;
  status: number;
  alias: string;
  createdAt: string;
  namePinyin: string;
  teamId: number;
  teamRole: TeamRole;
  mergedInto: number;
  teamTime: number;
  mobileAccount: string;
  hasPassword: boolean;
  isSeat: boolean;
  teamRoleEnum: number;
  registerTime: number;
  handoverMenu: {
    token: string;
    hasHistory: boolean;
  };
}

export interface OuterSiderUser {
  id: number;
  name: string;
  avatar: string;
  status: number;
  email: string;
  mobile: string;
  mobileAccount: string;
}

interface ShadowSubtreeResponseState extends ShadowSubtreeResponse {
  activeOtherKey: OtherListKeys | null;
}

export interface SearchUser {
  id: number;
  name: string;
  alias: string;
  avatar: string;
  isOutsider: boolean;
  email: string;
  role: null;
  departmentNames: [];
  labels: [];
  hitAttribute: {
    type: number;
    key: string;
    value: string;
  };
  teamRole: TeamRole;
  canBother: boolean;
  handoverMenu: {
    token: string;
    hasHistory: boolean;
  };
}

export interface SearchUserItem {
  type: string;
  user: SearchUser;
}
export interface SearchUserListResponse {
  data: {
    next: number;
    results: SearchUserItem[];
  };
  domain: string;
  requestID: string;
}

export interface OnlyRootUserResponse {
  total: number;
  users: SpecialUserItem[];
}

export interface InactivatedMembersResponse {
  users: SpecialUserItem[];
}

export type ExternalResponse = SpecialUserItem[];

export interface OuterSiderResponse {
  total: number;
  outsiders: OuterSiderUser[];
}

export interface MineMember {
  avatar: string;
  canBother: null | boolean;
  email: string;
  id: number;
  inviter_id: number;
  isSeat: number;
  isVerified: boolean;
  is_seat: number;
  lastVisit: string;
  last_visit: string;
  mobileAccount: string;
  name: string;
  namePinyin: string;
  name_pinyin: string;
  status: number;
  teamId: number;
  teamRole: TeamRole;
  team_id: number;
  team_role: TeamRole;
}

export type MimeMembersResponse = MineMember[];

export interface OtherItem {
  name: string;
  key: OtherListKeys;
  count: number;
}

export interface MembersState {
  departmentUserIDs: number[];
  /**
   * 当前部门下总人数
   */
  total: number;
  /**
   * 当前部门下用户列表
   */
  users: UserItem[];
  usersLoading: boolean;
  /**
   * 当前部门下用户列表 - 页数
   */
  usersPage: number;
  /**
   * 当前部门 ： 注意这个是后端范围的数据，对应左侧点击展开请求
   */
  currentDepartment: Department | null;
  /**
   * 子部门
   */
  subdepartments: Department[];
  rootVisible: boolean;
  /**
   * 左侧部门树元数据集
   */
  treeDataSource: ItemSource[];
  treeDataSourceLoading: boolean;
  /**
   * 未分配用户加载
   */
  onlyOnRootLoading: boolean;
  /**
   * 未分配用户列表
   */
  onlyOnRootList: SpecialUserItem[];
  /**
   * 未分配用户总人数
   */
  onlyOnRootTotal: number;
  /**
   * 未分配用户页码
   */
  onlyOnRootPage: number;
  /**
   * 外部协作者加载
   */
  outsiderLoading: boolean;
  /**
   * 外部协作者列表
   */
  outsiderList: OuterSiderUser[];
  /**
   * 外部协作者总人数
   */
  outsiderTotal: number;
  /**
   * 外部协作者页码
   */
  outsiderPage: number;
  /**
   * 禁用用户加载
   */
  disableLoading: boolean;
  /**
   * 禁用用户列表
   */
  disableList: SpecialUserItem[];
  /**
   * 禁用用户总数
   */
  disableTotal: number;
  /**
   * 禁用用户页码
   */
  disablePage: number;
  /**
   * 未分配、外部协作者、禁用
   */
  activeOtherKey: OtherListKeys | null;
  /**
   * 搜索列表
   */
  searchUserList: SearchUser[];
  isInSearchMode: boolean;
  searchLoading: boolean;
  searchKeyword: string;
  searchNext: number;
  searchLoadingMore: boolean;
  /**
   * 未激活成员
   */
  unactivated: SpecialUserItem[];
  unactivatedLoading: boolean;
  /**
   * 企业外部成员」或「未关联钉钉」或「未关联微信」
   */
  external: SpecialUserItem[];
}

export type Action =
  | {
      type: 'setShadowSubtreeResponse';
      payload: ShadowSubtreeResponseState;
    }
  | {
      type: 'setUserResponse';
      payload: UserResponse & {
        usersPage: number;
      };
    }
  | {
      type: 'setTreeDataSource';
      payload: ItemSource[];
    }
  | {
      type: 'setOnlyOnRootLoading';
      payload: {
        onlyOnRootLoading: boolean;
      };
    }
  | {
      type: 'setOnlyOnRoot';
      payload: {
        onlyOnRootList: SpecialUserItem[];
        onlyOnRootTotal: number;
        onlyOnRootPage: number;
      };
    }
  | {
      type: 'setOutsiderLoading';
      payload: {
        outsiderLoading: boolean;
      };
    }
  | {
      type: 'setOutsider';
      payload: {
        outsiderList: OuterSiderUser[];
        outsiderTotal: number;
        outsiderPage: number;
      };
    }
  | {
      type: 'setDisableLoading';
      payload: {
        disableLoading: boolean;
      };
    }
  | {
      type: 'setDisable';
      payload: {
        disableList: SpecialUserItem[];
        disableTotal: number;
        disablePage: number;
      };
    }
  | {
      type: 'setActiveOtherKey';
      payload: {
        activeOtherKey: OtherListKeys | null;
      };
    }
  | {
      type: 'setSearchResult';
      payload: {
        searchUserList: SearchUser[];
      };
    }
  | {
      type: 'setIsInSearchMode';
      payload: {
        isInSearchMode: boolean;
      };
    }
  | {
      type: 'setCurOuterItem';
      payload: {
        curOtherItem: OtherItem | null;
      };
    }
  | {
      type: 'setUnactivated';
      payload: {
        unactivated: SpecialUserItem[];
      };
    }
  | {
      type: 'setUnactivatedLoading';
      payload: {
        unactivatedLoading: boolean;
      };
    }
  | {
      type: 'setExternal';
      payload: {
        external: SpecialUserItem[];
      };
    }
  | {
      type: 'setSearchLoading';
      payload: {
        searchLoading: boolean;
      };
    }
  | {
      type: 'setTreeDataSourceLoading';
      payload: {
        treeDataSourceLoading: boolean;
      };
    }
  | {
      type: 'setUsersLoading';
      payload: {
        usersLoading: boolean;
      };
    }
  | {
      type: 'setSearchKeyword';
      payload: {
        searchKeyword: string;
      };
    }
  | {
      type: 'setSearchNext';
      payload: {
        searchNext: number;
      };
    }
  | {
      type: 'setSearchLoadingMore';
      payload: {
        searchLoadingMore: boolean;
      };
    };
