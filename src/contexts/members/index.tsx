import type { ReactNode } from 'react';
import React, { createContext, useReducer } from 'react';

import { useMeStore } from '@/store/Me';

import {
  getDepartmentUsers,
  getShadowSubtree,
  loadDisabledMembers,
  loadExternal,
  loadInactivatedMembers,
  loadOnlyOnRootMembers,
  loadOutsiderMembers,
  searchUsers,
} from '../../api/Members';
import { RootId } from '../../pages/pc/Members/const';
import { getDepartmentById, updateTreeData } from '../../pages/pc/Members/utils';
import { membersStateReducer } from './reducer';
import { type Action, type ItemSource, type MembersState, OtherListKeys } from './type';

const initState: MembersState = {
  departmentUserIDs: [],
  total: 0,
  users: [],
  usersLoading: false,
  usersPage: 1,
  currentDepartment: null,
  subdepartments: [],
  rootVisible: false,
  treeDataSource: [],
  treeDataSourceLoading: false,
  onlyOnRootLoading: false,
  onlyOnRootList: [],
  onlyOnRootTotal: 0,
  onlyOnRootPage: 1,
  outsiderLoading: false,
  outsiderList: [],
  outsiderTotal: 0,
  outsiderPage: 1,
  disableLoading: false,
  disableList: [],
  disableTotal: 0,
  disablePage: 1,
  activeOtherKey: null,
  isInSearchMode: false,
  unactivated: [],
  unactivatedLoading: false,
  external: [],
  searchKeyword: '',
  searchUserList: [],
  searchLoading: false,
  searchNext: -1,
  searchLoadingMore: false,
};

export const MembersContext = createContext<MembersState>(initState);
export const MembersDispatchContext = createContext<{
  dispatch: React.Dispatch<Action> | undefined;
  getOnlyOnRootMembers: ((data: { teamId: number; page: number }) => Promise<void>) | null;
  getOutsiderMembers: ((data: { page: number }) => Promise<void>) | null;
  getDisabledMembers: ((data: { teamId: number; page: number }) => Promise<void>) | null;
  getSubDepartmentList: (departmentId: number, showLoading?: boolean) => Promise<void>;
  getDepartmentUserList: ((data: { departmentId: number; page: number }) => Promise<void>) | null;
  getSearchUserList: ((keyword: string) => Promise<void>) | null;
  loadMoreSearchUsers: (() => Promise<void>) | null;
  getInactivatedList: ((teamId: number) => Promise<void>) | null;
  getExternalList: ((teamId: number) => Promise<void>) | null;
  refreshTableList: ((departmentId?: number) => Promise<void>) | null;
}>({
  dispatch: undefined,
  getOnlyOnRootMembers: null,
  getDisabledMembers: null,
  getOutsiderMembers: null,
  getSubDepartmentList: () => Promise.resolve(),
  getDepartmentUserList: null,
  getSearchUserList: null,
  loadMoreSearchUsers: null,
  getInactivatedList: null,
  getExternalList: null,
  refreshTableList: null,
});

const PageSizeForSearchUsers = 100;

export const MembersProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(membersStateReducer, initState);
  const me = useMeStore((state) => state.me);

  /**
   * 获取未分配部门成员
   */
  const getOnlyOnRootMembers = async ({ teamId, page }: { teamId: number; page: number }) => {
    dispatch?.({
      type: 'setOnlyOnRootLoading',
      payload: {
        onlyOnRootLoading: true,
      },
    });
    const { total, users } = await loadOnlyOnRootMembers({ teamId, page });
    dispatch?.({
      type: 'setOnlyOnRoot',
      payload: {
        onlyOnRootList: users || [],
        onlyOnRootTotal: total,
        onlyOnRootPage: page,
      },
    });
    dispatch?.({
      type: 'setOnlyOnRootLoading',
      payload: {
        onlyOnRootLoading: false,
      },
    });
  };

  /**
   * 获取外部协作者
   */
  const getOutsiderMembers = async ({ page }: { page: number }) => {
    dispatch?.({
      type: 'setOutsiderLoading',
      payload: {
        outsiderLoading: true,
      },
    });
    const { total, outsiders } = await loadOutsiderMembers({ page });
    dispatch?.({
      type: 'setOutsider',
      payload: {
        outsiderList: outsiders || [],
        outsiderTotal: total,
        outsiderPage: page,
      },
    });
    dispatch?.({
      type: 'setOutsiderLoading',
      payload: {
        outsiderLoading: false,
      },
    });
  };

  /**
   * 获取禁用成员
   */
  const getDisabledMembers = async ({ teamId, page }: { teamId: number; page: number }) => {
    dispatch?.({
      type: 'setDisableLoading',
      payload: {
        disableLoading: true,
      },
    });
    const { total, users } = await loadDisabledMembers({ teamId, page });
    dispatch?.({
      type: 'setDisable',
      payload: {
        disableList: users || [],
        disableTotal: total,
        disablePage: page,
      },
    });
    dispatch?.({
      type: 'setDisableLoading',
      payload: {
        disableLoading: false,
      },
    });
  };

  /**
   * 获取部门下的子部门
   */
  const getSubDepartmentList = async (departmentId: number, showLoading = true) => {
    const { treeDataSource } = state;

    if (showLoading) {
      dispatch({
        type: 'setTreeDataSourceLoading',
        payload: {
          treeDataSourceLoading: true,
        },
      });
    }

    try {
      const res = await getShadowSubtree(departmentId);
      dispatch?.({
        type: 'setShadowSubtreeResponse',
        payload: {
          ...res,
          activeOtherKey: null,
        },
      });

      if (departmentId === RootId) {
        const item: ItemSource = {
          ...res.currentDepartment,
          key: res.currentDepartment.id,
          children: res.subdepartments.map((item) => ({
            ...item,
            key: item.id,
            level: 1,
            parentId: departmentId,
            children: getDepartmentById(treeDataSource, item.id)?.children || [],
          })),
          level: 0,
        };
        // 1 判断是否为根节点
        dispatch?.({
          type: 'setTreeDataSource',
          payload: [{ ...item }],
        });
      } else {
        dispatch?.({
          type: 'setTreeDataSource',
          payload: updateTreeData({
            list: treeDataSource,
            key: departmentId,
            children: res.subdepartments.map((item) => {
              return {
                ...item,
                key: item.id,
                parentId: departmentId,
                children: getDepartmentById(treeDataSource, item.id)?.children || [],
              };
            }),
            curNode: res.currentDepartment,
            startLevel: 1,
          }),
        });
      }
    } catch (error) {
      console.error('getSubDepartmentList', error);
    }

    dispatch?.({
      type: 'setTreeDataSourceLoading',
      payload: {
        treeDataSourceLoading: false,
      },
    });
  };

  /**
   * 获取部门下的成员
   */
  const getDepartmentUserList = async (params: { departmentId: number; page: number }) => {
    dispatch?.({
      type: 'setUsersLoading',
      payload: {
        usersLoading: true,
      },
    });
    try {
      const res = await getDepartmentUsers(params);
      dispatch?.({
        type: 'setUserResponse',
        payload: {
          ...res,
          usersPage: params.page,
        },
      });
    } catch (error) {
      console.error('getDepartmentUserList', error);
    }
    dispatch?.({
      type: 'setUsersLoading',
      payload: {
        usersLoading: false,
      },
    });
  };

  const getInactivatedList = async (teamId: number) => {
    dispatch?.({
      type: 'setUnactivatedLoading',
      payload: {
        unactivatedLoading: true,
      },
    });
    const { users } = await loadInactivatedMembers({ teamId });
    dispatch?.({
      type: 'setUnactivated',
      payload: {
        unactivated: users,
      },
    });
    dispatch?.({
      type: 'setUnactivatedLoading',
      payload: {
        unactivatedLoading: false,
      },
    });
  };
  const getExternalList = async (teamId: number) => {
    const externalList = await loadExternal({ teamId });
    dispatch?.({
      type: 'setExternal',
      payload: {
        external: externalList,
      },
    });
  };

  const getSearchUserList = async (keyword: string) => {
    dispatch?.({
      type: 'setSearchKeyword',
      payload: {
        searchKeyword: keyword,
      },
    });
    if (!keyword) {
      dispatch?.({
        type: 'setIsInSearchMode',
        payload: {
          isInSearchMode: false,
        },
      });
    } else {
      dispatch?.({
        type: 'setIsInSearchMode',
        payload: {
          isInSearchMode: true,
        },
      });
      dispatch?.({
        type: 'setSearchLoading',
        payload: {
          searchLoading: true,
        },
      });
      dispatch?.({
        type: 'setActiveOtherKey',
        payload: {
          activeOtherKey: null,
        },
      });
      try {
        const {
          data: { next, results },
        } = await searchUsers({
          keyword,
          limit: PageSizeForSearchUsers,
        });
        const userList = results.map(({ user }) => user);
        dispatch?.({
          type: 'setSearchNext',
          payload: {
            searchNext: next,
          },
        });
        dispatch?.({
          type: 'setSearchResult',
          payload: {
            searchUserList: userList,
          },
        });
      } catch (error) {
        console.error('getSearchUserList', error);
      }
      dispatch?.({
        type: 'setSearchLoading',
        payload: {
          searchLoading: false,
        },
      });
    }
  };

  const loadMoreSearchUsers = async () => {
    const { searchKeyword, searchNext, searchUserList } = state;
    if (!searchKeyword || searchNext === -1) {
      return;
    }
    dispatch?.({
      type: 'setSearchLoadingMore',
      payload: {
        searchLoadingMore: true,
      },
    });
    try {
      const {
        data: { next, results },
      } = await searchUsers({
        keyword: searchKeyword,
        next: searchNext,
        limit: PageSizeForSearchUsers,
      });
      const userList = results.map(({ user }) => user);
      dispatch?.({
        type: 'setSearchNext',
        payload: {
          searchNext: next,
        },
      });
      dispatch?.({
        type: 'setSearchResult',
        payload: {
          searchUserList: searchUserList.concat(userList),
        },
      });
    } catch (error) {}
    dispatch?.({
      type: 'setSearchLoadingMore',
      payload: {
        searchLoadingMore: false,
      },
    });
  };

  const refreshTableList = async (departmentId?: number) => {
    const { isInSearchMode, activeOtherKey, onlyOnRootPage, outsiderPage, disablePage, usersPage, searchKeyword } =
      state;

    try {
      if (isInSearchMode) {
        await getSearchUserList(searchKeyword);
      } else if (!activeOtherKey && departmentId) {
        // 当前在全部成员模式
        getDepartmentUserList({
          departmentId: departmentId,
          page: usersPage,
        });
      } else if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
        // 未分配部门
        await getOnlyOnRootMembers({
          teamId: me!.teamId,
          page: onlyOnRootPage,
        });
      } else if (activeOtherKey === OtherListKeys.OuterSider) {
        // 外部协作者
        await getOutsiderMembers({
          page: outsiderPage,
        });
      } else if (activeOtherKey === OtherListKeys.Disabled) {
        // 禁用成会员
        await getDisabledMembers({
          teamId: me!.teamId,
          page: disablePage,
        });
      } else if (activeOtherKey === OtherListKeys.Inactivated) {
        // 未激活的成员
        await getInactivatedList(me!.teamId);
      } else if (activeOtherKey === OtherListKeys.DingOuters) {
        // 外部成员
        await getExternalList(me!.teamId);
      }
    } catch (error) {}
  };

  return (
    <MembersContext.Provider value={state}>
      <MembersDispatchContext.Provider
        // eslint-disable-next-line react/jsx-no-constructed-context-values
        value={{
          dispatch,
          getOnlyOnRootMembers,
          getOutsiderMembers,
          getDisabledMembers,
          getSubDepartmentList,
          getDepartmentUserList,
          getSearchUserList,
          getInactivatedList,
          getExternalList,
          refreshTableList,
          loadMoreSearchUsers,
        }}
      >
        {children}
      </MembersDispatchContext.Provider>
    </MembersContext.Provider>
  );
};
