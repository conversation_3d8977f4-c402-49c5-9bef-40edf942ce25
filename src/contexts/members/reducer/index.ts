/* eslint-disable complexity */
import type { Action, ItemSource, MembersState } from '../type';

export const membersStateReducer: (state: MembersState, action: Action) => MembersState = (state, action) => {
  switch (action.type) {
    case 'setShadowSubtreeResponse': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUserResponse': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setTreeDataSource': {
      return {
        ...state,
        treeDataSource: action.payload as ItemSource[],
      };
    }
    case 'setOnlyOnRootLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setOnlyOnRoot': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setOutsiderLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setOutsider': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setDisableLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setDisable': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setActiveOtherKey': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchResult': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setIsInSearchMode': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUnactivated': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUnactivatedLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setExternal': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setTreeDataSourceLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setCurOuterItem': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setUsersLoading': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchKeyword': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchLoadingMore': {
      return {
        ...state,
        ...action.payload,
      };
    }
    case 'setSearchNext': {
      return {
        ...state,
        ...action.payload,
      };
    }
    default:
      throw Error('unknown actin.type');
  }
};
