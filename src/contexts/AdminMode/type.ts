export interface CheckAdminModeResponse {
  in_admin_mode: boolean;
}

export interface ChangeOrBindEmailErrorResponse {
  requestId: string;
  error: string;
  errorCode: number;
  tid: string;
  msg: string;
  code: number;
}

export type AuthMode = 'email' | 'sms' | 'password' | undefined;

export enum AdminErrorCode {
  UnAuthErrorCode = 17004,
}

export interface GetAuthKindResponse {
  authMode: AuthMode;
}

export enum EmailStatus {
  /**
   * 0 正常的输入验证码状态
   */
  normal = 0,
  /**
   * 1 未绑定邮箱，弹出需要绑定的对话提示
   */
  noBind = 1,
  /**
   * 2 绑定的时间间隔太短，弹出绑定时间限制弹框
   */
  denied = 2,
}

export interface CheckEmailStatusResponse {
  status: EmailStatus;
  remain_wait_time: number;
}

export interface CheckPasswordExistsResponse {
  adminPasswordIsSet: boolean;
}

export interface AdminModeState {
  /**
   * 是否处于授权状态
   */
  authSuccess: boolean;
  /**
   * 授权方式
   */
  authMode: AuthMode;
  /**
   * 管理员是否已经设置了密码
   */
  adminPasswordIsSet: boolean;
}

export type Action =
  | {
      type: 'setAuthSuccess';
      payload: {
        authSuccess: boolean;
      };
    }
  | {
      type: 'setAdminPasswordIsSet';
      payload: {
        adminPasswordIsSet: boolean;
      };
    }
  | {
      type: 'setAuthMode';
      payload: {
        authMode: AuthMode;
      };
    };
