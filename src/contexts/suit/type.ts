export interface Department {
  id: number;
  name: string;
  teamId: number;
  allMemberCount: number;
  canBother: boolean;
}

export interface SuitItem {
  subType: number;
  productName: string;
  validStartTime: string;
  validEndTime: string;
  seatsCount: number;
  licenseStatus: boolean;
  assignedSeatsCount: number;
}

export interface ResponseError {
  error: {
    code: string;
    message: string;
  };
  requestID: string;
  domain: string;
}

export interface ResponseLicense {
  domain: string;
  requestId: string;
  data: {
    list: SuitItem[];
  };
}

export interface PaginationKitUser {
  subType: string;
  perPage?: string;
  page?: string;
  seatType?: '0' | '1';
}

export interface UnbindParam {
  subType: number;
  userID: number[];
  seatType: number;
}

export interface UsersByDepartment {
  id: string;
  subType: string;
  page: string;
  perPage: string;
  seatType?: '0' | '1';
}

export interface KitUser {
  id: number;
  teamId: number;
  teamRole: string;
  name: string;
  avatar: string;
  email: string;
  enableKit: boolean;
  departmentPath: { departments: Department[] }[];
}

export interface ResponseDepartmentUsers {
  departmentUserIDs: string[];
  total: number;
  users: KitUser[];
}

export interface UserItem {
  userID: number;
  enableKit: boolean;
}

export interface RowUser {
  type: string;
  user: KitUser;
}

export interface ResponseRowUserList {
  data: {
    next: string;
    results: RowUser[];
    userKitList: UserItem[];
  };
  domain: string;
  requestID: string;
}

export interface ResponseBind {
  domain: string;
  requestId: string;
  data: any;
}

export interface SearchKitUser {
  subType: string;
  keyword: string;
  seatType?: '0' | '1';
}

export interface ResponseKitUsers {
  domain: string;
  requestId: string;
  data: {
    total: number;
    users: KitUser[];
    whitelistInfo?: {
      assignedCount: number;
      limitCount: number;
    };
  };
}

export interface DepartmentWithParent extends Department {
  parentId?: number;
}

export interface SeatedItem {
  key: string;
  name: string; // 产品名称
  email: string; // 服务时间
  department: string; // 已购席位
  status: 'servicing' | 'expired'; // 订阅状态
}

export interface ShadowSubtree {
  currentDepartment: Department;
  subdepartments: Department[];
  rootVisible: boolean;
}

export interface SuitState {
  shadowSubtree: ShadowSubtree;
  dataSource: Map<number, DepartmentWithParent>;
  selectorList: DepartmentWithParent[];
  /**
   * 对应套件下已经购买的席位列表
   */
  seatedList: KitUser[];
  /**
   * 套件列表
   */
  suitList: SuitItem[];
  /**
   * 部门用户列表
   */
  departmentUserList: KitUser[];
}

export interface Action {
  type: 'setShadowSubtree' | 'setSelectorList' | 'setSeatedList' | 'setSuitList' | 'setDepartmentUserList';
  shadowSubtree?: ShadowSubtree;
  seatedList?: KitUser[];
  suitList?: SuitItem[];
  departmentUserList?: KitUser[];
}
