import type { Me } from '@/model/Me';
import { fm2 } from '@/modules/Locale';

import { TeamRole } from '../members/type';
import type { TeamInfoResponse } from '../permissions/type';

export function getAccountType(me: Me) {
  const { accountMetadata } = me;
  if (!accountMetadata) {
    return {};
  }
  const {
    isExpired,
    isDingtalk,
    isWework,
    isEnterprise,
    isFreeEnterprise,
    isTrial,
    isPersonalPremium,
    isEnterprisePremium,
  } = accountMetadata;
  return {
    isDingtalk,
    isWework,
    isTrial,
    isExpired,
    isPersonalPremium,
    isEnterprise,
    enterprise: isEnterprise
      ? {
          isFreeEnterprise,
          isEnterprisePremium,
        }
      : undefined,
  };
}
/**
 * 是否是新钉钉用户（这个判断是根据钉钉企业注册的时间判断的，和钉钉付费版概念不同）
 * @param  {Me} user
 * @returns boolean
 */
export const isNewDing = (user: Me) => {
  // return !!user?.membership?.isNewDing;
  return !!user?.id;
};

/** 判断用户是否是钉钉免费版用户
 *
 * @param   user
 * @returns boolean
 */
export const isEnterpriseDingding = (user: Me): boolean => {
  const { isDingtalk, isEnterprise, enterprise } = getAccountType(user);
  return !!(isDingtalk && isEnterprise && enterprise && enterprise.isFreeEnterprise);
};

/**
 * 判断用户是否是个人版
 *
 * @param user
 * @returns
 */
export const isPersonal = (user: Me): boolean => {
  const { isEnterprise } = getAccountType(user);
  return !isEnterprise;
};

/**
 * 判断用户是否是个人免费版
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isPersonalFree = (user: Me): boolean => {
  const { isEnterprise, isPersonalPremium } = getAccountType(user);
  return !isEnterprise && !isPersonalPremium;
};

/**
 * 判断用户是否是个人高级版
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isPersonalPremium = (user: Me): boolean => {
  const { isPersonalPremium } = getAccountType(user);
  return !!isPersonalPremium;
};

/**
 * 判断用户是否是企业版用户
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isEnterprise = (user: Me): boolean => {
  const { isEnterprise } = getAccountType(user);
  return !!isEnterprise;
};

/**
 * 判断用户是否是企业版标准版用户
 * 用户账户类型是企业标准版或过期的企业标准版时返回 true
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isEnterpriseStandard = (user: Me): boolean => {
  const { isEnterprise, enterprise } = getAccountType(user);
  return !!(isEnterprise && enterprise && !enterprise.isFreeEnterprise && !enterprise.isEnterprisePremium);
};

/**
 * 判断用户是否是企业版高级版用户
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isEnterprisePremium = (user: Me): boolean => {
  const { isEnterprise, enterprise } = getAccountType(user);
  return !!(isEnterprise && enterprise && enterprise.isEnterprisePremium);
};

/**
 * 判断用户是否是(企业版)过期用户
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isExpired = (me: Me): boolean => {
  const { isExpired } = getAccountType(me);
  return !!isExpired;
};

/**
 * 判断用户是否是(企业)试用版
 *
 * @param  {Me} user
 * @returns boolean
 */
export const isTrial = (me: Me): boolean => {
  const { isTrial } = getAccountType(me);
  return !!isTrial;
};

/**
 * 判断企业是否通过钉钉途径注册
 *
 * @param  {Team} team
 * @returns boolean
 */
export const isOriginatedFromDingtalk = (team: TeamInfoResponse): boolean => {
  return !!team?.dingtalkCorp;
};

/**
 * 判断企业是否通过非钉钉途径注册
 *
 * @param  {Team} team
 * @returns boolean
 */
export const isNotOriginatedFromDingtalk = (team: TeamInfoResponse): boolean => {
  return !isOriginatedFromDingtalk(team);
};

export const getRoleText = (role: TeamRole) => {
  switch (role) {
    case TeamRole.creator:
      return fm2('Members.creator');
    case TeamRole.admin:
      return fm2('Members.admin');
    case TeamRole.member:
      return fm2('Members.member');
    case TeamRole.disabled:
      return fm2('Members.disabled');
    case TeamRole.pending:
      return fm2('Members.pending');
    default:
      return '-';
  }
};
