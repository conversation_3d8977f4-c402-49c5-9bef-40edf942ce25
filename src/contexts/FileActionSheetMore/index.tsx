import { ActionSheet } from 'antd-mobile';
import type { ActionSheetProps } from 'antd-mobile/es/components/action-sheet';
import { createContext, type ReactNode, useMemo, useState } from 'react';

import { fm } from '@/modules/Locale';

import style from './index.less';
export const FileActionSheetMoreContext = createContext<{
  setOpenPopup: (bol: boolean) => void;
  setActionProp: (actions: ActionSheetProps) => void;
}>({
  setOpenPopup: () => {},
  setActionProp: () => {},
});
export function FileActionSheetMoreProvider({ children }: { children: ReactNode }) {
  const [openPopup, setOpenPopup] = useState(false);
  const [actionProp, setActionProp] = useState<ActionSheetProps>({
    actions: [
      { text: fm('File.reName'), key: 'copy' },
      { text: fm('ShareCollaboration.title'), key: 'edit' },
      { text: fm('Header.favorite'), key: 'edit' },
      { text: fm('File.move'), key: 'edit' },
      {
        text: fm('File.delete'),
        key: 'delete',
        danger: true,
        bold: true,
      },
    ],
    cancelText: fm('Space.cancel'),
    extra: fm('FilePathPicker.folders'),
  });
  const providerData = useMemo(
    () => ({
      setOpenPopup,
      setActionProp,
    }),
    [setOpenPopup, setActionProp],
  );
  return (
    <FileActionSheetMoreContext.Provider value={providerData}>
      <ActionSheet
        className={style.actionSheet}
        visible={openPopup}
        onClose={() => setOpenPopup(false)}
        {...actionProp}
      />
      {children}
    </FileActionSheetMoreContext.Provider>
  );
}
