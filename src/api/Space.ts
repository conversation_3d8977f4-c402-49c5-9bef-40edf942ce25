import { CommonApi } from './Request';

interface QueryParams {
  orderBy?: string;
}

// 获取团队空间列表
export function getSpaceList(query: QueryParams) {
  return CommonApi.get('/spaces', { params: query });
}

//创建团队空间
export function addSpace(name: string) {
  return CommonApi.post('/spaces', { name });
}
//删除团队空间
export function deleteSpace(guid?: string) {
  return CommonApi.delete(`/spaces/${guid}`);
}

//团队空间名称的修改
export function editSpaceName(guid?: string, params?: any) {
  return CommonApi.patch(`/spaces/${guid}`, params);
}

//获取单个空间权限数据
export function getSpacePermission(guid?: string) {
  return CommonApi.get(`/files/${guid}`);
}
