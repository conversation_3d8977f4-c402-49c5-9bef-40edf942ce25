import { dayjs } from '@shimo/lo-dates/src/dayjs';

import { CommonApi } from '@/api/Request';
import type { Filter, RestoreFileRes, SpaceEntity, User } from '@/model/Enterprise/Trash';
import { getFileTypeSource } from '@/pages/pc/Enterprise/common/components/FileTypeSelect/FileTypeSource';

export const PAGE_SIZE = 30;
const ONE_DAY_MILLISECOND = 86400000;

/**
 * 设置管理员模式密码
 */
export const adminPassword = async ({ password }: { password: string }) => {
  const { data } = await CommonApi.post('admin_mode/verify_admin_password', { password });
  return data;
};

/**
 * 检查是否处于管理模式中http://drive.pages.shimo.run/api/v1/admin_mode/check_and_refresh
 */
export const checkManageMode = async () => {
  const {
    data: { in_admin_mode },
  } = await CommonApi.get('admin_mode/check_and_refresh');
  return in_admin_mode;
};

// 格式化请求参数
const normalizeParams = (filter: Filter | undefined) => {
  if (!filter) {
    return {
      keyword: '',
      filter: null,
    };
  }
  const { keyword, fileTypeIds, creator, deletor, begin, end } = filter;

  // 文件被删除的起始时间（毫秒）
  const deletedAt = {
    begin: dayjs(begin).valueOf(),
    end: dayjs(end).valueOf() + ONE_DAY_MILLISECOND - 1,
  };

  // 文件类型 举例说明：勾选文档时，传 "type": [{"type": 2, "subType": [0,-2]}]
  const type = getFileTypeSource()
    .filter((item) => fileTypeIds.includes(item.id))
    .map(({ type, subType }) => ({
      type,
      subType,
    }));

  // 创建者删除者
  const createdBy = creator ? [Number(creator)] : null;
  const deletedBy = deletor ? [Number(deletor)] : null;

  return {
    keyword,
    filter: {
      file: {
        type,
        deletedAt,
        createdBy,
        deletedBy,
      },
    },
  };
};

/**
 * 企业回收站搜索 api
 */
export async function searchApi(filter: Filter | undefined, nextUrl?: string) {
  const response = await CommonApi.post('/sudo/enterprise_trashes/search', {
    ...normalizeParams(filter),
    limit: PAGE_SIZE,
    next: nextUrl,
  });
  const { data } = response;
  return data;
}

/**
 * 企业回收站获取自动清理数据 api
 */
export async function getAutoCleanDataApi(): Promise<{
  userTrashDuration: number;
  enterpriseTrashDuration: number;
}> {
  const url = `/sudo/enterprise_trashes/get_auto_remove_duration`;

  const response = await CommonApi.get(url);

  const { data } = response;

  return data;
}

/**
 * 企业回收站存储自动清理数据 api
 */
export const setAutoCleanDataApi = async ({
  userTrashDuration,
  enterpriseTrashDuration,
}: {
  userTrashDuration: number;
  enterpriseTrashDuration: number;
}) => {
  const url = `/sudo/enterprise_trashes/set_auto_remove_duration`;

  const response = await CommonApi.post(url, {
    userTrashDuration,
    enterpriseTrashDuration,
  });

  return response;
};

/**
 * 企业回收站彻底删除文件
 */
export const deleteFiles = async (ids: string[]) => {
  const url = `/sudo/enterprise_trashes/delete`;

  const response = await CommonApi.post(url, {
    fileGUIDs: ids,
  });
  return response;
};

/**
 * 企业回收站恢复文件
 */
export const restoreFiles = async (
  ids: string[],
  to: string,
): Promise<{
  files: RestoreFileRes[];
}> => {
  const url = `/sudo/enterprise_trashes/recover`;
  const response = await CommonApi.post(url, {
    fileGUIDs: ids,
    to,
  });
  const { data } = response;
  return data;
};

/**
 * api 获取团队空间
 */
export const getSpaces = async (): Promise<SpaceEntity[]> => {
  const response = await CommonApi.get('/spaces');
  const {
    data: { spaces },
  } = response;

  return spaces;
};

/**
 * 获取最近联系的用户http://drive.pages.shimo.run/api/v1/search/recent_contacts
 */
export const getRecentContacts = async (): Promise<User[]> => {
  const {
    data: { response },
  } = await CommonApi.get(`/search/recent_contacts`);
  return response;
};

/**
 * 搜索用户
 */
export const searchUserApi = async (
  keyword: string,
): Promise<{ id: number; name: string; avatar: string; email: string }[]> => {
  const response = await CommonApi.post(`/search`, {
    keyword,
    filter: {
      user: {
        includeRecentContact: false,
        includeTeamMember: true,
        includeDisabledMember: true,
      },
    },
  });
  const {
    data: { results },
  } = response;

  return results.map(({ user }: { user: { id: number; name: string; avatar: string; email: string } }) => user) || [];
};
