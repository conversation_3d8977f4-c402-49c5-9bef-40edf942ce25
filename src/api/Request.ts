import type { AxiosError, AxiosInstance } from 'axios';
import axios from 'axios';

import { fm2 } from '@/modules/Locale';
import type { ErrorData, ResponseError } from '@/types/api';
import type { ErrorCodes } from '@/utils/request/ErrorCodeMap';
import { ErrorCodeMap } from '@/utils/request/ErrorCodeMap';

const CommonApi = axios.create({ baseURL: '/api/v1' });

const addInterceptors = (instance: AxiosInstance) => {
  instance.interceptors.response.use(undefined, async (error: AxiosError & { __fromCache?: any }) => {
    const { response, __fromCache } = error;
    // 如果是我们主动 reject 的缓存命中，直接返回缓存内容作为模拟响应
    if (__fromCache) {
      return Promise.resolve({ data: __fromCache, config: error.config, status: 200 });
    }

    if (response) {
      // backend handled error
      const errorData = response.data as ErrorData;
      const responseError = {
        ...response,
        data: {
          ...errorData,
          msg: ErrorCodeMap[errorData?.code as ErrorCodes]
            ? fm2(ErrorCodeMap[errorData?.code as ErrorCodes])
            : errorData?.msg,
        },
      };
      return Promise.reject(responseError);
    }
    const responseError = {
      ...error,
      data: {
        code: -1,
        msg: fm2('Request.default'),
        tid: '',
      },
    };
    return Promise.reject(responseError);
  });

  instance.interceptors.request.use((config) => {
    const requestUrl = `${config.baseURL}${config.url}`;
    const prefetchPromise = window.__DRIVE_PREFETCH__[requestUrl];
    if (prefetchPromise) {
      return prefetchPromise.then(
        (data) => {
          delete window.__DRIVE_PREFETCH__[requestUrl];
          return Promise.reject({
            __fromCache: data,
            config,
            data: requestUrl,
          });
        },
        () => {
          // 如果数据预取的时候失败了，这里就不做任何处理，这样这个请求就会正常发送出去
          return config;
        },
      );
    }
    return config;
  });
};

addInterceptors(CommonApi);

/**
 * try catch 的替代方法 一般用于简化请求的错误处理
 * 使用方法参照 docs/await-to.md
 * @param promise 要执行的 Promise
 * @param errorExt 传递给err对象的附加信息
 * @return 返回包含错误和数据的元组
 */

function to<T, U = ResponseError>(promise: Promise<T>, errorExt?: object): Promise<[U?, T?]> {
  return promise
    .then<[undefined, T]>((data: T) => {
      return [undefined, data];
    })
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        Object.assign(err as object, errorExt);
      }
      return [err, undefined];
    });
}

// 处理需要返回错误的情况
const catchApiResult = <T>(fn: Promise<T>) => {
  return to<T, ResponseError>(fn);
};

export { catchApiResult, CommonApi, to };
