import { CommonApi } from './Request';

// 企业信息
export async function info(teamID: number) {
  return await CommonApi.get(`/teams/${teamID}`);
}

interface InfoQuery {
  avatar?: string; // 更新头像
}
// 更新企业信息
export async function updateInfo(query: InfoQuery) {
  return await CommonApi.patch(`/teams/mine`, query);
}

// 企业设置
export async function settings(setting: string) {
  return await CommonApi.get(`/team_settings/${setting}`);
}

interface SettingsQuery {
  status: boolean; // 是否开启
  extra: string; // 额外信息
}
// 更新企业设置
export async function updateSettings(settingName: string, data: SettingsQuery) {
  return await CommonApi.patch(`/team_settings/${settingName}`, data);
}

// 获取企业授权信息
export async function license() {
  return await CommonApi.get(`/kits/license`);
}
