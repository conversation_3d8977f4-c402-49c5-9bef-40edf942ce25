import { CommonApi } from '@/api/Request';

import { baseURL } from '../contexts/permissions/service/constant';
import type {
  FeatureKeys,
  PermissionItemKeys,
  SyncThirdPartyResponse,
  TeamInfoResponse,
} from '../contexts/permissions/type';

export const getAlphaFeature = async (key: string) => {
  const response = await CommonApi.get(`${baseURL}/alpha_features/${key}`);
  return (await response.data) as boolean;
};

export const getUserFeatures = async () => {
  const response = await CommonApi.get(`${baseURL}/user/features`);
  return (await response.data) as {
    data: {
      features: FeatureKeys[];
      domain: string;
      requestId: string;
    };
  };
};

export const getCurrentPermissions = async () => {
  const response = await CommonApi.get(`${baseURL}/user/users/me/permissions`);
  return (await response.data) as {
    data: {
      permissions: Array<PermissionItemKeys>;
      domain: string;
      requestId: string;
    };
  };
};

export const getLicenseLimitType = async () => {
  const response = await CommonApi.get(`${baseURL}/user/kits/license/limitType`);
  if (response.status === 200) {
    return (await response.data) as {
      data: {
        limitType: 1 | 0;
      };
      domain: string;
      requestId: string;
    };
  } else {
    return undefined;
  }
};

export const isDingtalkOrWeWork: (id: number) => Promise<{
  isDingtalk: boolean;
  isSSOSAML: boolean;
  isWework: boolean;
}> = async (id: number) => {
  const response = await CommonApi.get(`${baseURL}/teams/${id}/is_dingtalk_or_wework`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

export const getTeamInfo: () => Promise<TeamInfoResponse> = async () => {
  const response = await CommonApi.get(`${baseURL}/teams/mine`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 获取第三方同步状态
 */
export const getStateSyncThirdParty: (id: number) => Promise<SyncThirdPartyResponse> = async (id: number) => {
  const response = await CommonApi.get(`${baseURL}/sync_thirdparty/teams/${id}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};
