import { CommonApi } from './Request';
interface setParams {
  name?: string;
  avatar?: string;
}
// 修改用户信息
export function modifyUserInfo(params: setParams) {
  return CommonApi.patch('/users/me', params);
}

export function uploadAvatarToken(data: any) {
  return CommonApi.post('/uploader/token', data);
}
// 上传头像
export function uploadAvatar(data: any) {
  return CommonApi.post('/uploader/upload', data);
}

//修改密码
export function changePassword(params: any) {
  return CommonApi.post('/auth/password/change', params);
}
