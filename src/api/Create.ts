import { CommonApi } from './Request';
export interface SpaceProp {
  guid: string;
  name: string;
  id: number;
  [key: string]: any;
}

export interface ResponseData<T = any> {
  status: number;
  data: T;
  statusText: string;
}

export async function createSpace(spaceName: string): Promise<ResponseData<SpaceProp>> {
  return await CommonApi.post('/spaces', {
    name: spaceName,
  });
}

export async function createFolder(name: string, guid: string | null): Promise<ResponseData<SpaceProp>> {
  return await CommonApi.post('/files', {
    name,
    folder: guid,
    type: 'folder',
  });
}
