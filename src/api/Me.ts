import { CommonApi } from './Request';

export async function getMe() {
  return CommonApi.get('/users/me');
}

export async function getAnonymousId(): Promise<number> {
  const response = await CommonApi.get<{ anonymousUser: number }>('/auth/anonymous_id');
  return response.data.anonymousUser;
}

export async function login(username: string, password: string) {
  return await CommonApi.postForm('/auth/password/login', {
    email: username,
    password,
  });
}
/** 用户和企业容量 */
export function quota() {
  return CommonApi.get('/quota');
}

//UserCheckPoint 用户卡点 http://drive.pages.shimo.run/api/v1/users/checkpoint
export async function userCheckPoint() {
  const response = await CommonApi.get('/users/checkpoint');
  return response.data;
}
