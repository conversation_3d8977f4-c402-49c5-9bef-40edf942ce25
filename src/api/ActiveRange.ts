import { fetchGet, fetchPost } from '@/utils/request';

import { baseURL } from '../contexts/activeRange/service/constant';
import type {
  GetScopeDiffResponse,
  searchUserAndDepartmentResponse,
  ThirdCountSeatResponse,
  ThirdRootDepartmentResponse,
  ThirdScopeResponse,
  ThirdSubdepartmentsResponse,
  ThirdUserListResponse,
} from '../contexts/activeRange/type';

export const PAGE_SIZE = 50;
export const ROOT_ID = '0';

/**
 * 获取根部门 - 目前用于设置激活范围
 */
export const getThirdRootDepartment: (data: { teamId: number }) => Promise<ThirdRootDepartmentResponse> = async ({
  teamId,
}) => {
  const response = await fetchGet({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/root`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取子部门列表 - 目前用于设置激活范围
 */
export const getSubDepartment: (data: {
  departmentId: string;
  teamId: number;
}) => Promise<ThirdSubdepartmentsResponse> = async ({ departmentId, teamId }) => {
  const response = await fetchGet({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/subdepartments?departmentID=${departmentId}`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取部门下的用户列表 - 目前用于设置激活范围
 */
export const getDepartmentUserList: (data: {
  departmentId: string;
  page: number;
  teamId: number;
}) => Promise<ThirdUserListResponse> = async ({ departmentId, page, teamId }) => {
  const USER_PRE_PAGE = 100;
  if (departmentId === ROOT_ID) {
    return Promise.resolve({ data: { users: [], totalCount: 0 } });
  }

  const response = await fetchGet({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/subusers?departmentID=${departmentId}&page=${page}&perPage=${USER_PRE_PAGE}`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取激活范围下的用户数量 - 目前用于设置激活范围
 */
export const getThirdSelectedScope: (teamId: number) => Promise<ThirdScopeResponse> = async (teamId) => {
  const response = await fetchGet({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/scope`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 保存激活范围 - 目前用于设置激活范围
 */
export const saveThirdSelectedScope = async (
  teamId: number,
  data: {
    departmentIDs: string[];
    userIDs: string[];
    userBlacklist: string[];
  },
) => {
  const response = await fetchPost({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/scope`,
    params: data,
  });
  if (response.ok) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取激活范围下的用户数量 - 目前用于设置激活范围
 */
export const getThirdCountSeat: (
  teamId: number,
  data: {
    departments: string[];
    users: string[];
    userBlacklist: string[];
  },
) => Promise<ThirdCountSeatResponse> = async (teamId: number, data) => {
  const response = await fetchPost({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/count_seat`,
    params: data,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 搜索用户和部门 - 目前用于设置激活范围
 * @param teamId - 团队ID
 * @param keyword - 搜索关键词
 * @returns - 搜索结果
 * */
export const searchUserAndDepartment: (
  teamId: number,
  keyword: string,
) => Promise<searchUserAndDepartmentResponse> = async (teamId, keyword) => {
  const response = await fetchGet({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/search?keyword=${keyword}`,
  });
  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};

/**
 * 获取激活范围和当前选择范围的差异 - 目前用于设置激活范围
 * @param teamId - 团队ID
 * @param data - 请求参数
 * @returns - 差异结果
 * */
export const getScopeDiff: (
  teamId: number,
  data: {
    departmentIDs: string[];
    userIDs: string[];
    userBlacklist: string[];
  },
) => Promise<GetScopeDiffResponse> = async (teamId, data) => {
  const response = await fetchPost({
    url: `${baseURL}/sync_thirdparty/teams/${teamId}/organization/diff`,
    params: data,
  });

  if (response.ok) {
    return response.json();
  } else {
    return Promise.reject(await response.json());
  }
};
