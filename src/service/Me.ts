import { useCallback } from 'react';
import { useIntl } from 'umi';

import * as meApi from '@/api/Me';
import { useMeStore } from '@/store/Me';

export function useUserCheckPoint() {
  const features = useMeStore((state) => state.features);
  const setFeatures = useMeStore((state) => state.setFeatures);
  const setQuotas = useMeStore((state) => state.setQuotas);
  const initFeatureQuotas = useCallback(async () => {
    await meApi.userCheckPoint().then((res) => {
      setFeatures(res.features);
      setQuotas(res.quotas);
    });
  }, [setFeatures, setQuotas]);
  const isNoFeaturePoint = useCallback(
    (key: string) => {
      return !features.includes(key);
    },
    [features],
  );
  const isFeaturePoint = useCallback(
    (key: string) => {
      return features.includes(key);
    },
    [features],
  );
  return {
    initFeatureQuotas,
    isFeaturePoint,
    isNoFeaturePoint,
  };
}
export function useGetMe() {
  const setMe = useMeStore((state) => state.setMe);

  const me = useMeStore((state) => state.me);
  const { initFeatureQuotas } = useUserCheckPoint();
  const intl = useIntl();

  return useCallback(async () => {
    return await meApi.getMe().then(
      (response) => {
        setMe(response.data);
        initFeatureQuotas();
      },
      async (error) => {
        if (error.status === 404) {
          const anonymousId = await meApi.getAnonymousId();
          setMe({
            ...me,
            id: anonymousId,
            name: intl.formatMessage({
              id: 'service.Me.anonymousName',
            }),
          });
        }
      },
    );
  }, [setMe]);
}

export function useLogin() {
  return useCallback(async (username: string, password: string) => {
    await meApi.login(username, password);
  }, []);
}
