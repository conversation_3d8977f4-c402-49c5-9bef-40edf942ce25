import type { FileCreateType, FileCreateTypeValue, FilePathType, FromType, TableActionType } from './Common';
export interface OptionType {
  value: FileCreateTypeValue;
  label: string;
  typeValue: FileCreateType;
  fileValue: FilePathType;
  icon?: React.ReactNode;
  hidden?: boolean;
}

export interface TemplateDataItem {
  id: string | number;
  guid: string;
  img: string;
  icon: string;
  name: string;
  desc_url?: string;
  type: FileCreateTypeValue;
  [key: string]: any;
}
export interface TemplateTypeSelectProps {
  defaultValue?: number;
  options: OptionType[];
  optionRender: (option: { data: OptionType }) => React.ReactNode;
  typeChange: (item?: OptionType) => void;
  required?: boolean;
  disabled?: boolean;
  from?: FromType;
}

export interface FormModalProps {
  formValues?: TemplateDataItem;
  typeSelectProp: TemplateTypeSelectProps;
  type: TableActionType;
  onOk?: (values: TemplateDataItem) => void;
  okText?: string;
  cancelText?: string;
  initialValues?: TemplateDataItem;
  onClose?: () => void;
}
