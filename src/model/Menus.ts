import type { BreadcrumbItemType, BreadcrumbSeparatorType } from 'antd/es/breadcrumb/Breadcrumb';
export enum MenuKey {
  /** 能效看版 */
  efficiency = 'enterprise/efficiency',
  /** 通讯录 */
  members = 'enterprise/members',
  /** 操作日志 */
  audit = 'enterprise/audit',
  /** 套件增值包 */
  packages = 'enterprise/packages',
  /** 在线席位白名单 */
  whitelist = 'enterprise/whitelist',
  /** 企业设置 */
  settings = 'enterprise/settings',
  /** 模版设置 */
  template = 'enterprise/template',
}
export type MenuItem = {
  title: string;
  label: string;
  key: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
};

/**
 * GetItem 获取当前菜单
  GetAllItem 获取当前所有层级菜单
 */
export enum GetMenuTypeEnum {
  GetAllMenuItem,
  GetMenuItem,
}
export type BreadcrumbModel = Partial<BreadcrumbItemType & BreadcrumbSeparatorType>;
