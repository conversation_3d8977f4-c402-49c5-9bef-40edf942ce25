export interface Me {
  id: null | number;
  name: string;
  name_pinyin?: string;
  email?: string;
  avatar?: string;
  status?: 1 | 0;
  teamRole?: string;
  teamTime?: string;
  team_time?: string;
  team_role?: 'creator' | 'admin' | '';
  isSeat?: 1 | 0;
  is_seat?: 1 | 0;
  createdAt?: string;
  merged_into?: null;
  mergedInto?: null;
  teamId: number;
  team_id?: number;
  mobile?: string;
  mobileAccount?: string;
  hasPassword?: boolean;
  accountMetadata: {
    isExpired: boolean;
    isDingtalk: boolean;
    isWework: boolean;
    isEnterprise: boolean;
    isFreeEnterprise: boolean;
    expiredAt: {
      seconds: number;
      nanos: number;
    };
    isTrial: boolean;
    isPersonalPremium: boolean;
    isEnterprisePremium: boolean;
    isEnterpriseLight: boolean;
    editionId: number;
  };
  team: {
    quitAction: string;
    scaleNum: number;
    id: number;
    city: string;
    mobile: string;
    name: string;
    province: string;
    quit_action: string;
    scale: string;
    type: string;
    deletedAt: string;
    updatedAt: string;
    createdAt: string;
    scale_num: number;
    info: string;
    dissolved: boolean;
    isMerged: boolean;
    dingtalkCorp?: boolean;
    weworkCorp?: boolean;
  };
  membership: {
    accountTypeExpiredAt: string;
    accountType: string;
    accountTypeCreatedAt: string;
    isEnterprisePremium: boolean;
    isExpired: boolean;
    isNewDing: boolean;
    isOfficial: boolean;
    editionId: number;
  };
  /**
   * 这个字段需要考虑国际化问题
   */
  editionName?: string;
  requiresIdentityVerification?: boolean;
}
