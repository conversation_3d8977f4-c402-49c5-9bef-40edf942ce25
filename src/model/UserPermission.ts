interface RangeType {
  start: number;
  end: number;
  max: number;
  min: number;
}

export interface AttachLimit {
  /**
   * 是否不检测
   * true：直接通过
   * false：需要进行检测具体数值是否满足要求
   */
  noCheck: boolean;

  /**
   * 单位（不参与计算，仅作显示）
   * minutes、MB
   */
  unit: string;

  /**
   * 描述
   */
  desc: string;

  /**
   * 是否采用范围
   */
  isRange: boolean;

  /**
   * 范围
   */
  range?: Array<RangeType>;

  /**
   * min/max int64 默认表示 MB/个/分钟/行
   */
  max?: number;
  min?: number;
}

export enum UserQuota {
  /**
   * 轻文档	导入大小限制
   */
  ImportLimitRdocSize = 'import_limit_rdoc_size',
}

export enum UserFeature {
  /**
   * 是否启用轻文档
   */
  SupportNewdoc = 'support_newdoc',
  /**
   * 是否启用传统文档
   */
  SupportModoc = 'support_modoc',
  /**
   * 是否启用表格
   */
  SupportMosheet = 'support_mosheet',
  /**
   * 是否启用应用表格
   */
  SupportTable = 'support_table',
  /**
   * 是否启用幻灯片
   */
  SupportPresentation = 'support_presentation',
  /**
   * 是否启用表单
   */
  SupportForm = 'support_form',
}

export type FeatureType = Array<keyof UserFeature | string>;

export type Quotas = {
  [key in UserQuota | string]?: AttachLimit;
};

export interface CheckpointType {
  /**
   * 当features字段存在但在里面找不到key时则证明该功能点未开启
   */
  features: FeatureType;
  /**
   * 所有的quotas都会被返回
   */
  quotas: Quotas;
}
