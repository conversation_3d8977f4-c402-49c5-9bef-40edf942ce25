import type { CSSProperties } from 'react';

export type RowRendererProps = {
  index: number;
  key: string;
  style: CSSProperties;
};
export type SortType = 'default' | 'name' | 'createTime' | 'updateTime' | 'size';
export interface SortModel {
  name: string;
  isFolderPinned: boolean;
  isReversed: boolean;
  layout: ModeTypeEnum;
  selectedType: SortType;
}
export enum ModeTypeEnum {
  list, // 列表
  card, // 卡片
}
