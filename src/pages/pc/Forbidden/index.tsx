import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import styles from './index.less';

const DisabledPage = () => {
  const me = useMeStore((state) => state.me);

  const title = fm('Forbidden.title');

  const tips = fm('Forbidden.tips', { email: me.email });

  return (
    <div className={styles.disabledContent}>
      <div className={styles.disabledTitle}>{title}</div>
      <div className={styles.disabledTips}>{tips}</div>
    </div>
  );
};

export default DisabledPage;
