import { CheckOutlined } from '@ant-design/icons';
import { type MenuProps, message, Tooltip } from 'antd';

import { deleteFile } from '@/api/File';
import { getPositioning } from '@/components/fileList';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { FilePathPicker } from '@/components/Modal/FilePathPicker';
import type { DownLoadFileProps } from '@/hooks/useFileTypeDownload';
import { fileStar, openFile } from '@/utils/file';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

export const items = ({
  record,
  renameCallback,
  i18nText,
  meId,
  reload,
  setShareVisible,
  downloadDiffFile,
}: {
  record: any;
  renameCallback: ({ visible, params }: { visible: boolean; params: { guid: string; fileName: string } }) => void;
  meId: number | null;
  reload: () => void;
  i18nText: { [key: string]: string };
  setShareVisible: (visible: boolean) => void;
  downloadDiffFile: ({ type, guid, fileName }: DownLoadFileProps) => void;
}): MenuItem[] => {
  return [
    // {
    //   label: '当前标签页打开',
    //   key: 'openTag',
    // },
    {
      label: i18nText.newTabOpens,
      key: 'newTabOpens',
      onClick: () => {
        const newArg = record;
        if (record.isShortcut) {
          const { url, type } = record.shortcutSource;
          newArg.type = type;
          newArg.url = url;
        }
        openFile({ type: newArg.type, guid: newArg.guid, url: newArg.url, model: 'new' });
      },
    },
    // {
    //   label: '预览',
    //   key: 'view',
    // },
    // {
    //   label: '定位到所在文件夹',
    //   key: 'position',
    // },
    // {
    //   label: '添加到快速访问',
    //   key: 'quickAccess',
    // },
    {
      label: record.starred ? i18nText.removeStr : i18nText.star,
      key: 'star',
      onClick: () =>
        fileStar({ guid: record.guid, status: record.starred })
          .then(({ type }) => {
            if (type === 'star') {
              message.success(i18nText.starSuccess);
            } else {
              message.success(i18nText.removeStarSuccess);
            }
            reload();
          })
          .catch(({ type }) => {
            if (type === 'star') {
              message.error(i18nText.starError);
            } else {
              message.error(i18nText.removeStarError);
            }
          }),
    },
    { type: 'divider' },
    {
      label: '定位到所在文件夹',
      key: 'positioning',
      onClick: () => {
        getPositioning(record);
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.share + i18nText.collaboration,
      key: 'share',
      onClick: () => {
        setShareVisible(true);
      },
    },

    {
      type: 'divider',
    },
    {
      label: i18nText.download,
      key: 'downloadOther',
      hidden: !record.name?.includes('.') || record.type === 'folder',
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.download,
      key: 'download',
      hidden: ['board', 'form', 'table', 'folder', 'shortcut', 'zip'].includes(record.type),
      children: [
        {
          label: i18nText.png,
          key: 'jpg',
          hidden: !['mindmap', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'Xmind',
          key: 'xmind',
          hidden: !['mindmap'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'PPTX',
          key: 'pptx',
          hidden: !['presentation'].includes(record.type) || ['img'].includes(record.type), // presentation
        },
        {
          label: 'PDF',
          key: 'pdf',
          hidden: !['presentation', 'newdoc'].includes(record.type) || ['img'].includes(record.type), //presentation
        },
        {
          label: 'Excel',
          key: 'xlsx',
          hidden: !['mosheet'].includes(record.type) || ['img'].includes(record.type), // mosheet
        },
        {
          label: 'WPS',
          key: 'wps',
          hidden: !['modoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'Word',
          key: 'docx',
          hidden: !['modoc', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'MarkDown',
          key: 'md',
          hidden: !['newdoc'].includes(record.type) || ['img'].includes(record.type), // newdoc
        },
      ],
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.reName,
      key: 'reName',
      onClick: () => renameCallback({ visible: true, params: { fileName: record.name, guid: record.guid } }),
    },
    {
      label: (
        <Tooltip title={!record.permissionsAndReasons?.canRemove.value ? i18nText.moveTips : null}>
          {i18nText.moveTo}
        </Tooltip>
      ),
      key: 'moveTo',
      disabled: !record.permissionsAndReasons?.canRemove.value,
      onClick: () => {
        FilePathPicker({
          type: 'move',
          locationGuid: 'recent',
          source: [
            {
              name: record.name,
              fileGuid: record.guid,
              parentGuid: record.parent_guid,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ],
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      label: i18nText.copyTo,
      key: 'copyTo',
      hidden: record.isShortcut ? record.shortcutSource.type === 'folder' : record.type === 'folder',
      onClick: () => {
        FilePathPicker({
          type: 'create',
          locationGuid: 'recent',
          source: [
            {
              name: record.name,
              fileGuid: record.guid,
              parentGuid: record.parent_guid,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ],
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.delete,
      key: 'delete',
      danger: true,
      disabled: meId !== record.userId, // 非创建人无法删除
      onClick: () => deleteConfirm({ i18nText, data: record['guid'], api: deleteFile, callback: reload }),
    },
  ];
};

export const dropdownItems = (init: string, i18nText: { [key: string]: string }): MenuProps['items'] => [
  {
    key: 'clear',
    label: i18nText.clearFilter,
  },
  {
    label: i18nText.recentlyOpened,
    key: 'open',
    icon: init === 'open' ? <CheckOutlined /> : <span />,
  },
  {
    label: i18nText.recentlyEdit,
    key: 'edit',
    icon: init === 'edit' ? <CheckOutlined /> : <span />,
  },
];
