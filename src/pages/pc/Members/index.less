.styledMainContent {
  padding: 16px 0;
  height: calc(100% - 72px);
}

.styleTopHeader {
  width: 100%;
  padding: 16px 24px;
  gap: 8px;
  border-bottom: 1px solid var(--theme-separator-color-lighter, rgba(65, 70, 75, 10%));
}

.styleTopHeaderName {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
}

.styleTopHeaderId {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.headerTitleContainer {
  display: flex;
  align-items: center;
}

.headerSubTitle {
  color: var(--theme-text-color-secondary);
  margin-left: 12px;
}

.styledContent {
  margin: 16px;
  border-radius: 8px;
  border: 1px solid var(--theme-separator-color-lighter);
  height: 100%;
  display: flex;
  overflow: hidden;
}
