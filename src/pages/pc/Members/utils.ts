import type { Department, ItemSource } from '../../../contexts/members/type';

export const getDepartmentById = (list: ItemSource[], id: number) => {
  let result: ItemSource | null = null;
  list.forEach((item) => {
    if (result) {
      return null;
    } else if (item.id === id) {
      result = item;
    } else if (item.children) {
      result = getDepartmentById(item.children, id);
    }
  });

  return result as ItemSource | null;
};

export const updateTreeData = ({
  list,
  children,
  key,
  curNode,
  startLevel,
}: {
  list: ItemSource[];
  key: React.Key;
  children: ItemSource[];
  curNode: Department;
  startLevel: number;
}): ItemSource[] => {
  return list.map((node) => {
    if (node.key === key) {
      return {
        ...node,
        ...curNode,
        children: children.map((item) => {
          return { ...item, level: startLevel, parentId: node.id };
        }),
      };
    }
    if (node.children) {
      return {
        ...node,
        children: updateTreeData({
          list: node.children,
          key,
          children,
          curNode,
          startLevel: startLevel + 1,
        }),
      };
    }
    return node;
  });
};

/**
 * 选中的节点是否是目标节点的子节点或者后代节点
 *
 * 虽然树形结构是动态加载的数据
 *
 * 但是当要执行这个判断的时候，两个节点的已经加载完毕
 *
 * 所以不存在某一个节点还没有加载好的情况
 * @param selectedId 选中的节点ID
 * @param targetId 目标节点
 */
export const isChildOfTarget = ({ selectedId, targetNode }: { selectedId: number; targetNode: ItemSource }) => {
  let result = false;

  targetNode.children.forEach((item) => {
    if (item.id === selectedId) {
      result = true;
    } else if (!result && item.children && item.children.length > 0) {
      result = isChildOfTarget({
        targetNode: item,
        selectedId,
      });
    }
  });

  return result;
};
