import userImportTemplate from '@/assets/images/members/user_import_template.xlsx';
import { fm2 } from '@/modules/Locale';

import { OtherListKeys } from '../../../contexts/members/type';

export const RootId = 1;

export enum DepartmentMenuKeys {
  /** 添加子部门 */
  addSubDepartment = 'addSubDepartment',
  /** 修改部门 */
  editDepartment = 'editDepartment',
  /** 删除部门 */
  deleteDepartment = 'deleteDepartment',
}

export const getOtherList = () => [
  {
    id: OtherListKeys.OnlyOnRoot,
    name: fm2('Members.onlyOnRoot'),
  },
  {
    id: OtherListKeys.OuterSider,
    name: fm2('Members.outerSider'),
  },
  {
    id: OtherListKeys.Disabled,
    name: fm2('Members.disableder'),
  },
];

/**
 * 部门允许嵌套的最大等级 按照 lizard-one 逻辑 需要小于这个值
 *
 * lizard-one 是从 0-14 级
 */
export const MaxLevel = 15;

/**
 * 当前部门下存在成员，不能删除
 */
export const EXISTENTIAL_MEMBER_ERROR_CODE = 12007;

export const TEMPLATE_URL: string = userImportTemplate;

export enum ErrorKeys {
  /** 上传文件列名有误 */
  FILE_FORMAT_ERROR = 'FILE_FORMAT_ERROR',
  /** 上传文件为空 */
  FILE_EMPTY_ERROR = 'FILE_EMPTY_ERROR',
  /** 单次导入数量超过最大限制 */
  TOO_MANY_USERS = 'TOO_MANY_USERS',
  /** 单次导入数量超过最大限制 */
  TRIAL_TOO_MANY_USERS = 'TRIAL_TOO_MANY_USERS',
  /** 上传文件格式不正确 */
  OPEN_FILE_ERROR = 'OPEN_FILE_ERROR',
  /** 有成员导入失败 */
  PART_SUCCESS = 'PART_SUCCESS',
  /** 导入数量超过当前企业剩余席位 */
  NOT_ENOUGH_SEAT = 'NOT_ENOUGH_SEAT',
  UNKNOWN = 'UNKNOWN',
}

export const getErrorMessages = (key?: ErrorKeys) => {
  switch (key) {
    case ErrorKeys.FILE_FORMAT_ERROR:
      return {
        title: fm2('Members.fileFormatError'),
        info: fm2('Members.fileFormatErrorTips'),
      };
    case ErrorKeys.FILE_EMPTY_ERROR:
      return {
        title: fm2('Members.fileEmptyError'),
        info: null,
      };
    case ErrorKeys.TOO_MANY_USERS:
      return {
        title: fm2('Members.tooManyUsers'),
        info: fm2('Members.tooManyUsersTips', { total: 500 }),
      };
    case ErrorKeys.TRIAL_TOO_MANY_USERS:
      return {
        title: fm2('Members.tooManyUsers'),
        info: fm2('Members.tooManyUsersTips', { total: 50 }),
      };
    case ErrorKeys.OPEN_FILE_ERROR:
      return {
        title: fm2('Members.openFileError'),
        info: fm2('Members.openFileErrorTips'),
      };
    case ErrorKeys.PART_SUCCESS:
      return {
        title: fm2('Members.partSuccess'),
        info: fm2('Members.partSuccessTips'),
      };
    case ErrorKeys.NOT_ENOUGH_SEAT:
      return {
        title: fm2('Members.notEnoughSeat'),
        info: null,
      };
    case ErrorKeys.UNKNOWN:
    default:
      return {
        title: fm2('Members.uploadError'),
        info: null,
      };
  }
};
