import styled from 'styled-components';

export const StyledOtherList = styled.div`
  padding: 8px 0;
  border-top: 1px solid var(--theme-separator-color-lighter);

  &.hasBottomBorder {
    border-bottom: 1px solid var(--theme-separator-color-lighter);
  }
`;

export const OtherItem = styled.div`
  padding: 8px 16px;
  height: 40px;
  overflow: hidden;
  color: var(--theme-text-color-default);
  text-overflow: ellipsis;
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &.active {
    background: var(--theme-menu-color-bg-active);
  }
`;

export const StyledTitle = styled.div`
  font-size: 14px;
  line-height: 24px;
  display: flx;
`;

export const ExternalItem = styled.div`
  display: flex;
  align-items: center;
  padding-left: 12px;
  cursor: pointer;
  height: 40px;
  margin-bottom: 8px;
`;
