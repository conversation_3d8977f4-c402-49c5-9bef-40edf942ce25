/* eslint-disable complexity */
import classNames from 'classnames';
import React, { useContext } from 'react';

import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { OtherListKeys } from '@/contexts/members/type';
import { useMeStore } from '@/store/Me';

import { getOtherList } from '../../../const';
import { OtherItem, StyledOtherList } from './index.style';

export const OtherList: React.FC = () => {
  const { activeOtherKey, unactivated } = useContext(MembersContext);
  const { dispatch, getOnlyOnRootMembers, getOutsiderMembers, getDisabledMembers, getInactivatedList } =
    useContext(MembersDispatchContext);
  const me = useMeStore((state) => state.me);

  /** 创建者与管理员拥有此权限，普通企业成员不展示「未分配成员」，仅展示企业部门组织架构 */
  const isCreateOrManger = me.teamRole === 'creator' || me.teamRole === 'admin';

  const handleOtherItem = async (id: OtherListKeys) => {
    dispatch?.({
      type: 'setIsInSearchMode',
      payload: {
        isInSearchMode: false,
      },
    });
    dispatch?.({
      type: 'setActiveOtherKey',
      payload: {
        activeOtherKey: id,
      },
    });
    if (me?.id) {
      if (id === OtherListKeys.OnlyOnRoot) {
        // 未分配部门成员
        await getOnlyOnRootMembers?.({ teamId: me.teamId, page: 1 });
      } else if (id === OtherListKeys.OuterSider) {
        // 外部写作者
        await getOutsiderMembers?.({ page: 1 });
      } else if (id === OtherListKeys.Disabled) {
        // 已禁用成员
        await getDisabledMembers?.({ teamId: me.teamId, page: 1 });
      } else if (id === OtherListKeys.Inactivated) {
        await getInactivatedList?.(me.teamId);
      }
    }
  };

  return (
    <StyledOtherList
      className={classNames({
        hasBottomBorder: unactivated?.length > 0,
      })}
    >
      {getOtherList().map((item) => {
        if (item.id === OtherListKeys.OnlyOnRoot && !isCreateOrManger) {
          return null;
        }

        if (item.id === OtherListKeys.OuterSider) {
          // 不展示外部协作者
          return null;
        }

        if (item.id === OtherListKeys.Disabled && !isCreateOrManger) {
          return null;
        }

        return (
          <OtherItem
            key={item.id}
            className={classNames({
              active: item.id === activeOtherKey,
            })}
            onClick={() => {
              handleOtherItem(item.id);
            }}
          >
            {item.name}
          </OtherItem>
        );
      })}
    </StyledOtherList>
  );
};
