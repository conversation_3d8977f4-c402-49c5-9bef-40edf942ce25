import styled from 'styled-components';

export const StyledOtherList = styled.div`
  padding: 8px 0;
  border-top: 1px solid var(--theme-separator-color-lighter);
  flex-shrink: 0;

  &.hasBottomBorder {
    border-bottom: 1px solid var(--theme-separator-color-lighter);
  }
`;

export const OtherItem = styled.div`
  padding: 8px 16px;
  height: 40px;
  overflow: hidden;
  color: var(--theme-text-color-default);
  text-overflow: ellipsis;
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &.active {
    background: var(--theme-menu-color-bg-active);
  }
`;
