/* eslint-disable complexity */
import { DownOutlined } from '@ant-design/icons';
import type { TreeDataNode } from 'antd';
import { Tree } from 'antd';
import type { EventDataNode } from 'antd/es/tree';
import React, { useContext, useEffect, useState } from 'react';

import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import type { ItemSource } from '@/contexts/members/type';
import { useMeStore } from '@/store/Me';
import { isSVGElement } from '@/utils/dom';

import { TreeTitle } from '../../../components/TreeTitle';
import { DepartmentMenuKeys, RootId } from '../../../const';
import { getDepartmentOperations, getRootDepartmentOperations } from '../../../menus';
import { getDepartmentById } from '../../../utils';

interface Props {
  changeActiveDepartmentId: (v: number) => void;
  activeDepartmentId: number;
  setAddSubDepartmentOpen: (v: boolean) => void;
  setEditNameOpen: (v: boolean) => void;
  setDeleteDepartmentOpen: (v: boolean) => void;
  setCantDeleteOpen: (v: boolean) => void;
  setCurrentDepartmentLevel: (v: number) => void;
  setCurrentDepartment: (v: ItemSource | null) => void;
  treeContainerRef: React.RefObject<HTMLDivElement>;
}

export const TreeList: React.FC<Props> = ({
  changeActiveDepartmentId,
  activeDepartmentId,
  setAddSubDepartmentOpen,
  setEditNameOpen,
  setDeleteDepartmentOpen,
  setCantDeleteOpen,
  setCurrentDepartmentLevel,
  setCurrentDepartment,
  treeContainerRef,
}) => {
  const { treeDataSource } = useContext(MembersContext);
  const { dispatch, getSubDepartmentList } = useContext(MembersDispatchContext);
  const { activeOtherKey, external } = useContext(MembersContext);
  const [treeHeight, setTreeHeight] = useState(0);

  const me = useMeStore((state) => state.me);

  const canAddOrRemoveMember = me.teamRole === 'creator' || me.teamRole === 'admin';

  const loadData = (node: EventDataNode<TreeDataNode>) => {
    return getSubDepartmentList(node.key as number, false);
  };

  const handleSelectTreeItem: (
    selectedKeys: React.Key[],
    info: {
      event: 'select';
      selected: boolean;
      node: EventDataNode<TreeDataNode>;
      selectedNodes: TreeDataNode[];
      nativeEvent: MouseEvent;
    },
  ) => void = (selectedKeys, { nativeEvent, node }) => {
    dispatch?.({
      type: 'setActiveOtherKey',
      payload: {
        activeOtherKey: null,
      },
    });
    dispatch?.({
      type: 'setIsInSearchMode',
      payload: {
        isInSearchMode: false,
      },
    });

    const target = nativeEvent.target as HTMLElement;

    setCurrentDepartmentLevel(node.pos.split('-').length - 2);
    if (isSVGElement(target) || target.classList.contains('operation')) {
      return;
    }

    if (selectedKeys.length === 0) {
      return;
    }

    changeActiveDepartmentId(selectedKeys[0] as number);
  };

  const formatTreeData = (treeDataItem: ItemSource): TreeDataNode => {
    const departmentID = treeDataItem.id;
    const curDepartment = getDepartmentById(treeDataSource, departmentID);
    const handleMenuClick = ({
      domEvent,
      key,
    }: {
      domEvent: React.MouseEvent<HTMLElement, MouseEvent>;
      key: DepartmentMenuKeys;
    }) => {
      domEvent.preventDefault();
      domEvent.stopPropagation();
      setCurrentDepartment(curDepartment);
      if (key === DepartmentMenuKeys.addSubDepartment) {
        // 添加子部门
        setAddSubDepartmentOpen(true);
      } else if (key === DepartmentMenuKeys.editDepartment) {
        // 编辑部门
        setEditNameOpen(true);
      } else if (key === DepartmentMenuKeys.deleteDepartment) {
        if (treeDataItem.allMemberCount > 0) {
          // 不能删除对话框
          setCantDeleteOpen(true);
        } else {
          // 删除部门
          setDeleteDepartmentOpen(true);
        }
      }
    };

    const menu = canAddOrRemoveMember
      ? {
          items: departmentID === RootId ? getRootDepartmentOperations() : getDepartmentOperations(),
          onClick: handleMenuClick,
        }
      : undefined;

    const result = {
      key: treeDataItem.key,
      parentId: treeDataItem.parentId,
      title: (
        <TreeTitle
          allMemberCount={treeDataItem.allMemberCount}
          // @ts-ignore
          menu={menu}
          name={treeDataItem.name}
          nodeId={departmentID}
          showNum={treeDataItem.allMemberCount > -1}
        />
      ),
    };

    if (treeDataItem.children && treeDataItem.children.length > 0) {
      return {
        ...result,
        children: treeDataItem.children.map(formatTreeData),
      };
    }

    return result;
  };

  const treeData = treeDataSource.map(formatTreeData);

  useEffect(() => {
    if (treeContainerRef.current) {
      const totalHeight = treeContainerRef.current.clientHeight;
      let otherHeight = 0;
      let treeHeight = 0;
      treeContainerRef.current.childNodes.forEach((item: any) => {
        if (!item.classList.contains('ant-tree')) {
          otherHeight += item.clientHeight;
        }
      });
      const externalMargin = external?.length > 0 ? 8 : 0;
      const otherItemMargin = 18;
      treeHeight = totalHeight - otherHeight - otherItemMargin - externalMargin;
      setTreeHeight(treeHeight);
    }
  }, [treeContainerRef.current, external]);

  return (
    treeData.length > 0 && (
      <Tree
        blockNode
        virtual
        defaultExpandedKeys={[1]}
        height={treeHeight}
        loadData={loadData}
        // draggable={{
        //   icon: false,
        //   nodeDraggable: (node) => {
        //     if (node.key === RootId) {
        //       return false;
        //     }
        //     return true;
        //   },
        // }}
        // allowDrop={({ dropNode }) => {
        //   if ((dropNode as any).parentId === dragNodeParentId.current) {
        //     return true;
        //   }

        //   return false;
        // }}
        // onDragStart={({ node }) => {
        //   dragNodeParentId.current = (node as any).parentId as number;
        // }}
        // onDrop={({ dragNode, dragNodesKeys, dropPosition, dropToGap }) => {
        //   console.log('dragNode', dragNode);
        //   console.log('dragNodesKeys', dragNodesKeys);
        //   console.log('dropPosition', dropPosition);
        //   console.log('dropToGap', dropToGap);
        //   dragNodeParentId.current = -1;
        // }}
        selectedKeys={activeOtherKey ? [] : [activeDepartmentId]}
        switcherIcon={<DownOutlined />}
        treeData={treeData}
        onSelect={handleSelectTreeItem}
      />
    )
  );
};
