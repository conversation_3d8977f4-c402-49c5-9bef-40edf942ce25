/* eslint-disable complexity */
import s18n from '@shimo/simple-i18n';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useContext } from 'react';

import { ReactComponent as NoticeTipIconSVG } from '@/assets/images/members/notice-tip-icon.svg';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { OtherListKeys } from '@/contexts/members/type';
import { PermissionContext } from '@/contexts/permissions';

import {
  ExternalItem as StyledExternalItem,
  ExternalLeft,
  StyledAllMemberIcon,
  StyledDownOutlined,
  StyledName,
  StyledTipLink,
} from './index.style';

export const ExternalItem: React.FC = () => {
  const { activeOtherKey, external } = useContext(MembersContext);
  const { dispatch } = useContext(MembersDispatchContext);
  const { teamInfo } = useContext(PermissionContext);

  const handleOtherItem = async () => {
    dispatch?.({
      type: 'setIsInSearchMode',
      payload: {
        isInSearchMode: false,
      },
    });
    dispatch?.({
      type: 'setActiveOtherKey',
      payload: {
        activeOtherKey: OtherListKeys.DingOuters,
      },
    });
  };

  const externalText = teamInfo?.dingtalkCorp
    ? s18n('未关联钉钉成员')
    : teamInfo?.weworkCorp
      ? s18n('未关联企业微信成员')
      : s18n('企业外部成员');
  const externalTooltipText = teamInfo?.dingtalkCorp
    ? s18n('通讯录中存在未关联当前钉钉企业的成员')
    : teamInfo?.weworkCorp
      ? s18n('通讯录中存在未关联当前企业微信的成员')
      : s18n('通讯录中存在非当前企业的成员');

  return (
    external?.length > 0 && (
      <StyledExternalItem key={OtherListKeys.DingOuters}>
        <StyledDownOutlined />
        <ExternalLeft
          className={classNames({
            active: OtherListKeys.DingOuters === activeOtherKey,
          })}
          onClick={() => {
            handleOtherItem();
          }}
        >
          <StyledAllMemberIcon />
          <StyledName>{externalText}</StyledName>
          <Tooltip
            title={
              <div>
                <div>{externalTooltipText}</div>
                <StyledTipLink href="https://shimo.im/docs/DyYzE9pmdokopM39" target="_blank">
                  {s18n('了解更多>')}
                </StyledTipLink>
              </div>
            }
          >
            <span>
              <NoticeTipIconSVG />
            </span>
          </Tooltip>
        </ExternalLeft>
      </StyledExternalItem>
    )
  );
};
