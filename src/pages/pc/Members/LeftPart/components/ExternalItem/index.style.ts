import { DownOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { ReactComponent as AllMemberIconSVG } from '@/assets/images/members/all-members-icon.svg';
import { singleLineEllipsis } from '@/utils/css';

export const StyledDownOutlined = styled(DownOutlined)`
  transform: rotate(-90deg) scale(0.7);
  margin-left: 4px;
  opacity: 0;
`;

export const StyledAllMemberIcon = styled(AllMemberIconSVG)`
  flex-shrink: 0;
`;

export const StyledName = styled.div`
  margin-left: 10px;
  margin-right: 4px;
  flex: 1;
  width: 0;
  ${singleLineEllipsis};
`;

export const ExternalItem = styled.div`
  display: flex;
  align-items: center;
  padding-left: 12px;
  cursor: pointer;
  height: 40px;
  margin-bottom: 8px;
  flex-shrink: 0;
`;

export const ExternalLeft = styled.div`
  display: flex;
  height: 100%;
  flex: 1;
  align-items: center;
  margin-left: 6px;
  padding-right: 12px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &.active {
    background: var(--theme-menu-color-bg-active);
  }
`;

export const StyledTipLink = styled.a`
  color: unset !important;
  text-decoration: underline !important;
`;
