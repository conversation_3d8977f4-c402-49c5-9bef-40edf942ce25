/* eslint-disable complexity */

import type { MenuProps } from 'antd';
import styled from 'styled-components';

import type { Me } from '@/model/Me';
import { fm } from '@/modules/Locale';

import type { UserItem } from '../../../contexts/members/type';
import { OtherListKeys, TeamRole } from '../../../contexts/members/type';
import { DepartmentMenuKeys } from './const';

export const StyledDeleteText = styled.div`
  color: var(--theme-text-color-alert);
`;

export const StyledNormalText = styled.div`
  color: var(--theme-text-color-default);
`;

export const getRootDepartmentOperations: () => MenuProps['items'] = () => [
  {
    key: DepartmentMenuKeys.addSubDepartment,
    label: <div>{fm('Members.addSubDepartment')}</div>,
  },
];

/**
 * 普通部门菜单
 */
export const getDepartmentOperations: () => MenuProps['items'] = () => [
  {
    key: DepartmentMenuKeys.addSubDepartment,
    label: <div>{fm('Members.addSubDepartment')}</div>,
  },
  {
    key: DepartmentMenuKeys.editDepartment,
    label: <div>{fm('Members.editDepartment')}</div>,
  },
  {
    type: 'divider',
  },
  {
    key: DepartmentMenuKeys.deleteDepartment,
    label: <StyledDeleteText>{fm('Members.deleteDepartment')}</StyledDeleteText>,
  },
];

export enum MemberMenuKeys {
  /**
   * 分割线
   */
  divider = 'divider',
  /**
   * 添加子部门
   */
  addSubDepartment = 'addSubDepartment',
  /**
   * 修改部门
   */
  editDepartment = 'editDepartment',
  /**
   * 删除部门
   */
  deleteDepartment = 'deleteDepartment',
  /**
   * 账号设置
   */
  accountSettings = 'accountSettings',
  /**
   * 修改昵称
   */
  editNickName = 'editNickName',
  /**
   * 换绑邮箱
   */
  changeEmail = 'changeEmail',
  /**
   * 绑定邮箱
   */
  bindEmail = 'bindEmail',
  /**
   * 解绑邮箱
   */
  unbindEmail = 'unbindEmail',
  /**
   * 重置密码
   */
  restPassword = 'restPassword',
  /**
   * 文件交接
   */
  fileHandover = 'fileHandover',
  /**
   * 发起文件交接
   */
  launchFileHandover = 'launchFileHandover',
  /**
   * 管理文件交接
   */
  manageFileHandover = 'manageFileHandover',
  /**
   * 文件交接
   */
  fileHandoverHistory = 'fileHandoverHistory',
  /**
   * 移除成员
   */
  deleteMember = 'deleteMember',
  /**
   * 取消邀请
   */
  cancelInvite = 'cancelInvite',
  /**
   * 设置所在部门
   */
  setDepartment = 'setDepartment',
  /**
   * 查看该用户参与协作的企业文件
   */
  checkCollaboratedFile = 'checkCollaboratedFile',
  /**
   * 邀请该用户加入企业 特殊 情况该用户已加入其他企业，无法邀请
   */
  inviteJoinEnterprise = 'inviteJoinEnterprise',
  /**
   * 将该用户从所有企业文件中移除
   */
  removeFromAllEnterpriseFiles = 'removeFromAllEnterpriseFiles',
  /**
   * 重新激活成员
   */
  reactivateMember = 'reactivateMember',
  /**
   * 激活成员
   */
  activateMember = 'activateMember',
  /**
   * 删除记录
   */
  deleteLog = 'deleteLog',
  /**
   * 禁用成员
   */
  disableMember = 'disableMember',
}

export const getAllMenus = () => ({
  [MemberMenuKeys.addSubDepartment]: {
    key: MemberMenuKeys.addSubDepartment,
    label: <StyledNormalText>{fm('Members.addSubDepartment')}</StyledNormalText>,
  },
  [MemberMenuKeys.editDepartment]: {
    key: MemberMenuKeys.editDepartment,
    label: <StyledNormalText>{fm('Members.editDepartment')}</StyledNormalText>,
  },
  [MemberMenuKeys.deleteDepartment]: {
    key: MemberMenuKeys.deleteDepartment,
    label: <StyledDeleteText>{fm('Members.deleteDepartment')}</StyledDeleteText>,
  },
  [MemberMenuKeys.accountSettings]: {
    key: MemberMenuKeys.accountSettings,
    label: <StyledNormalText>{fm('Members.accountSettings')}</StyledNormalText>,
  },
  [MemberMenuKeys.editNickName]: {
    key: MemberMenuKeys.editNickName,
    label: <StyledNormalText>{fm('Members.editNickName')}</StyledNormalText>,
  },
  [MemberMenuKeys.changeEmail]: {
    key: MemberMenuKeys.changeEmail,
    label: <StyledNormalText>{fm('Members.changeEmail')}</StyledNormalText>,
  },
  [MemberMenuKeys.bindEmail]: {
    key: MemberMenuKeys.bindEmail,
    label: <StyledNormalText>{fm('Members.bindEmail')}</StyledNormalText>,
  },
  [MemberMenuKeys.unbindEmail]: {
    key: MemberMenuKeys.unbindEmail,
    label: <StyledNormalText>{fm('Members.unbindEmail')}</StyledNormalText>,
  },
  [MemberMenuKeys.restPassword]: {
    key: MemberMenuKeys.restPassword,
    label: <StyledNormalText>{fm('Members.restPassword')}</StyledNormalText>,
  },
  // [MemberMenuKeys.fileHandover]: {
  //   key: MemberMenuKeys.fileHandover,
  //   label: <StyledNormalText>{fm('文件交接')}</StyledNormalText>,
  // },
  [MemberMenuKeys.deleteMember]: {
    key: MemberMenuKeys.deleteMember,
    label: <StyledDeleteText>{fm('Members.deleteMember')}</StyledDeleteText>,
  },
  [MemberMenuKeys.cancelInvite]: {
    key: MemberMenuKeys.cancelInvite,
    label: <StyledDeleteText>{fm('Members.cancelInvite')}</StyledDeleteText>,
  },
  [MemberMenuKeys.setDepartment]: {
    key: MemberMenuKeys.setDepartment,
    label: <StyledNormalText>{fm('Members.setDepartment')}</StyledNormalText>,
  },
  [MemberMenuKeys.checkCollaboratedFile]: {
    key: MemberMenuKeys.checkCollaboratedFile,
    label: <StyledNormalText>{fm('Members.checkCollaboratedFile')}</StyledNormalText>,
  },
  [MemberMenuKeys.inviteJoinEnterprise]: {
    key: MemberMenuKeys.inviteJoinEnterprise,
    label: <StyledNormalText>{fm('Members.inviteJoinEnterprise')}</StyledNormalText>,
  },
  [MemberMenuKeys.removeFromAllEnterpriseFiles]: {
    key: MemberMenuKeys.removeFromAllEnterpriseFiles,
    label: <StyledDeleteText>{fm('Members.removeFromAllEnterpriseFiles')}</StyledDeleteText>,
  },
  [MemberMenuKeys.reactivateMember]: {
    key: MemberMenuKeys.reactivateMember,
    label: <StyledNormalText>{fm('Members.reactivateMember')}</StyledNormalText>,
  },
  [MemberMenuKeys.activateMember]: {
    key: MemberMenuKeys.activateMember,
    label: <StyledNormalText>{fm('Members.activateMember')}</StyledNormalText>,
  },
  [MemberMenuKeys.disableMember]: {
    key: MemberMenuKeys.disableMember,
    label: <StyledNormalText>{fm('Members.disableMember')}</StyledNormalText>,
  },
  [MemberMenuKeys.deleteLog]: {
    key: MemberMenuKeys.deleteLog,
    label: <StyledDeleteText>{fm('Members.deleteLog')}</StyledDeleteText>,
  },
  [MemberMenuKeys.divider]: {
    key: MemberMenuKeys.divider,
    label: null,
  },
});

/**
 * 根据一系列角色信息、权限信息、当前数据信息，得到的菜单 key
 */
export const useUserMenus = () => {
  const getUserMenus = ({
    isOutdated,
    member,
    me,
    activeTabKey,
    // syncFromThirdparty,
    // syncPanelEnabled,
    canManageTeamMembers,
  }: {
    member: UserItem;
    /** 是否是过期用户 */
    isOutdated: boolean;
    me: Me;
    activeTabKey: OtherListKeys | 'isSearchMode' | 'treeData';
    syncFromThirdparty: boolean;
    syncPanelEnabled: boolean;
    canManageTeamMembers: boolean;
  }) => {
    const canAddOrRemoveMember = me.teamRole === 'creator' || me.teamRole === 'admin';

    const canModifyEmailOrPassword = me.teamRole === 'creator';

    // const canInitiateFileTransfer = me.teamRole === 'creator' || me.teamRole === 'admin';

    const modifyEmailOrPasswordMenus = canModifyEmailOrPassword
      ? [MemberMenuKeys.bindEmail, ...(member.email ? [MemberMenuKeys.unbindEmail] : []), MemberMenuKeys.restPassword]
      : [];

    // 1. 没有管理人员权限
    if (!canManageTeamMembers) {
      return [];
    }

    const disabled = activeTabKey === OtherListKeys.Disabled || member.teamRole === TeamRole.disabled;

    if (canAddOrRemoveMember && disabled) {
      // member is disabled
      return [
        // ...(canInitiateFileTransfer ? [MemberMenuKeys.fileHandover] : []), 文件交接
        isOutdated ? MemberMenuKeys.deleteLog : MemberMenuKeys.reactivateMember,
      ];
    } else if (activeTabKey === OtherListKeys.OuterSider) {
      // member is outsider
      return [
        MemberMenuKeys.checkCollaboratedFile,
        ...(canAddOrRemoveMember ? [MemberMenuKeys.inviteJoinEnterprise] : []),
        MemberMenuKeys.divider,
        MemberMenuKeys.removeFromAllEnterpriseFiles,
      ];
    } else if (activeTabKey === OtherListKeys.Inactivated || member.isSeat === 0) {
      // member is inactivated
      const menus = [MemberMenuKeys.activateMember, MemberMenuKeys.editNickName, ...modifyEmailOrPasswordMenus];
      if (canAddOrRemoveMember) {
        menus.push(MemberMenuKeys.divider);
        menus.push(MemberMenuKeys.deleteMember);
      }
      return menus;
    } else if (activeTabKey === OtherListKeys.DingOuters) {
      // member is dingtalk user
      return [MemberMenuKeys.disableMember];
    } else if (activeTabKey === OtherListKeys.OnlyOnRoot) {
      // member did not have department
      return [isOutdated ? MemberMenuKeys.deleteLog : MemberMenuKeys.setDepartment];
    } else if (canAddOrRemoveMember && member.teamRole === 'pending') {
      // member is pending
      return [isOutdated ? MemberMenuKeys.deleteLog : MemberMenuKeys.cancelInvite];
    }
    // member is normal member
    const menus = [...modifyEmailOrPasswordMenus];
    if (canAddOrRemoveMember || member.id === me.id) {
      menus.push(MemberMenuKeys.editNickName);
    }

    // if (canInitiateFileTransfer) { 文件交接
    //   menus.push(MemberMenuKeys.fileHandover);
    // }

    if (canAddOrRemoveMember && member.teamRole !== 'creator') {
      menus.push(MemberMenuKeys.divider);
      menus.push(MemberMenuKeys.deleteMember);
    }
    return menus;
  };
  return {
    getUserMenus,
  };
};
export const useDepartmentMenus = () => {
  const getDepartmentMenus = ({
    isRoot,
    canManageDepartment,
    level,
  }: {
    isRoot: boolean;
    level: number;
    canManageDepartment: boolean;
  }) => {
    if (!canManageDepartment) {
      return [];
    }

    if (isRoot) {
      return [MemberMenuKeys.addSubDepartment];
    } else {
      if (level < 15) {
        return [
          MemberMenuKeys.addSubDepartment,
          MemberMenuKeys.editDepartment,
          MemberMenuKeys.divider,
          MemberMenuKeys.deleteDepartment,
        ];
      } else {
        return [MemberMenuKeys.editDepartment, MemberMenuKeys.divider, MemberMenuKeys.deleteDepartment];
      }
    }
  };
  return {
    getDepartmentMenus,
  };
};
