import { Dropdown } from 'antd';
import type { MenuProps } from 'antd/lib';
import React from 'react';

import { ReactComponent as AllMembersIconSVG } from '@/assets/images/members/all-members-icon.svg';
import { ReactComponent as DepartmentIconSVG } from '@/assets/images/members/department-icon.svg';
import { ReactComponent as MoreIconSVG } from '@/assets/images/members/more-icon.svg';

import { RootId } from '../../const';
import styles from './index.less';
interface Props {
  name: string;
  allMemberCount: number;
  nodeId: number;
  menu?: MenuProps;
  showNum: boolean;
}

export const TreeTitle: React.FC<Props> = ({ name, allMemberCount, nodeId, menu, showNum }) => {
  return (
    <div className={styles['treeTitle']}>
      {nodeId === RootId ? <AllMembersIconSVG /> : <DepartmentIconSVG />}
      <div className={styles['textContainer']}>
        <div className={styles['titleText']}>{name}</div>
      </div>
      {menu && (
        <Dropdown menu={menu} trigger={['click']}>
          <div className={styles['operationButton']}>
            <div className={styles['memberCount']}>{showNum ? allMemberCount : ' '}</div>
            <MoreIconSVG className={styles['styledMoreIconSVG']} />
          </div>
        </Dropdown>
      )}
      {!menu && showNum && <div className={styles['memberCount']}>{allMemberCount}</div>}
    </div>
  );
};
