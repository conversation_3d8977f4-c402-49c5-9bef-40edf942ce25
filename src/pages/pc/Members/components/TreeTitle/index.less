.textContainer {
  position: relative;
  flex: 1;
  width: 0;
}

.titleText {
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.memberCount {
  color: var(--theme-text-color-secondary);
}

.operationButton {
  display: flex;
  align-items: center;
  min-width: 20px;
  height: 24px;
  cursor: pointer;
}

.styledMoreIconSVG {
  font-size: 12px;
}

.treeTitle {
  height: 32px;
  display: flex;
  align-items: center;
  padding-right: 16px;

  .styledMoreIconSVG {
    display: none;
  }

  &:hover {
    .styledMoreIconSVG {
      display: block;
    }

    .memberCount {
      display: none;
    }
  }

  & > svg,
  & > div {
    margin-right: 8px;

    &:last-child {
      margin-right: 0;
    }
  }
}
