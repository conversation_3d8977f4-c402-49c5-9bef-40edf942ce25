import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { changeOrBindEmail } from '@/api/adminMode';
import { AdminModeDispatchContext } from '@/contexts/AdminMode';
import type { ChangeOrBindEmailErrorResponse } from '@/contexts/AdminMode/type';
import type { UserItem } from '@/contexts/members/type';
import { MessageContext } from '@/contexts/MessageProvider';
import { isEmail } from '@/utils/validate';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  currentTableUser: UserItem | null;
}

const { Item } = Form;
const Email = 'email';

export const BindEmailModal: React.FC<Props> = ({ changeOpen, open, currentTableUser }) => {
  const [form] = Form.useForm();
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const [loading, setLoading] = useState(false);
  const messageApi = useContext(MessageContext);

  const [inputValue, setInputValue] = useState('');

  if (!currentTableUser) {
    return null;
  }

  const changeOrBindEmailAndRefresh = async ({ email }: { email: string }) => {
    try {
      await changeOrBindEmail({
        email: email,
        teamId: currentTableUser?.teamId,
        userId: currentTableUser?.id,
      });
      message.success(s18n`已发送验证邮件至 ${email}（新邮箱地址），请前往验证`);
    } catch (error: any) {
      message.error(error?.data.msg || '');
    }
  };

  const handleOK = async () => {
    setLoading(true);
    let email = '';
    try {
      const formResult = await form.validateFields();
      email = formResult[Email];
      // 按照后端说法 初始绑定邮箱后 刷新列表并不会更新数据
      // 因为绑定邮箱需要用户本人登录邮箱 并且点击邮箱链接后 才会更新数据
      // 唯一的问题是，后端并没有给发送链接的用户调整状态
      await changeOrBindEmailAndRefresh({ email: formResult[Email] });
      changeOpen(false);
      form.resetFields();
    } catch (error) {
      const errorCode = (error as any as ChangeOrBindEmailErrorResponse)?.errorCode;
      const errorMsg = (error as any as ChangeOrBindEmailErrorResponse)?.error;
      if (errorCode === 17004) {
        // 管控模式授权失败
        // 启动特殊授权模式
        openAuthModal?.({
          onSuccess: () => {
            changeOrBindEmailAndRefresh({ email }).finally();
          },
        });
        // 隐藏 当前绑定对话框
        changeOpen(false);
        return;
      }

      if (errorMsg) {
        messageApi?.error(errorMsg);
      }
    }
    setLoading(false);
  };

  const userEmail = currentTableUser?.email;
  const modalTitle = currentTableUser?.email ? s18n('换绑邮箱') : s18n('绑定邮箱');
  const newEmailText = userEmail ? s18n('新邮箱') : s18n('邮箱地址');
  const isSubmitDisabled = () => {
    if (!inputValue) {
      return true;
    }

    if (!isEmail(inputValue)) {
      return true;
    }

    return false;
  };

  return (
    <Modal
      centered
      closable
      cancelText={s18n('暂不修改')}
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: isSubmitDisabled(),
        loading: loading,
      }}
      okText={s18n('提交')}
      open={open}
      title={modalTitle}
      width={420}
      onCancel={() => {
        changeOpen(false);
        form.resetFields();
      }}
      onOk={handleOK}
    >
      {userEmail && (
        <Item label={s18n('当前邮箱')} layout="vertical">
          <Input disabled value={userEmail} />
        </Item>
      )}
      <Form form={form} layout="vertical" requiredMark={false}>
        <Item
          label={newEmailText}
          name={Email}
          rules={[
            {
              required: true,
              message: s18n('请输入邮箱地址'),
            },
            {
              validator: (_, value) => {
                if (isEmail(value)) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(s18n('邮箱格式错误'));
                }
              },
            },
          ]}
          validateFirst={true}
        >
          <Input
            onChange={(e) => {
              setInputValue(e.target.value);
              form.validateFields();
            }}
          />
        </Item>
      </Form>
    </Modal>
  );
};
