import s18n from '@shimo/simple-i18n';
import { Modal } from 'antd';
import React from 'react';

import styles from './index.less';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  changeActiveRangeOpen: (v: boolean) => void;
}

export const ActiveOrDisableModal: React.FC<Props> = ({ open, changeOpen, changeActiveRangeOpen }) => {
  const handleCancel = () => {
    changeOpen(false);
  };

  const handleOk = () => {
    // 设置激活范围
    changeActiveRangeOpen(true);
    changeOpen(false);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okText={s18n('设置激活范围')}
      open={open}
      title={s18n('激活/禁用成员')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <div className={styles['styledContext']}>
        {s18n('当前通讯录从外部系统同步，请通过「设置激活范围」激活/禁用成员。')}
      </div>
    </Modal>
  );
};
