import s18n from '@shimo/simple-i18n';
import { Avatar, Tooltip } from 'antd';
import React from 'react';

import styles from './index.less';

interface Props {
  avatar: string;
  name: string;
  isMe: boolean;
}

export const TableUserItem: React.FC<Props> = ({ avatar, name, isMe }) => {
  return (
    <div className={styles['styledUserItem']}>
      <Avatar className={styles['styledUserAvatar']} size={28} src={avatar} />
      <div className={styles['styledUserName']}>
        <Tooltip title={name}>
          <span>
            {name}
            {isMe && `(${s18n('我')})`}
          </span>
        </Tooltip>
      </div>
    </div>
  );
};
