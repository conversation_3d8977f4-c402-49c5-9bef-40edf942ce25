import { DownOutlined } from '@ant-design/icons';
import s18n from '@shimo/simple-i18n';
import type { TreeDataNode } from 'antd';
import { message, Modal, Tree } from 'antd';
import type { EventDataNode } from 'antd/es/tree';
import React, { useContext, useState } from 'react';

import { addMembersByIds } from '@/api/Members';
import { ReactComponent as DepartmentIconSVG } from '@/assets/images/members/department-icon.svg';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { type ItemSource, OtherListKeys, type UserItem } from '@/contexts/members/type';

import { RootId } from '../../const';
import { TreeTitle } from '../TreeTitle';
import {
  ClearButton,
  LeftContent,
  RightContent,
  SelectedItem,
  SelectedList,
  SelectedNameContainer,
  StyledClose,
  StyledCloseContainer,
  StyledContentHeader,
  StyledModalContent,
  StyledName,
  StyledTitle,
  TreeListContainer,
} from './index.style';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  selectedUsers: UserItem[];
  user: UserItem | null;
}

export const ChangeDepartmentModal: React.FC<Props> = ({ changeOpen, open, selectedUsers, user }) => {
  const { treeDataSource, activeOtherKey } = useContext(MembersContext);
  const { getSubDepartmentList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  const formatTreeData = (treeDataItem: ItemSource): TreeDataNode => {
    const result = {
      key: treeDataItem.key,
      title: (
        <TreeTitle
          allMemberCount={treeDataItem.allMemberCount}
          name={treeDataItem.name}
          nodeId={treeDataItem.id}
          showNum={false}
        />
      ),
      name: treeDataItem.name,
    };

    if (treeDataItem.children && treeDataItem.children.length > 0) {
      return {
        ...result,
        children: treeDataItem.children.map(formatTreeData),
      };
    }

    return result;
  };

  const treeData = treeDataSource.map(formatTreeData);
  const [selectedDepartments, setSelectedDepartments] = useState<TreeDataNode[]>([]);

  const loadData = (node: EventDataNode<TreeDataNode>) => {
    return getSubDepartmentList(node.key as number);
  };

  const handleSelectTreeItem = (
    _: any,
    {
      selectedNodes,
    }: {
      selectedNodes: TreeDataNode[];
    },
  ) => {
    setSelectedDepartments(selectedNodes);
  };

  const handleClickX = (id: number) => {
    setSelectedDepartments(selectedDepartments.filter((item) => item.key !== id));
  };

  const handleClear = () => {
    setSelectedDepartments([]);
  };

  const handleOk = async () => {
    try {
      setLoading(true);
      const userIds =
        activeOtherKey === OtherListKeys.OnlyOnRoot && user ? [user.id] : selectedUsers.map((item) => item.id);

      // 1. 发送变更请求
      await addMembersByIds({
        departmentIDs: selectedDepartments.map((item) => item.key as number),
        userIDs: userIds,
      });
      message.success(s18n('添加成功'));

      // 直接更新根节点
      await getSubDepartmentList?.(RootId);
      changeOpen(false);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      message.error(s18n('添加失败'));
    }
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: selectedDepartments.length === 0,
        loading,
      }}
      open={open}
      title={s18n('选择部门')}
      width={618}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <StyledModalContent>
        <LeftContent>
          <StyledContentHeader>
            <StyledTitle>{s18n`选择部门`}</StyledTitle>
          </StyledContentHeader>
          <TreeListContainer>
            <Tree
              // @ts-ignore
              blockNode
              multiple
              defaultExpandedKeys={[1]}
              loadData={loadData}
              selectedKeys={selectedDepartments.map((item) => item.key)}
              switcherIcon={<DownOutlined />}
              treeData={treeData}
              onSelect={handleSelectTreeItem}
            />
          </TreeListContainer>
        </LeftContent>
        <RightContent>
          <StyledContentHeader>
            <StyledTitle>{s18n`${selectedUsers.length} 位成员将属于以下部门`}</StyledTitle>
            <ClearButton onClick={handleClear}>{s18n('清空')}</ClearButton>
          </StyledContentHeader>
          <SelectedList>
            {selectedDepartments.map((item) => {
              return (
                <SelectedItem key={item.key}>
                  <SelectedNameContainer>
                    <DepartmentIconSVG />
                    <StyledName>{(item as any).name}</StyledName>
                  </SelectedNameContainer>
                  <StyledCloseContainer
                    onClick={() => {
                      handleClickX(item.key as number);
                    }}
                  >
                    <StyledClose />
                  </StyledCloseContainer>
                </SelectedItem>
              );
            })}
          </SelectedList>
        </RightContent>
      </StyledModalContent>
    </Modal>
  );
};
