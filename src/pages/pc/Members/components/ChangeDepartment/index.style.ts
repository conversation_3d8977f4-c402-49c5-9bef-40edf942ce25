import styled from 'styled-components';

import { ReactComponent as CloseIconSVG } from '@/assets/images/members/close-icon.svg';
import { singleLineEllipsis } from '@/utils/css';

export const StyledModalContent = styled.div`
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  height: 420px;
  display: flex;
`;

export const LeftContent = styled.div`
  width: 285px;
  flex-shrink: 0;
`;

export const RightContent = styled.div`
  flex: 1;
  border-left: 1px solid var(--theme-separator-color-lighter);
  border-bottom-color: transparent;

  & > div:first-child {
    & > div:first-child {
      flex: 1;
      width: 0;
    }
  }
`;

export const StyledContentHeader = styled.div`
  display: flex;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledTitle = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 22px;
  ${singleLineEllipsis};
`;

export const ClearButton = styled.div`
  color: var(--theme-link-button-color);
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;
  flex-shrink: 0;

  &:hover {
    color: var(--theme-link-button-color-hover);
  }
`;

export const SelectedItem = styled.div`
  display: flex;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 12px;
  align-items: center;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
`;

export const SelectedList = styled.div`
  margin-top: 6px;
  height: 364px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  ${SelectedItem} {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const StyledName = styled.div`
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  flex: 1;
  width: 0;
  ${singleLineEllipsis};
`;

export const SelectedNameContainer = styled.div`
  display: flex;
  align-items: center;
  flex: 1;

  ${StyledName} {
    margin-left: 8px;
  }
`;

export const StyledCloseContainer = styled.div`
  height: 100%;
  display: flex;
  align-items: center;
`;

export const StyledClose = styled(CloseIconSVG)`
  opacity: 0.6;

  &:hover {
    opacity: 1;
  }
`;

export const TreeListContainer = styled.div`
  flex: 1;
  overflow: auto;
  padding: 0;
  height: 364px;
  overflow-y: auto;
  padding-top: 4px;

  .ant-tree {
    background: transparent;
    padding-left: 8px;

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        height: 100%;
      }
    }

    .ant-tree-node-content-wrapper {
      padding: 0;
    }
  }
`;
