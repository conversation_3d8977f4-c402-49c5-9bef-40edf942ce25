import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { editDepartment } from '@/api/Members';
import { MembersDispatchContext } from '@/contexts/members';
import type { Department } from '@/contexts/members/type';
import { trimInput } from '@/utils/trim';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  department: Department | null;
}

const { Item } = Form;
const DepartmentName = 'departmentName';

export const EditDepartmentNameModal: React.FC<Props> = ({ open, changeOpen, department }) => {
  const [form] = Form.useForm();
  const { getSubDepartmentList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  const [inputValue, setInputValue] = useState<string>();

  React.useEffect(() => {
    if (department) {
      form.setFieldsValue({
        [DepartmentName]: department.name,
      });
    }
  }, [department]);

  if (!department) {
    return null;
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = trimInput(e.target.value);

    form.setFieldValue(DepartmentName, value);

    setInputValue(value);
  };

  const handleOk = async () => {
    const validateData = await form.validateFields();
    setLoading(true);
    try {
      // 更改名称
      await editDepartment({
        id: department.id,
        name: validateData[DepartmentName],
      });
      // 更新树状图数据
      await getSubDepartmentList?.(department.id);
      changeOpen(false);
      message.success(s18n`操作成功`);
    } catch (error: any) {
      message.error(error.data.msg || `操作成功`);
    }
    setLoading(false);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: !inputValue,
        loading,
      }}
      open={open}
      title={s18n('修改部门名称')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical" requiredMark={false} validateTrigger={['onBlur']}>
        <Item
          label={s18n('部门名称')}
          name={DepartmentName}
          rules={[
            {
              required: true,
              message: s18n('请先输入部门名称'),
            },
            {
              pattern: /^[\S\s]+$/g,
              message: s18n('部门名称最少一个字或字符'),
            },
            {
              max: 50,
              message: s18n('部门名称最长 50 个字'),
            },
            { pattern: /(^\S)((.)*\S)?(\S*$)/, message: '前后不能有空格' },
          ]}
          validateTrigger="onChange"
        >
          <Input value={inputValue} onChange={handleInputChange} />
        </Item>
      </Form>
    </Modal>
  );
};
