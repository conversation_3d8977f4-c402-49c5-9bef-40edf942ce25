/* eslint-disable complexity */
import s18n from '@shimo/simple-i18n';
import { Modal, Tooltip } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

import { loadOutsiderFiles, removeOutsiderFileRight } from '@/api/Members';
import { MembersDispatchContext } from '@/contexts/members';
import type { OutsiderFilesResponse, UserItem } from '@/contexts/members/type';
import { MessageContext } from '@/contexts/MessageProvider';

import {
  StyledClear,
  StyledHeader,
  StyledItem,
  StyledItemName,
  StyledList,
  StyledModalBody,
  StyledNameContainer,
} from './index.style';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
}

export const CheckFileModal: React.FC<Props> = ({ open, changeOpen, user }) => {
  const { getOutsiderMembers } = useContext(MembersDispatchContext);
  const [list, setList] = useState<OutsiderFilesResponse>([]);
  const [loading, setLoading] = useState(false);
  const messageApi = useContext(MessageContext);

  if (!user) {
    return null;
  }

  /**
   * 初始化文件列表
   */
  const initCheckFileList = async () => {
    setLoading(true);
    try {
      const list = await loadOutsiderFiles({ id: user.id });
      setList(list);
    } catch (error) {
      const errorMsg = ((error as any).error as string) || s18n('未知错误');
      messageApi?.error(errorMsg);
    }
    setLoading(false);
  };

  /**
   * 取消文件协作权限
   */
  const cancelFileRight = async (guid: string) => {
    setLoading(true);
    try {
      // 1. 发送请求
      await removeOutsiderFileRight({ userId: user.id, guid });
      // 2. 从列表中静态删除某个项目
      setList(list.filter((item) => !(item.guid === guid)));
      // 3. 刷新列表
      await getOutsiderMembers?.({ page: 1 });
      messageApi?.success(s18n('操作成功'));
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      messageApi?.error(errorMsg);
    }
    setLoading(false);
  };

  // eslint-disable-next-line react-hooks/rules-of-hooks
  useEffect(() => {
    initCheckFileList();
  }, []);

  return (
    <Modal
      centered
      closable
      footer={false}
      maskClosable={false}
      open={open}
      title={s18n('查看参与协作的企业文件')}
      width={618}
      onCancel={() => {
        changeOpen(false);
      }}
    >
      <StyledModalBody>
        <StyledHeader>
          <div>{s18n`${user.name} 参与协作的企业文件`}</div>
        </StyledHeader>
        <StyledList>
          {loading ? (
            <span />
          ) : (
            list.map((item) => {
              return (
                <StyledItem key={item.id}>
                  <StyledNameContainer>
                    {/* {getOuterFileIcon(item.type)} */}
                    <StyledItemName>
                      <Tooltip title={item.name}>
                        <span>{item.name}</span>
                      </Tooltip>
                    </StyledItemName>
                  </StyledNameContainer>
                  <StyledClear
                    onClick={() => {
                      cancelFileRight(item.guid);
                    }}
                  >
                    {s18n('取消协作')}
                  </StyledClear>
                </StyledItem>
              );
            })
          )}
        </StyledList>
      </StyledModalBody>
    </Modal>
  );
};
