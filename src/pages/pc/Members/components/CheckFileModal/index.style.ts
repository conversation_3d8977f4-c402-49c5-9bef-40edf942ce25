import styled from 'styled-components';

import { singleLineEllipsis } from '@/utils/css';

export const StyledModalBody = styled.div`
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledHeader = styled.div`
  display: flex;
  padding: 8px 12px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledClear = styled.div`
  color: var(--theme-text-color-alert);
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  flex-shrink: 0;
`;

export const StyledList = styled.div`
  height: 420px;
  overflow: auto;
`;

export const StyledItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  height: 40px;
  box-sizing: border-box;
  margin-bottom: 4px;

  ${StyledClear} {
    display: none;
  }

  &:hover {
    background: var(--theme-menu-color-bg-hover);
    ${StyledClear} {
      display: block;
    }
  }
`;

export const StyledItemName = styled.div`
  ${singleLineEllipsis}

  font-size: 13px;
  line-height: 20px;
  width: 0;
  flex: 1;
`;

export const StyledNameContainer = styled.div`
  display: flex;
  align-items: center;
  flex: 1;

  ${StyledItemName} {
    margin-left: 8px;
  }
`;
