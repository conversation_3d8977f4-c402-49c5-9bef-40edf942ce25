import s18n from '@shimo/simple-i18n';
import React from 'react';

import { CustomModal } from '@/components/CustomModal';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  organizationMessage: string;
}

export const ReminderModal: React.FC<Props> = ({ open, changeOpen, organizationMessage }) => {
  const handleButton = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      maskClosable={false}
      modalType="confirm"
      okText={s18n('知道了')}
      open={open}
      title={s18n('提示')}
      width={420}
      onCancel={handleButton}
      onOk={handleButton}
    >
      {
        /* eslint-disable */
        <div
          dangerouslySetInnerHTML={{
            __html: organizationMessage,
          }}
        />
      }
    </CustomModal>
  );
};
