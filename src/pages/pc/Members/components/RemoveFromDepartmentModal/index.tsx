import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';

import { deleteMembers } from '@/api/Members';
import { CustomModal } from '@/components/CustomModal';
import { MembersDispatchContext } from '@/contexts/members';
import type { UserItem } from '@/contexts/members/type';

import styles from './index.less';

interface Props {
  departmentName: string;
  selectedUsers: UserItem[];
  activeDepartmentId: number;
  open: boolean;
  changeOpen: (v: boolean) => void;
}

export const RemoveFromDepartmentModal: React.FC<Props> = ({
  departmentName,
  selectedUsers,
  activeDepartmentId,
  open,
  changeOpen,
}) => {
  const { refreshTableList, getSubDepartmentList } = useContext(MembersDispatchContext);
  const names = selectedUsers.map((item) => item.name).join('、');
  const [loading, setLoading] = useState(false);

  const refreshData = async () => {
    await refreshTableList?.(activeDepartmentId);
    await getSubDepartmentList?.(activeDepartmentId);
    changeOpen(false);
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 1. 批量移出成员
      const response = await deleteMembers({
        ids: selectedUsers.map((item) => item.id),
        departmentId: activeDepartmentId,
      });
      const successLen = response?.removedUserIDs?.length;

      if (successLen === selectedUsers.length) {
        message.success(s18n`操作成功`);
        await refreshData();
      } else if (successLen && successLen !== selectedUsers.length) {
        message.success(s18n`已移出 ${successLen} 位成员${s18n('，部分成员移出失败，每位成员至少属于一个部门')}`);
        await refreshData();
      } else {
        message.error(s18n('成员移出失败，每位成员至少属于一个部门'));
      }
    } catch (error) {
      message.error(s18n('成员移出失败，每位成员至少属于一个部门'));
      console.error('deleteMembers ->', error);
    }
    setLoading(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
      }}
      okText={s18n`确定移出 ${selectedUsers.length} 位成员`}
      open={open}
      title={s18n`将成员移出${departmentName}？`}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <div className={styles['styledContentText']}>{s18n`确定将 ${names} 移出“${departmentName}”？`}</div>
    </CustomModal>
  );
};
