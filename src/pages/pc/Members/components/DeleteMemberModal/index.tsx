import s18n from '@shimo/simple-i18n';
import { Divider, message, Radio } from 'antd';
import type { RadioChangeEvent } from 'antd/lib';
import type { HookAPI } from 'antd/lib/modal/useModal';
import classNames from 'classnames';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';

import { deleteMember, disabledOrChangeMemberRole } from '@/api/Members';
import { CustomModal } from '@/components/CustomModal';
import { MembersDispatchContext } from '@/contexts/members';
import type { UserItem } from '@/contexts/members/type';
import { PermissionContext } from '@/contexts/permissions';
import { useMeStore } from '@/store/Me';

interface Props {
  modal: HookAPI;
  user: UserItem | null;
  open: boolean;
  changeOpen: (v: boolean) => void;
  activeDepartmentId: number;
}

enum DeleteOrDisable {
  delete = 'delete',
  disabled = 'disabled',
}

const StyledRadio = styled(Radio)`
  .ant-radio.ant-wave-target {
    align-self: flex-start;
    margin-top: 2px;
  }
`;

const StyledContentText = styled.span`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;

  &.disabled {
    opacity: 0.6;
  }
`;

const StyledDeleteWay = styled.span`
  color: var(--theme-text-color-alert);
  font-size: 14px;
  line-height: 22px;
`;

const isPrivateDeploy = true;

const DefaultRadioV = DeleteOrDisable.disabled;

export const DeleteMemberModal: React.FC<Props> = ({ changeOpen, open, user, activeDepartmentId }) => {
  const me = useMeStore((state) => state.me);

  const [radioV, setRadioV] = useState<DeleteOrDisable>(DefaultRadioV);
  const [loading, setLoading] = useState(false);
  const { isDingtalk, isWework, isSSOSAML } = useContext(PermissionContext);
  const { refreshTableList, getSubDepartmentList } = useContext(MembersDispatchContext);
  const disableRemoveOrExit = isDingtalk || isWework || isSSOSAML;

  const handleRadioChange = (e: RadioChangeEvent) => {
    setRadioV(e.target.value);
  };

  const handleOK = async () => {
    if (!user?.id || !me) {
      return;
    }

    setLoading(true);

    try {
      // 1. 发送请求
      if (radioV === DeleteOrDisable.delete) {
        await deleteMember({
          id: user.id,
          action: radioV,
        });
      } else {
        await disabledOrChangeMemberRole({
          id: user.id,
          role: DeleteOrDisable.disabled,
          teamId: me.teamId,
        });
      }

      // 2. 刷新用户列表
      await refreshTableList?.(activeDepartmentId);
      await getSubDepartmentList?.(activeDepartmentId);
      message.success(s18n('操作成功'));
      changeOpen(false);
    } catch (error) {
      if ((error as any).errorCode) {
        const msg = (error as any)?.error || s18n('未知错误');
        message.error(msg);
      }
    }

    setLoading(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      hasPaddingLeft={false}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
        danger: true,
      }}
      okText={s18n('确定移除')}
      open={open}
      title={
        <>
          <span>{s18n.x`即将移除 ${user?.name || ''}`}</span>
          <span>{user?.email ? ` (${user.email})` : ''}</span>
        </>
      }
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOK}
    >
      <Radio.Group defaultValue={DefaultRadioV} onChange={handleRadioChange}>
        <StyledRadio value={DeleteOrDisable.disabled}>
          {s18n('方式一：该账号将被禁用，用户将无法继续登录和使用该账号。')}
        </StyledRadio>
        <Divider style={{ marginTop: 8, marginBottom: 8 }} />
        {!isPrivateDeploy && (
          <StyledRadio disabled={disableRemoveOrExit} value={DeleteOrDisable.delete}>
            <StyledContentText
              className={classNames({
                disabled: disableRemoveOrExit,
              })}
            >
              {s18n('方式二：该账号将被移出企业，用户将以个人身份继续登录和使用该账号。')}
              <StyledDeleteWay>{s18n`此方式将保留用户对企业文件的协作或管理权限，请谨慎选择。`}</StyledDeleteWay>
            </StyledContentText>
          </StyledRadio>
        )}
      </Radio.Group>
    </CustomModal>
  );
};
