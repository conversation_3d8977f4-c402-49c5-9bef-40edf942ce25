import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { changeNickName } from '@/api/Members';
import { modifyUserInfo } from '@/api/Profile';
import { MembersDispatchContext } from '@/contexts/members';
import type { UserItem } from '@/contexts/members/type';
import { useMeStore } from '@/store/Me';
import { trimInput } from '@/utils/trim';

import { StyledItem, StyledLabel } from './index.style';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  currentTableUser: UserItem | null;
  activeDepartmentId: number;
}

const { Item } = Form;
const NickName = 'nickName';
const INVALID_NICKNAME_RE = /[.`~!@#$%^&*()+=[\]{}\\/|;:'",<>?！～…（）—「」【】『』、；：，。《》／？]/;

export const EditNickNameModal: React.FC<Props> = ({ open, changeOpen, currentTableUser, activeDepartmentId }) => {
  const [form] = Form.useForm();
  const { refreshTableList } = useContext(MembersDispatchContext);
  const [inputValue, setInputValue] = useState<string>();
  const [loading, setLoading] = useState(false);

  const me = useMeStore((state) => state.me);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.validateFields();

    const value = trimInput(e.target.value);

    form.setFieldValue(NickName, value);

    setInputValue(value);
  };

  if (!currentTableUser) {
    return null;
  }

  const handleOk = async () => {
    const res = await form.validateFields();
    try {
      setLoading(true);
      if (me.teamRole === 'member') {
        // changeNickName 会有权限问题
        await modifyUserInfo({ name: res[NickName] });
      } else {
        await changeNickName({
          teamId: currentTableUser.teamId,
          userId: currentTableUser.id,
          username: res[NickName],
        });
      }
      message.success(s18n('用户昵称修改成功'));
      await refreshTableList?.(activeDepartmentId);
      changeOpen(false);

      form.resetFields();
      setInputValue(undefined);
    } catch (error: any) {
      message.error(error?.data.msg || s18n('用户昵称修改失败，请稍候再试！'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    changeOpen(false);
    form.resetFields();
    setInputValue(undefined);
  };

  return (
    <Modal
      centered
      closable
      cancelText={s18n('暂不修改')}
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        loading,
        disabled: !inputValue,
      }}
      okText={s18n('提交')}
      open={open}
      title={s18n('修改昵称')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <StyledItem>
        <StyledLabel>{s18n('当前昵称')}</StyledLabel>
        <Input disabled value={currentTableUser.name} />
      </StyledItem>
      <Form form={form} layout="vertical" requiredMark={false} validateTrigger={['onBlur']}>
        <Item
          label={s18n('更换昵称')}
          name={NickName}
          rules={[
            {
              required: true,
              message: s18n('请先输入昵称'),
            },
            {
              max: 20,
              message: s18n('昵称不能超过 20 个字'),
            },
            { pattern: /(^\S)((.)*\S)?(\S*$)/, message: '前后不能有空格' },
            {
              validator: (_, value) => {
                if (!INVALID_NICKNAME_RE.test(value)) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(s18n('昵称包含不支持的符号，请检查并重新输入'));
                }
              },
            },
          ]}
        >
          <Input placeholder={s18n('用户昵称')} onChange={handleInputChange} />
        </Item>
      </Form>
    </Modal>
  );
};
