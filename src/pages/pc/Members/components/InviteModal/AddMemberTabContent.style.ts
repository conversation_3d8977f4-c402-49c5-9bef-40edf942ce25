import { Button } from 'antd';
import TextArea from 'antd/lib/input/TextArea';
import styled from 'styled-components';

export const StyledContent = styled.div<{ $visible: boolean }>`
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  padding: 12px 16px;
  height: 325px;
  overflow: auto;
  box-sizing: border-box;
  display: '${({ $visible }) => ($visible ? 'flex' : 'none')}';
  flex-direction: column;
`;

export const StyledTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
`;

export const StyledButton = styled(Button)`
  display: flex;
  align-items: center;
  margin-top: 8px;
`;

export const StyledTextArea = styled(TextArea)`
  flex: 1;
`;

export const StyledSwitchText = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 22px;
`;

export const StyledAddMemberHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

export const StyledSwitchContainer = styled.div`
  display: flex;
  align-items: center;
  flex-shrink: 0;

  ${StyledSwitchText} {
    margin-right: 8px;
  }
`;

export const StyledAddButton = styled(Button)`
  overflow: hidden;
  max-width: 50%;

  & > span {
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;
