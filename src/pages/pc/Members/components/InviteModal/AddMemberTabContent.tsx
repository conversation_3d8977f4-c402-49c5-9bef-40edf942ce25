import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Switch } from 'antd';
import union from 'lodash/union';
import React, { useContext, useRef, useState } from 'react';

import { addMembersByAccount } from '@/api/Members';
import { ReactComponent as PlusIconSVG } from '@/assets/images/members/plus-icon.svg';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';

import { getDepartmentById } from '../../utils';
import {
  StyledAddButton,
  StyledAddMemberHeader,
  StyledButton,
  StyledContent,
  StyledSwitchContainer,
  StyledSwitchText,
  StyledTextArea,
  StyledTitle,
} from './AddMemberTabContent.style';

interface Props {
  activeDepartmentId: number;
  changeOpen: (v: boolean) => void;
  visible: boolean;
}

interface FormItem {
  key: number;
  value: string;
}

export const AddMemberTabContent: React.FC<Props> = ({ activeDepartmentId, changeOpen, visible }) => {
  const [form] = Form.useForm();
  const scrollRef = useRef<HTMLDivElement>(null);
  const { treeDataSource } = useContext(MembersContext);
  const { refreshTableList, getSubDepartmentList } = useContext(MembersDispatchContext);
  const activeDepartment = getDepartmentById(treeDataSource, activeDepartmentId);

  const [inputs, setInputs] = useState<FormItem[]>([
    {
      key: 0,
      value: '',
    },
  ]);
  const [showTextArea, setShowTextArea] = useState(false);
  const [textAreaContent, setTextAreaContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSwitchChange = (checked: boolean) => {
    setShowTextArea(checked);
  };

  const handleTextAreaChange: React.ChangeEventHandler<HTMLTextAreaElement> = (e) => {
    setTextAreaContent(e.target.value);
  };

  const addAccountsToDepartment = async () => {
    if (loading) {
      return;
    }

    let accounts: string[] = [];
    if (showTextArea) {
      // 使用 textarea 的数据
      const solvedAccount = textAreaContent.replace(/[\r|\n|,]/g, ' ').replace(/\s+/g, ',');
      accounts = union(solvedAccount.split(','));
      if (!accounts.length) {
        message.error(s18n('账号不正确，请重新输入'));
        return;
      }
    } else {
      // 使用 input 的数据
      try {
        const response = await form.validateFields();
        accounts = Object.values(response);
      } catch (error) {
        console.error('err ->', error);
        return;
      }
    }

    // 1. 对 accounts 进行过滤
    // const verifyAccounts = accounts.filter(isMobileOrEmail);
    const verifyAccounts = accounts;

    if (accounts.length === 0) {
      // 没有通过手机号和邮箱地址的校验
      message.error(s18n('账号不正确，请重新输入'));
      return;
    }

    // 2. accounts 已经通过校验，可以发送请求
    setLoading(true);
    try {
      const response = await addMembersByAccount({
        accounts: verifyAccounts,
        departmentID: activeDepartmentId,
      });

      // 3.1. 更新当前部门用户的数据
      await refreshTableList?.(activeDepartmentId);
      // 3.2. 更新当前部门的信息
      await getSubDepartmentList?.(activeDepartmentId);
      setLoading(false);

      // 4. 比对返回结果和发送结果 进行精细化提示
      const sendAccountLen = verifyAccounts.length; // 发送的数据长度
      const invitedAccountLen = response.invitedAccounts?.length; // 返回的数据长度
      const accountLen = accounts.length; // 过滤前数据长度
      if (invitedAccountLen < accountLen) {
        if (sendAccountLen !== accountLen) {
          // 如果发送的数据长度与过滤前的数据长度不同
          message.error(s18n('部分账号不正确，请重新输入'));
          return;
        }
        // 获取没有被邀请的账户
        // 即通过过滤筛选，但是后端仍然没有将账户加进去
        const uninvitedAccounts = verifyAccounts.filter((account) => {
          return !response.invitedAccounts.includes(account);
        });
        if (uninvitedAccounts.length > 0) {
          message.error(s18n`${uninvitedAccounts.join(', ')}  已加入其他企业，邀请失败`);
          return;
        }
        if (uninvitedAccounts.length === 0) {
          // 只有全部添加成功时才关闭 modal
          message.success(s18n('添加成功'));
          changeOpen(false);
        }
      } else {
        // 全部添加成功
        message.success(s18n('添加成功'));
        changeOpen(false);
      }
    } catch (error) {
      const errorMsg = (error as any)?.error || s18n('操作失败');
      message.error(errorMsg);
      console.error('addMembersByAccount ->', error);
    }
    setLoading(false);
  };

  return (
    <>
      <StyledContent $visible={visible}>
        <StyledAddMemberHeader>
          <StyledTitle>{s18n('请输入成员邮箱或手机号，快速添加；添加后成员会收到邀请邮件')}</StyledTitle>
          <StyledSwitchContainer>
            <StyledSwitchText>{s18n('批量添加')}</StyledSwitchText>
            <Switch onChange={handleSwitchChange} />
          </StyledSwitchContainer>
        </StyledAddMemberHeader>
        {showTextArea ? (
          <StyledTextArea
            placeholder={s18n`请输入手机号或邮箱，多个账号之间用逗号“,”分隔`}
            value={textAreaContent}
            onChange={handleTextAreaChange}
          />
        ) : (
          <div>
            <Form form={form} layout="vertical" requiredMark={false} validateTrigger="onBlur">
              {inputs.map((item) => {
                return (
                  <Form.Item
                    key={item.key}
                    label={s18n`成员${item.key + 1}`}
                    name={item.key}
                    rules={[
                      {
                        required: true,
                        message: s18n('请输入成员邮箱或手机号'),
                      },
                      {
                        // validator: (_, value) => {
                        //   if (isMobileOrEmail(value)) {
                        //     return Promise.resolve();
                        //   } else {
                        //     return Promise.reject(
                        //       s18n('请输入正确的手机号或邮箱'),
                        //     );
                        //   }
                        // },
                      },
                    ]}
                    validateFirst={true}
                  >
                    <Input placeholder={s18n('请输入成员邮箱或手机号')} />
                  </Form.Item>
                );
              })}
            </Form>
            <StyledButton
              icon={<PlusIconSVG />}
              onClick={() => {
                setInputs([
                  ...inputs,
                  {
                    key: inputs.length,
                    value: '',
                  },
                ]);
                setTimeout(() => {
                  if (scrollRef.current) {
                    scrollRef.current.scrollIntoView({
                      behavior: 'smooth',
                      block: 'end',
                    });
                  }
                }, 100);
              }}
            >
              {s18n('批量添加')}
            </StyledButton>
            <div ref={scrollRef} style={{ height: 12, marginBottom: -12 }} />
          </div>
        )}
      </StyledContent>
      <div
        style={{
          marginTop: 12,
          display: visible ? 'flex' : 'none',
          flexDirection: 'row-reverse',
        }}
      >
        {activeDepartment && (
          <StyledAddButton loading={loading} type="primary" onClick={addAccountsToDepartment}>
            {s18n`添加到${activeDepartment.name}`}
          </StyledAddButton>
        )}
      </div>
    </>
  );
};
