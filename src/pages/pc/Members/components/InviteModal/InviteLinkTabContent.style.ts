import styled from 'styled-components';

export const StyledTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
`;

// 邀请链接
export const StyledLinkContent = styled.div<{ $visible: boolean }>`
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  padding: 12px 16px;
  height: 349px;
  overflow: auto;
  box-sizing: border-box;
  margin-bottom: 32px;
  ${({ $visible }) => ($visible ? '' : 'display: none;')}
`;
export const StyledInviteHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const StyledSwitchText = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 22px;
`;

export const StyledInviteHeaderRight = styled.div`
  display: flex;

  ${StyledSwitchText} {
    margin-right: 8px;
  }
`;

export const StyledInputLink = styled.div`
  margin-top: 16px;
  width: 100%;
  display: flex;
  padding: 5px 12px;
  align-items: center;
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  background: var(--theme-layout-color-bg-editor);
  box-sizing: border-box;
  margin-bottom: 24px;
  color: var(--theme-text-color-default);

  &.disabled {
    color: var(--theme-text-color-disabled);
  }
`;

export const StyledCopyButtonContainer = styled.div`
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
`;
