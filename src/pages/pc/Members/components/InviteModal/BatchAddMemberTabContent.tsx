import s18n from '@shimo/simple-i18n';
import { Button, Upload } from 'antd';
import type { UploadProps } from 'antd/lib';
import React, { useContext, useState } from 'react';

import { uploadFile } from '@/api/Members';
import { ReactComponent as CheckCircleIcon } from '@/assets/images/members/check-circle-icon.svg';
import { ReactComponent as DownloadIconSVG } from '@/assets/images/members/download-icon.svg';
import { ReactComponent as ErrorIconSVG } from '@/assets/images/members/error-icon.svg';
import LoadingGIF from '@/assets/images/members/loading-gif.gif';
import { ReactComponent as UploadIconSVG } from '@/assets/images/members/upload-icon.svg';
import { ReactComponent as XlsIconSVG } from '@/assets/images/members/xls.svg';
import { MembersDispatchContext } from '@/contexts/members';
import { concatImageUrl } from '@/utils/image';

import { ErrorKeys, getErrorMessages, TEMPLATE_URL } from '../../const';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>eader,
  ErrorInfo,
  ErrorTitle,
  StyledButton,
  StyledFileName,
  StyledFirstBlock,
  StyledImg,
  StyledInfo,
  StyledLeft,
  StyledLoadingContainer,
  StyledSecondBlock,
  StyledStepTitle,
  StyledSuccussContainer,
  StyledSuccussText,
  StyledUploadButtons,
  StyledUploadContainer,
  StyledUploadText,
} from './BatchAddMemberTabContent.style';

interface UploadErrorResponse {
  error: {
    code: ErrorKeys;
    message: string;
  };
  requestId: string;
  domain: string;
}
const { Dragger } = Upload;

export enum InviteTabKeys {
  addMember = 'addMember',
  batchAddMember = 'batchAddMember',
  inviteLink = 'inviteLink',
}

enum LoadingStatus {
  /**
   * 最开始的状态
   */
  none = 'none',
  /**
   * 文件打开完成 - 即打开文件对话框，选择完了文件，但是还没有发送请求
   */
  done = 'done',
  /**
   * 上传中
   */
  uploading = 'uploading',
  /**
   * 成功
   */
  success = 'success',
  /**
   * 失败
   */
  error = 'error',
  /**
   * 部分数据成功，部分失败
   */
  partSuccess = 'partSuccess',
  /**
   * 特殊成功 UI
   */
  specialSuccess = 'specialSuccess',
}

export const BatchAddMemberTabContent: React.FC<{ visible: boolean; activeDepartmentId: number }> = ({
  visible,
  activeDepartmentId,
}) => {
  const [uploading, setUploading] = useState<LoadingStatus>(LoadingStatus.none);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [errorKey, setErrorKey] = useState<ErrorKeys | null>(null);
  const [responseFile, setResponseFile] = useState<ArrayBuffer | null>();
  const [successTotal, setSuccessTotal] = useState(0);

  const { getSubDepartmentList, getDepartmentUserList } = useContext(MembersDispatchContext);

  // ERROR_MESSAGES
  const uploadButtonProps: UploadProps = {
    name: 'file',
    accept: '.xls,.xlsx',
    multiple: false,
    showUploadList: false,
    customRequest: (options) => {
      const { file } = options;
      if (file) {
        setSelectedFile(file as File);
        setUploading(LoadingStatus.done);
      }
    },
  };

  const dropProps: UploadProps = {
    ...uploadButtonProps,
    openFileDialogOnClick: false,
  };

  const handleSendData = async () => {
    if (!selectedFile) {
      return;
    }
    // 初始化
    setResponseFile(null);
    setUploading(LoadingStatus.uploading);
    setSuccessTotal(0);

    try {
      // 1. 发送文件请求
      const response = await uploadFile(selectedFile);
      // @ts-ignore
      const contentDisposition = response.headers['content-disposition'];
      // @ts-ignore
      const arrayBuffer = response?.data;

      if (response.status === 201) {
        if (contentDisposition?.includes('success=true')) {
          // 这是私部环境完全成功的情况
          // 返回的文件是 导入后的结果
          // 展示特殊成功 UI
          setUploading(LoadingStatus.specialSuccess);
        } else {
          // 普通环境的情况是部分成功的结果
          // 文件的内容详细列举了哪些数据出现了错误
          // 展示部分错误 UI
          setUploading(LoadingStatus.partSuccess);
          setErrorKey(ErrorKeys.PART_SUCCESS);
        }
        setResponseFile(arrayBuffer);
      } else {
        // 这是普通环境完全正确的情况
        const afterFormatResult = JSON.parse(new TextDecoder().decode(arrayBuffer));
        const total = afterFormatResult.data.total as number;
        setSuccessTotal(total);
        setUploading(LoadingStatus.success);
      }
    } catch (error: any) {
      let errorBuffer;
      try {
        errorBuffer = await error.arrayBuffer();
      } catch (error) {
        // 特殊错误 一般到了这里 认为后端服务器出了问题 已经没有办法处理正常处理错误了
        setErrorKey(ErrorKeys.UNKNOWN);
        setUploading(LoadingStatus.error);
        return;
      }
      const afterFormatResult = JSON.parse(new TextDecoder().decode(errorBuffer)) as UploadErrorResponse;

      const errorCode = afterFormatResult?.error?.code;
      if (!errorCode) {
        //  i 如果返回的错误结果超出预期 报错未知错误
        setErrorKey(ErrorKeys.UNKNOWN);
        setUploading(LoadingStatus.error);
        return;
      }
      if (!getErrorMessages(errorCode)) {
        // 没有找到对应的错误类型
        setErrorKey(ErrorKeys.UNKNOWN);
        setUploading(LoadingStatus.error);
        return;
      }

      // c 错误
      //    ii 如果范围的结果符合格式要求，根据返回的数据暂时内容
      setErrorKey(errorCode);
      setUploading(LoadingStatus.error);
    } finally {
      await getSubDepartmentList?.(activeDepartmentId!);
      // 4. 更新列表
      await getDepartmentUserList?.({
        departmentId: activeDepartmentId!,
        page: 1,
      });
    }
  };

  const downloadFile = () => {
    const a = document.createElement('a');
    a.href = TEMPLATE_URL;
    a.download = s18n('通讯录导入模板.xlsx');
    a.click();
  };

  const downloadResponseFile = () => {
    if (responseFile) {
      const url = window.URL.createObjectURL(
        new Blob([responseFile], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        }),
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = s18n('导入失败列表.xlsx');
      a.click();
      a.remove();
    }
  };

  return (
    <div
      style={{
        display: visible ? 'block' : 'none',
      }}
    >
      <StyledFirstBlock>
        <StyledLeft>
          <StyledStepTitle>{s18n('第一步：下载通讯录导模版')}</StyledStepTitle>
          <StyledInfo>{s18n('请根据表格内的提示信息完善内容')}</StyledInfo>
        </StyledLeft>
        <StyledButton icon={<DownloadIconSVG />} onClick={downloadFile}>
          {s18n('下载模版')}
        </StyledButton>
      </StyledFirstBlock>
      <StyledSecondBlock>
        <StyledStepTitle>{s18n('第二步：上传填写好的表格')}</StyledStepTitle>
        {uploading === LoadingStatus.none && (
          <Dragger {...dropProps}>
            <UploadIconSVG />
            <StyledUploadText>{s18n('可将文件直接拖拽至此区域进行上传；仅支持 xls、xlsx 格式文件')}</StyledUploadText>
            <Upload {...uploadButtonProps}>
              <Button>{s18n('选择上传文件')}</Button>
            </Upload>
          </Dragger>
        )}
        {(uploading === LoadingStatus.uploading || uploading === LoadingStatus.done) && (
          <StyledUploadContainer>
            <XlsIconSVG />
            <StyledFileName>{selectedFile?.name}</StyledFileName>
            {uploading === LoadingStatus.done ? (
              <StyledUploadButtons>
                <Upload {...uploadButtonProps}>
                  <Button>{s18n('重新选择')}</Button>
                </Upload>
                <Button type="primary" onClick={handleSendData}>
                  {s18n('开始上传')}
                </Button>
              </StyledUploadButtons>
            ) : (
              <StyledLoadingContainer>
                <StyledImg src={concatImageUrl(LoadingGIF)} />
                <div>{s18n('正在上传')}</div>
              </StyledLoadingContainer>
            )}
          </StyledUploadContainer>
        )}
        {uploading === LoadingStatus.success && (
          <StyledUploadContainer>
            <StyledSuccussContainer>
              <CheckCircleIcon />
              <StyledSuccussText>{s18n`成功导入 ${successTotal} 人`}</StyledSuccussText>
            </StyledSuccussContainer>
            <Upload {...uploadButtonProps}>
              <Button type="primary">{s18n('再次导入')}</Button>
            </Upload>
          </StyledUploadContainer>
        )}
        {uploading === LoadingStatus.error && errorKey && (
          <StyledUploadContainer>
            <ErrorContainer>
              <ErrorHeader>
                <ErrorIconSVG />
                <ErrorTitle> {getErrorMessages(errorKey).title}</ErrorTitle>
              </ErrorHeader>
              {getErrorMessages(errorKey).info && <ErrorInfo>{getErrorMessages(errorKey).info}</ErrorInfo>}
            </ErrorContainer>

            <StyledUploadButtons>
              <Button
                type="primary"
                onClick={() => {
                  setUploading(LoadingStatus.none);
                }}
              >
                {s18n('重新上传')}
              </Button>
            </StyledUploadButtons>
          </StyledUploadContainer>
        )}
        {uploading === LoadingStatus.partSuccess && errorKey && (
          <StyledUploadContainer>
            <ErrorContainer>
              <ErrorHeader>
                <ErrorIconSVG />
                <ErrorTitle> {getErrorMessages(errorKey).title}</ErrorTitle>
              </ErrorHeader>
              {getErrorMessages(errorKey).info && <ErrorInfo>{getErrorMessages(errorKey).info}</ErrorInfo>}
            </ErrorContainer>

            <StyledUploadButtons>
              <Button onClick={downloadResponseFile}>{s18n('下载导入失败列表')}</Button>
              <Button
                type="primary"
                onClick={() => {
                  setUploading(LoadingStatus.none);
                }}
              >
                {s18n('重新上传')}
              </Button>
            </StyledUploadButtons>
          </StyledUploadContainer>
        )}
        {uploading === LoadingStatus.specialSuccess && (
          <StyledUploadContainer>
            <StyledSuccussContainer>
              <CheckCircleIcon />
              <StyledSuccussText>{s18n('成功导入')}</StyledSuccussText>
            </StyledSuccussContainer>

            <StyledUploadButtons>
              <Button onClick={downloadResponseFile}>{s18n('下载导入结果')}</Button>
              <Button
                type="primary"
                onClick={() => {
                  setUploading(LoadingStatus.none);
                }}
              >
                {s18n('重新上传')}
              </Button>
            </StyledUploadButtons>
          </StyledUploadContainer>
        )}
      </StyledSecondBlock>
    </div>
  );
};
