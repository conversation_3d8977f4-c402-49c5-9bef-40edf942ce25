import { Button } from 'antd';
import styled, { css } from 'styled-components';

export const StyledButton = styled(Button)`
  display: flex;
  align-items: center;
  margin-top: 8px;
`;

// 批量导入
export const BlockStyle = css`
  padding: 12px 16px;
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
`;

export const StyledFirstBlock = styled.div`
  ${BlockStyle}

  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

export const StyledSecondBlock = styled.div`
  ${BlockStyle}

  height: 269px;
  box-sizing: border-box;
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;

  & > div:first-child {
    margin-bottom: 12px;
  }

  & > .ant-upload-wrapper {
    background: transparent;
    border-radius: 4px;
    border: 1px solid var(--theme-separator-color-lighter);
    flex-grow: 1;

    .ant-upload-drag {
      border-color: transparent;

      &:hover {
        border-color: transparent;
      }
    }

    .ant-upload-drag-container {
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }

    .ant-upload-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: auto;
    }

    .ant-upload-select,
    .ant-upload-select .ant-upload {
      padding: 0;
    }
  }
`;

export const StyledLeft = styled.div`
  display: flex;
  flex-direction: column;

  & > div {
    &:last-child {
      margin-left: 0;
    }
  }
`;

export const StyledStepTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
`;

export const StyledInfo = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
`;

export const StyledUploadContainer = styled.div`
  display: flex;
  padding: 40px 0;
  flex-direction: column;
  align-items: center;
  background: var(--theme-layout-color-bg-new-page);
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  margin-top: 12px;
  flex-grow: 1;
  justify-content: center;

  .ant-upload-wrapper {
    flex-grow: unset;
  }

  & > div,
  & > span,
  & > span.ant-upload-wrapper,
  & > button {
    margin-top: 12px;

    &:first-child {
      margin-top: 0;
    }
  }
`;

export const StyledUploadText = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
`;

export const StyledFileName = styled.div`
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
`;

export const StyledImg = styled.img`
  width: 20px;
  height: 20px;
`;

export const StyledLoadingContainer = styled.div`
  display: flex;

  ${StyledImg} {
    margin-right: 8px;
  }
`;

export const StyledUploadButtons = styled.div`
  display: flex;
  align-items: center;

  & > span,
  & > button {
    margin-left: 8px;

    &:first-child {
      margin-left: 0;
    }
  }
`;

export const StyledSuccussText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
`;

export const StyledSuccussContainer = styled.div`
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  display: flex;
  align-items: center;

  ${StyledSuccussText} {
    margin-left: 12px;
  }
`;

export const ErrorContainer = styled.div`
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-left: 24px;
  padding-right: 24px;
`;

export const ErrorHeader = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 4px;
`;

export const ErrorTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  margin-left: 12px;
`;

export const ErrorInfo = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
`;
