import s18n from '@shimo/simple-i18n';
import { Button, message, Switch } from 'antd';
import type { SwitchChangeEventHandler } from 'antd/lib/switch';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';

import { closeInviteLink, getInviteLink, openInviteLink } from '@/api/Members';

import {
  StyledCopyButtonContainer,
  StyledInputLink,
  StyledInviteHeader,
  StyledInviteHeaderRight,
  StyledLinkContent,
  StyledSwitchText,
  StyledTitle,
} from './InviteLinkTabContent.style';

interface Props {
  visible: boolean;
}

export const InviteLinkTabContent: React.FC<Props> = ({ visible }) => {
  const [checked, seChecked] = useState(false);

  const disableLinText = s18n('邀请链接已关闭');
  const [linkValue, setLinkValue] = useState(disableLinText);
  const [switchLoading, setSwitchLoading] = useState(false);
  const [originalLink, setOriginalLink] = useState('');

  /**
   * 切换链接可用状态
   */
  const handleSwitchChange: SwitchChangeEventHandler = async (checked) => {
    setSwitchLoading(true);

    if (checked) {
      try {
        const { inviteGuid } = await openInviteLink();
        if (inviteGuid) {
          setLinkValue(
            `${window.location.protocol}//${window.location.host}/joinCompany/${
              originalLink ? originalLink.replace(/^[a-zA-Z0-9]{16}/, inviteGuid) : inviteGuid
            }`,
          );
          seChecked(true);
        }
      } catch (error) {
        console.error(error);
        message.error(s18n('开启邀请链接失败'));
      }
      setSwitchLoading(false);
    } else {
      try {
        await closeInviteLink();
        seChecked(false);
        setLinkValue(disableLinText);
      } catch (error) {
        console.error(error);
        message.error(s18n('关闭邀请链接失败'));
      }
    }
    setSwitchLoading(false);
    seChecked(checked);
  };

  /**
   * 初始化链接详情
   */
  const intInviteLink = async () => {
    setSwitchLoading(true);
    try {
      const { inviteGuid } = await getInviteLink();
      if (inviteGuid) {
        setLinkValue(`${window.location.protocol}//${window.location.host}/joinCompany/${inviteGuid}`);
        seChecked(true);
        setOriginalLink(inviteGuid);
      }
    } catch (error) {
      console.error(error);
    }
    setSwitchLoading(false);
  };

  useEffect(() => {
    intInviteLink();
  }, []);

  const handleCopyButton = async () => {
    if (linkValue) {
      try {
        // 先用新的 API
        await navigator.clipboard.writeText(linkValue);
        message.success(s18n('复制成功！'));
      } catch (error) {
        // 备用复制方法;
        const textArea = document.createElement('textarea');
        textArea.value = linkValue;
        document.body.appendChild(textArea);
        textArea.select();

        try {
          document.execCommand('copy');
          message.success(s18n('复制成功！'));
          document.body.removeChild(textArea);
        } catch (error) {
          message.error(s18n('复制失败！'));
        }
      }
    }
  };

  return (
    <StyledLinkContent $visible={visible}>
      <StyledInviteHeader>
        <StyledTitle>{s18n('收到邀请链接的人可以加入企业')}</StyledTitle>
        <StyledInviteHeaderRight>
          <StyledSwitchText>{s18n('链接开启')}</StyledSwitchText>
          <Switch loading={switchLoading} value={checked} onChange={handleSwitchChange} />
        </StyledInviteHeaderRight>
      </StyledInviteHeader>
      <StyledInputLink className={classNames({ disabled: !checked })}>{linkValue}</StyledInputLink>
      <StyledCopyButtonContainer>
        <Button disabled={!checked} type="primary" onClick={handleCopyButton}>
          {s18n('复制链接')}
        </Button>
      </StyledCopyButtonContainer>
    </StyledLinkContent>
  );
};
