import styled from 'styled-components';

export const StyledResultContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 480px;
  margin: auto;
  align-items: center;
`;

export const StyledTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
`;

export const StyledTitleText = styled.div`
  margin-left: 12px;
`;

export const StyledContent = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 12px;
`;

export const StyledCopyLink = styled.span`
  color: var(--theme-link-button-color);
  cursor: pointer;
  margin-left: 12px;
`;
