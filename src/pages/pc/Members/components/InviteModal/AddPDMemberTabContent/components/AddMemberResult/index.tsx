import s18n from '@shimo/simple-i18n';
import { Button, message } from 'antd';
import React from 'react';

import { ReactComponent as CheckCircleSVG } from '@/assets/images/members/check-circle-20.svg';
import { oldCopy } from '@/utils/copy';

import { AddPDStatus, type AddPdUserItem } from '../AddMemberForm';
import { StyledContent, StyledCopyLink, StyledResultContainer, StyledTitle, StyledTitleText } from './index.style';

interface Props {
  user: AddPdUserItem;
  setAddStatus: (v: AddPDStatus) => void;
}

// 私部环境添加成员的方式
export const AddMemberResult: React.FC<Props> = ({ user, setAddStatus }) => {
  const text = s18n`${user.name}（${user.email}）的初始密码为 ${user.password ?? ''}`;
  return (
    <StyledResultContainer>
      <StyledTitle>
        <CheckCircleSVG />
        <StyledTitleText>{s18n('添加成功')}</StyledTitleText>
      </StyledTitle>
      <StyledContent>
        <span>{text}</span>
        <StyledCopyLink
          onClick={() => {
            oldCopy(text);
            message.success('复制成功');
          }}
        >
          {s18n('复制')}
        </StyledCopyLink>
      </StyledContent>
      <Button
        type="primary"
        onClick={() => {
          setAddStatus(AddPDStatus.form);
        }}
      >
        {s18n('继续添加下一个')}
      </Button>
    </StyledResultContainer>
  );
};
