import { Modal } from 'antd';
import styled from 'styled-components';

import { singleLineEllipsis } from '@/utils/css';

export const StyledModal = styled(Modal)`
  .ant-modal-footer button[type='button'] > span {
    text-overflow: ellipsis;
    overflow: hidden;
  }
`;

export const StyledModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
`;

export const StyledTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
`;

export const StyledSelected = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 22px;
`;

export const StyledContent = styled.div`
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  display: flex;
  height: 372px;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;
  box-sizing: border-box;

  & > span {
    margin-bottom: 10px;
  }
`;

export const StyledList = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  height: 340px;
  overflow-y: auto;

  & > div {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
`;

export const StyledItem = styled.div`
  display: flex;
  padding: 4px 12px;
  align-items: center;
  justify-content: space-between;
  align-self: stretch;
  border-radius: 2px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
`;

export const StyledNameContainer = styled.div`
  display: flex;
  align-items: center;
  width: 155px;
`;

export const StyledAvatar = styled.img`
  width: 28px;
  height: 28px;
  border-radius: 50%;
`;

export const StyledDisableTag = styled.span`
  color: var(--theme-red-tag-color-text);
  border: 1px solid var(--theme-red-tag-color-border);
  background: var(--theme-red-tag-color-bg);
  font-size: 12px;
  line-height: 20px;
  padding: 0 4px;
  flex-shrink: 0;
  margin-left: 8px;
  border-radius: 2px;
`;

export const StyledName = styled.div`
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  max-width: 120px;
  margin-left: 8px;
  ${singleLineEllipsis}
`;

export const StyledEmail = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  width: 260px;
  margin-left: 12px;
  ${singleLineEllipsis}
`;

export const StyledHolderContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export const LoadingIMG = styled.img`
  width: 20px;
  height: 20px;
`;

export const EmptyIMG = styled.img`
  width: 94px;
  height: 70px;
  margin-bottom: 16px;
`;

export const StyledText = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  text-align: center;
`;
