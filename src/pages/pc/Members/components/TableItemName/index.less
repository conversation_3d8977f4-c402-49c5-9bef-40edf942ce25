.styledDepartmentName {
  display: flex;
  flex: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.styledTableItemName {
  display: flex;
  flex: 1;
  align-items: center;
  justify-items: center;
  cursor: pointer;
  color: var(--theme-text-color-guidance);

  & > svg,
  .styledDepartmentName {
    margin-right: 8px;
  }
}

.styledRightArrow {
  transform: rotate(-90deg);
}
