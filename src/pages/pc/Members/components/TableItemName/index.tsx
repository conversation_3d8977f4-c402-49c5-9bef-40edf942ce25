import React from 'react';

import { ReactComponent as DepartmentIconSVG } from '@/assets/images/members/department-icon.svg';
import { ReactComponent as DownIconSVG } from '@/assets/images/members/down-icon.svg';

import styles from './index.less';
interface Props {
  name: string;
  handleClick: () => void;
  allMemberCount: number;
}

export const TableDepartmentItem: React.FC<Props> = ({ name, handleClick, allMemberCount }) => {
  return (
    <span className={styles['styledTableItemName']} onClick={handleClick}>
      <DepartmentIconSVG />
      <div className={styles['styledDepartmentName']}>
        {name}
        {allMemberCount > -1 ? <span> ({allMemberCount})</span> : ''}
      </div>
      <DownIconSVG className={styles['styledRightArrow']} />
    </span>
  );
};
