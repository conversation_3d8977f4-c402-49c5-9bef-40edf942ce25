import { Dropdown } from 'antd';
import type { MenuProps } from 'antd/lib';
import React from 'react';
import styled from 'styled-components';

import { ReactComponent as DownIconSVG } from '@/assets/images/members/down-icon.svg';

interface Props {
  menu: MenuProps;
  text: string;
}

const StyledLinkButton = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--theme-link-button-color);

  :hover {
    color: var(--theme-link-button-color-hover);
  }

  & > span {
    margin-right: 4px;
  }

  & > svg {
    width: 12px;
    height: 12px;
    margin-top: 6px;
  }
`;

export const DropdownLink: React.FC<Props> = ({ menu, text }) => {
  return (
    <Dropdown menu={menu} trigger={['click']}>
      <StyledLinkButton>
        <span>{text}</span>
        <DownIconSVG />
      </StyledLinkButton>
    </Dropdown>
  );
};
