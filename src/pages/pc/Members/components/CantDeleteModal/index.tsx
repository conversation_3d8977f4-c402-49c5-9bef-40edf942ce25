import s18n from '@shimo/simple-i18n';
import React from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
}

const StyledContentText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
`;

export const CantDeleteModal: React.FC<Props> = ({ open, changeOpen }) => {
  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      maskClosable={false}
      modalType="warning"
      open={open}
      title={s18n('不能删除部门')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={() => {
        changeOpen(false);
      }}
    >
      <StyledContentText>{s18n('删除前请先移除部门及其子部门里的成员。')}</StyledContentText>
    </CustomModal>
  );
};
