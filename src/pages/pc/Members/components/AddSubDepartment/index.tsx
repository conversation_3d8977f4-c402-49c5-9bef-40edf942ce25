import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { createSubDepartment } from '@/api/Members';
import { MembersDispatchContext } from '@/contexts/members';
import type { Department } from '@/contexts/members/type';

import { StyledInput, StyledItem, StyledLabel } from './index.styled';

const { Item } = Form;

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  parentDepartment: Department | null;
}

const DepartmentName = 'departmentName';

export const AddSubDepartmentModal: React.FC<Props> = ({ open, changeOpen, parentDepartment }) => {
  const [form] = Form.useForm();
  const { getSubDepartmentList, getDepartmentUserList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  const departmentName = Form.useWatch('departmentName', form);

  if (!parentDepartment) {
    return null;
  }

  const handleOk = async () => {
    const validateData = await form.validateFields();
    try {
      setLoading(true);

      const departmentRes = await createSubDepartment({
        name: validateData[DepartmentName],
        parentID: parentDepartment.id,
      });
      if (departmentRes.id) {
        // 请求成功后 更新 tree data
        await getSubDepartmentList?.(parentDepartment.id);
        message.success(s18n`“${validateData[DepartmentName]}”创建成功`);
        await getDepartmentUserList?.({
          departmentId: parentDepartment!.id,
          page: 1,
        });
        changeOpen(false);
        form.resetFields();
      }
    } catch (error) {
      const errorMsg = (error as any)?.error;
      message.error(errorMsg);
    }
    setLoading(false);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: !departmentName,
        loading,
      }}
      open={open}
      title={s18n('添加子部门')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical" requiredMark={false} validateTrigger={['onBlur']}>
        <Item
          label={s18n('部门名称')}
          name={DepartmentName}
          rules={[
            {
              required: true,
              message: s18n('请先输入部门名称'),
            },
            {
              pattern: /^[\S\s]+$/g,
              message: s18n('部门名称最少一个字或字符'),
            },
            {
              max: 50,
              message: s18n('部门名称最长 50 个字'),
            },
            { pattern: /(^\S)((.)*\S)?(\S*$)/, message: '前后不能有空格' },
          ]}
        >
          <Input />
        </Item>
      </Form>
      <StyledItem $marginBottom={34}>
        <StyledLabel>{s18n('上级部门')}</StyledLabel>
        <StyledInput disabled value={parentDepartment.name} />
      </StyledItem>
    </Modal>
  );
};
