.styledRightHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.styledLeftPart {
  display: flex;
  padding: 10px 24px;
  align-items: center;
  width: calc(100% - 360px);
  height: 52px;
  line-height: 52px;
}

.styledDeparmentText {
  color: var(--theme-text-color-default);
  font-size: 24px;
  font-weight: 500;
  line-height: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.styledLabelContainer {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: 16px;
}

.styledLabelTitle {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  font-weight: 500;
  line-height: 20px;
}

.styledLabelValue {
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
