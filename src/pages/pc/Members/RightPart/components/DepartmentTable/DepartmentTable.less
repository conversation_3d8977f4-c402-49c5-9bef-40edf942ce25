.styledTableContainer {
  padding: 16px 24px;
  height: calc(100% - 148px);

  :global {
    .ant-table-header {
      .ant-table-thead > tr > th {
        height: 50px;
        background: var(--theme-basic-color-bg-default);
        border-bottom: 1px solid var(--gray10);
        border-top: 1px solid var(--gray10);
      }
    }

    .ant-table-tbody {
      background: var(--theme-basic-color-bg-default);
    }

    .ant-table-tbody .ant-table-row {
      .ant-table-selection-column,
      .ant-table-cell {
        line-height: 24px;
      }
    }
  }
}

.styledEmptyContainer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.styledEmptyIMG {
  width: 124px;
  height: 92px;
  margin-bottom: 40px;
}

.styledEmptyText {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 24px;
}

.styledSelectedUserNum {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  margin-left: 16px;
  font-weight: 400;
}
