/* eslint-disable complexity */
import s18n from '@shimo/simple-i18n';
import { Table, Tooltip } from 'antd';
// import {
//   isDingtalk as isDingTalNavigator,
//   isWework as isWeworkNavigator,
// } from '@shimo/knife/browser';
import type { TableProps } from 'antd/lib';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';

import EmptyDataPNG from '@/assets/images/members/empty-data.png';
import { getRoleText, isExpired } from '@/contexts/me/util';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { type Department, type ItemSource, type UserItem } from '@/contexts/members/type';
import { PermissionContext } from '@/contexts/permissions';
import { getManageTeamMembers } from '@/contexts/permissions/helper';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import { DropdownLink } from '../../../components/DropdownLink';
import { TableDepartmentItem } from '../../../components/TableItemName';
import { TableUserItem } from '../../../components/TableUserItem';
import { getAllMenus, MemberMenuKeys, useDepartmentMenus, useUserMenus } from '../../../menus';
import styles from './DepartmentTable.less';

const PageSize = 100;
const PaginationHeight = 64;
type DataType = Department & UserItem;

interface Props {
  changeActiveDepartmentId: (v: number) => void;
  activeDepartment: ItemSource | null;
  changeSelectedUsers: (userList: UserItem[]) => void;
  selectedUsers: UserItem[];
  changeCurrentTableUser: (user: UserItem) => void;
  changeCurrentTableDepartment: (department: Department) => void;
  handleContextMenuClick: (data: { key: string; record: Department | UserItem }) => void;
  initLoading: boolean;
}

export const DepartmentTable: React.FC<Props> = ({
  changeActiveDepartmentId,
  activeDepartment,
  changeSelectedUsers,
  selectedUsers,
  changeCurrentTableUser,
  changeCurrentTableDepartment,
  handleContextMenuClick,
  initLoading,
}) => {
  const OperationMenuText = {
    /** 部门操作 */
    departmentText: s18n('部门操作'),
    /** 创建者 */
    creator: s18n('创建者'),
    /** 管理员 */
    admin: s18n('管理员'),
    /** 成员 */
    member: s18n('成员'),
    /** 待接受邀请 */
    pending: s18n('待接受邀请'),
  };
  const {
    users,
    usersPage,
    total,
    subdepartments,
    isInSearchMode,
    // activeOtherKey,
    searchLoading,
    usersLoading,
    searchLoadingMore,
    searchUserList,
    searchNext,
    treeDataSourceLoading,
  } = useContext(MembersContext);
  const { getDepartmentUserList, loadMoreSearchUsers } = useContext(MembersDispatchContext);

  const me = useMeStore((state) => state.me);

  const { getUserMenus } = useUserMenus();

  const { getDepartmentMenus } = useDepartmentMenus();

  const hasCheckbox = (me && !isExpired(me)) || true;
  const { syncFromThirdparty, syncPanelEnabled, currentPermissions } = useContext(PermissionContext);
  const menageTeamMemberPermission = getManageTeamMembers({
    manageTeamMembers: currentPermissions.includes('manage_team_members') || true,
  });
  const canManageTeamMember = me.teamRole === 'creator' || me.teamRole === 'admin';

  const columns: TableProps<DataType>['columns'] = [
    {
      title: (
        <div>
          {s18n('成员/子部门')}
          {selectedUsers && selectedUsers.length > 0 && (
            <span className={styles['styledSelectedUserNum']}>{s18n.x`已选 ${selectedUsers.length} 位成员`}</span>
          )}
        </div>
      ),
      dataIndex: 'name',
      key: 'id',
      render(_, record) {
        if (record.email !== undefined || record.allMemberCount === undefined) {
          return <TableUserItem avatar={record.avatar} isMe={me?.id === record.id} name={record.name} />;
        }
        const handleDepartmentClick = () => {
          changeActiveDepartmentId(record.id);
        };

        return (
          <TableDepartmentItem
            allMemberCount={record.allMemberCount}
            handleClick={handleDepartmentClick}
            name={record.name}
          />
        );
      },
    },
    {
      title: s18n('邮箱'),
      dataIndex: 'email',
      key: 'email',
      ellipsis: { showTitle: true },
      render(value, record) {
        const isDepartment = record.allMemberCount !== undefined;

        if (isDepartment) {
          return <span>{'-'}</span>;
        }
        return (
          <Tooltip title={value || s18n('未绑定邮箱')}>
            <span>{value || s18n('未绑定邮箱')}</span>
          </Tooltip>
        );
      },
    },
    {
      title: s18n('操作'),
      dataIndex: 'operation',
      key: 'id',
      width: 200,
      render(_, record) {
        const isDepartment = record.allMemberCount !== undefined;
        let text = isDepartment ? s18n('部门操作') : OperationMenuText.member;
        if (record.teamRole === 'admin') {
          text = OperationMenuText.admin;
        } else if (record.teamRole === 'creator') {
          text = OperationMenuText.creator;
        } else if (record.teamRole === 'pending') {
          text = OperationMenuText.pending;
        }
        const allMenus = getAllMenus();
        // 是否是过期用户
        const isOutdated = record.status === -1 && !record.email && !record.mobile && !record.mobileAccount;

        const userMenuKeys = getUserMenus({
          activeTabKey: 'treeData',
          canManageTeamMembers: menageTeamMemberPermission,
          isOutdated,
          me: me!,
          member: record,
          syncFromThirdparty,
          syncPanelEnabled,
        });

        const featureDisabled = false;

        const departmentMenusKeys = getDepartmentMenus({
          isRoot: false,
          canManageDepartment: canManageTeamMember,
          level: (record as any).level,
        });

        let menuKeys = userMenuKeys;
        if (isDepartment) {
          // 使用 部门下拉列表
          menuKeys = departmentMenusKeys;
        }

        const currentMenus = menuKeys.map((key) => {
          if (key === MemberMenuKeys.divider) {
            return {
              type: 'divider',
            };
          }
          // @ts-ignore
          const menuItem = allMenus[key];

          // if (menuItem.key === MemberMenuKeys.fileHandover) {
          //   const hasHistory = record?.handoverMenu?.hasHistory;
          //   const demission = activeOtherKey === OtherListKeys.Disabled || record.teamRole === TeamRole.disabled;
          //   const hasToken = record?.handoverMenu?.token;
          //   const title =
          //     !hasHistory && hasToken
          //       ? s18n`管理${demission ? s18n('离职') : s18n('文件')}交接`
          //       : s18n`${demission ? s18n('离职') : s18n('文件')}交接`;

          //   if (!hasHistory) {
          //     return {
          //       key: MemberMenuKeys.fileHandover,
          //       label: title,
          //     };
          //   }
          //   return {
          //     key: MemberMenuKeys.fileHandover,
          //     label: title,
          //     children: [
          //       hasToken
          //         ? {
          //             label: s18n`管理${demission ? s18n('离职') : s18n('文件')}交接`,
          //             key: MemberMenuKeys.manageFileHandover,
          //           }
          //         : {
          //             label: s18n`发起${demission ? s18n('离职') : s18n('文件')}交接`,
          //             key: MemberMenuKeys.launchFileHandover,
          //           },
          //       {
          //         label: s18n('查看历史交接文件'),
          //         key: MemberMenuKeys.fileHandoverHistory,
          //       },
          //     ],
          //   };
          // }

          if (
            key === MemberMenuKeys.restPassword ||
            key === MemberMenuKeys.unbindEmail ||
            key === MemberMenuKeys.bindEmail
          ) {
            if (record.email && key === MemberMenuKeys.bindEmail) {
              menuItem.label = s18n('换绑邮箱');
            }

            return {
              ...allMenus[key],
              disabled: featureDisabled,
            };
          }

          return menuItem;
        });

        const enableModifyProfile = true;
        // || __RUNTIME_ENV__.PRIVATE_DEPLOY_ACCOUNT_SETTINGS;
        if (me?.id === record.id && enableModifyProfile) {
          // 当前登录账号和列表账号是同一个人时，展示账号设置菜单
          currentMenus.unshift({
            ...allMenus[MemberMenuKeys.accountSettings],
          });
        }

        const handleMenuClick = ({ key }: { key: string }) => {
          if (isDepartment) {
            changeCurrentTableDepartment(record);
          } else {
            changeCurrentTableUser(record);
          }
          handleContextMenuClick({ key, record });
        };

        if (currentMenus.length === 0) {
          const roteText = getRoleText(record.teamRole);
          return roteText;
        }

        return <DropdownLink menu={{ items: currentMenus, onClick: handleMenuClick }} text={text} />;
      },
    },
  ];

  const tableContainerRef = useRef(null);
  const [tableScroll, setTableScroll] = useState(-1);

  useEffect(() => {
    if (tableContainerRef.current) {
      const observer = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === tableContainerRef.current) {
            const scrollHeight = entry.contentRect.height + 18;
            setTableScroll(scrollHeight);
          }
        }
      });

      observer.observe(tableContainerRef.current);
    }
  }, [subdepartments, users]);

  const getLoadingStatus = () => {
    if (isInSearchMode) {
      return searchLoading;
    }
    return usersLoading || treeDataSourceLoading || !activeDepartment;
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight) {
      // 滚动到底部
      if (isInSearchMode && searchNext > 0 && !searchLoadingMore) {
        loadMoreSearchUsers?.();
      }
    }
  };

  const noPagination = total <= 100 || isInSearchMode || !activeDepartment;
  // const isEmptyData = () => {
  //   if (isInSearchMode) {
  //     return searchUserList.length === 0;
  //   }

  //   if (activeDepartment === null) {
  //     return true;
  //   }

  //   if (getLoadingStatus()) {
  //     return true;
  //   }

  //   return total === 0 && !!activeDepartment && activeDepartment.children.length === 0;
  // };

  const dataSource = useMemo(() => {
    return getLoadingStatus()
      ? []
      : isInSearchMode
        ? searchUserList
        : [
            ...activeDepartment!.children
              .map(({ id, key, name, allMemberCount, level, parentId }) => {
                return {
                  name,
                  key,
                  id,
                  allMemberCount,
                  level,
                  parentId,
                };
              })
              // @ts-ignore
              .concat(isInSearchMode ? searchUserList : users),
          ];
  }, [isInSearchMode, searchUserList, users]);

  useEffect(() => {
    changeSelectedUsers([]);
  }, [dataSource]);

  return (
    // $isEmpty={isEmptyData()}
    <div ref={tableContainerRef} className={styles['styledTableContainer']}>
      <Table
        virtual
        columns={columns}
        // @ts-ignore
        dataSource={dataSource}
        loading={getLoadingStatus() && !initLoading}
        pagination={
          noPagination
            ? false
            : {
                size: 'small',
                pageSize: PageSize + activeDepartment!.children.length,
                current: usersPage,
                showSizeChanger: false,
                total: total + Math.ceil(total / PageSize) * activeDepartment!.children.length,
                onChange(page: number) {
                  getDepartmentUserList?.({
                    departmentId: activeDepartment!.id,
                    page,
                  });
                },
              }
        }
        // @ts-ignore
        renderEmpty={() => {
          return (
            <div className={styles['styledEmptyContainer']} style={{ height: tableScroll }}>
              <img className={styles['styledEmptyIMG']} src={concatImageUrl(EmptyDataPNG)} />
              <div className={styles['styledEmptyText']}>{s18n('暂无数据')}</div>
            </div>
          );
        }}
        rowKey="id"
        rowSelection={
          menageTeamMemberPermission && hasCheckbox
            ? {
                selectedRowKeys: selectedUsers.map((item) => item.id),
                // @ts-ignore
                onChange: (_, selectedRows: any) => {
                  changeSelectedUsers([...selectedRows]);
                },
                type: 'checkbox',
                columnWidth: 32,
                getCheckboxProps: (record: any) => {
                  const isDepartment = !record.teamRole;

                  return {
                    disabled: isDepartment,
                    style: {
                      display: isDepartment ? 'none' : 'inline-flex',
                    },
                  };
                },
              }
            : undefined
        }
        scroll={{
          y: noPagination ? tableScroll : tableScroll - PaginationHeight,
        }}
        onScroll={handleScroll}
      />
    </div>
  );
};
