import s18n from '@shimo/simple-i18n';
import { Button, Space, Tooltip } from 'antd';
import React, { useContext, useState } from 'react';

import { ReactComponent as PlusIconSVG } from '@/assets/images/members/plus-icon.svg';
import { MembersContext } from '@/contexts/members';
import type { ItemSource, UserItem } from '@/contexts/members/type';
import { AddSubDepartmentModal } from '@/pages/pc/Members/components/AddSubDepartment';
import type { InviteType } from '@/pages/pc/Members/components/InviteModal';
import { getDepartmentById } from '@/pages/pc/Members/utils';
import { useMeStore } from '@/store/Me';

import styles from './OperationContainer.less';

interface Props {
  removeFromDepartment: () => void;
  changeAddNewMemberOpen: (v: boolean) => void;
  changeInviteOpen: (v: boolean) => void;
  changeDepartmentOpen: (v: boolean) => void;
  selectedUsers: UserItem[];
  changeInviteType: (v: InviteType) => void;
  activeDepartmentId: number;
}

export const OperationContainer: React.FC<Props> = ({
  changeAddNewMemberOpen,
  removeFromDepartment,
  changeInviteOpen,
  changeDepartmentOpen,
  changeInviteType,
  selectedUsers,
  activeDepartmentId,
}) => {
  const { treeDataSource } = useContext(MembersContext);

  const me = useMeStore((state) => state.me);

  /** 创建者与管理员拥有此权限 */
  const isCreateOrManger = me.teamRole === 'creator' || me.teamRole === 'admin';

  const [addSubDepartmentOpen, setAddSubDepartmentOpen] = useState(false);

  const [currentDepartment, setCurrentDepartment] = useState<ItemSource | null>(null);

  return (
    <div className={styles['styledOperationContainer']}>
      <Space>
        {isCreateOrManger && (
          <>
            <Tooltip>
              <Button
                icon={<PlusIconSVG />}
                type="primary"
                onClick={() => {
                  changeAddNewMemberOpen(true);
                }}
              >
                {s18n('添加部门成员')}
              </Button>
            </Tooltip>
            <Tooltip title={selectedUsers.length < 1 ? s18n('请选择要设置的成员') : ''}>
              <Button
                disabled={selectedUsers.length < 1}
                onClick={() => {
                  changeDepartmentOpen(true);
                }}
              >
                {s18n('设置所在部门')}
              </Button>
            </Tooltip>
            <Tooltip title={selectedUsers.length < 1 ? s18n('请选择要移除的成员') : ''}>
              <Button
                disabled={selectedUsers.length < 1}
                onClick={() => {
                  removeFromDepartment();
                }}
              >
                {s18n('从部门移出')}
              </Button>
            </Tooltip>
          </>
        )}
      </Space>

      <Space className={styles['styledRightButtons']}>
        {isCreateOrManger && (
          <>
            <Tooltip>
              <Button
                type="primary"
                onClick={() => {
                  changeInviteOpen(true);
                  changeInviteType('addMemberByOrder');
                }}
              >
                {s18n('添加成员')}
              </Button>
            </Tooltip>
            <Tooltip>
              <Button
                onClick={() => {
                  changeInviteOpen(true);
                  changeInviteType('batchAddMember');
                }}
              >
                {s18n('批量导入成员')}
              </Button>
            </Tooltip>
            <Tooltip>
              <Button
                onClick={() => {
                  const curDepartment = getDepartmentById(treeDataSource, activeDepartmentId);
                  setCurrentDepartment(curDepartment);
                  setAddSubDepartmentOpen(true);
                }}
              >
                {s18n('添加子部门')}
              </Button>
            </Tooltip>
          </>
        )}
      </Space>
      {addSubDepartmentOpen && (
        <AddSubDepartmentModal
          changeOpen={(v: boolean) => {
            setAddSubDepartmentOpen(v);
          }}
          open={addSubDepartmentOpen}
          parentDepartment={currentDepartment}
        />
      )}
    </div>
  );
};
