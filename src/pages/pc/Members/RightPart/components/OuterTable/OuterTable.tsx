import s18n from '@shimo/simple-i18n';
import { Table, Tooltip } from 'antd';
import type { TableProps } from 'antd/lib';
import React, { useContext, useEffect, useRef, useState } from 'react';

import EmptyFilePNG from '@/assets/images/members/empty-data.png';
import { getRoleText } from '@/contexts/me/util';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { type Department, OtherListKeys, TeamRole, type UserItem } from '@/contexts/members/type';
import { PermissionContext } from '@/contexts/permissions';
import { getManageTeamMembers } from '@/contexts/permissions/helper';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import { DropdownLink } from '../../../components/DropdownLink';
import { TableUserItem } from '../../../components/TableUserItem';
import { getAllMenus, MemberMenuKeys, useUserMenus } from '../../../menus';
import styles from './OuterTable.less';

type DataType = Department & UserItem;

interface Props {
  handleContextMenuClick: (data: { key: string; record: UserItem | Department }) => void;
  changeCurrentTableUser: (user: UserItem) => void;
}

const PageSize = 50;
const TableRowHeight = 44;

export const OuterTable: React.FC<Props> = ({ handleContextMenuClick, changeCurrentTableUser }) => {
  const {
    activeOtherKey,
    disableLoading,
    disableList,
    disableTotal,
    onlyOnRootLoading,
    onlyOnRootList,
    onlyOnRootTotal,
    outsiderLoading,
    outsiderList,
    outsiderTotal,
    unactivated,
    unactivatedLoading,
    external,
  } = useContext(MembersContext);
  const { getDisabledMembers, getOnlyOnRootMembers, getOutsiderMembers } = useContext(MembersDispatchContext);
  const me = useMeStore((state) => state.me);

  const { getUserMenus } = useUserMenus();

  const { syncFromThirdparty, syncPanelEnabled, currentPermissions, teamInfo } = useContext(PermissionContext);
  const menageTeamMemberPermission =
    getManageTeamMembers({
      manageTeamMembers: currentPermissions.includes('manage_team_members'),
    }) || true; // 控制【未分配部门成员】【已禁用成员】操作按钮的menu

  const columns: TableProps<DataType>['columns'] = [
    {
      title: s18n('成员/子部门'),
      dataIndex: 'name',
      key: 'id',
      render(_, record) {
        const isMe = me?.id === record.id;

        return <TableUserItem avatar={record.avatar} isMe={isMe} name={record.name} />;
      },
    },
    {
      title: s18n('邮箱'),
      dataIndex: 'email',
      key: 'email',
      render(value, record) {
        const isDepartment = record.allMemberCount !== undefined;

        if (isDepartment) {
          return <span>{'-'}</span>;
        }
        return <span>{value || s18n('未绑定邮箱')}</span>;
      },
    },
    {
      title: s18n('操作'),
      dataIndex: 'operation',
      key: 'id',
      width: 200,
      render(_, record) {
        const text = s18n('操作');
        const allMenus = getAllMenus();
        // 是否是过期用户
        const isOutdated = record.status === -1 && !record.email && !record.mobile && !record.mobileAccount;

        const userMenuKeys = getUserMenus({
          activeTabKey: activeOtherKey!,
          canManageTeamMembers: menageTeamMemberPermission,
          isOutdated,
          me: me!,
          member: record,
          syncFromThirdparty,
          syncPanelEnabled,
        });

        const currentMenus = userMenuKeys.map((key) => {
          if (key === MemberMenuKeys.divider) {
            return {
              type: 'divider',
            };
          }

          // @ts-ignore
          const menuItem = allMenus[key];

          if (menuItem.key === MemberMenuKeys.fileHandover) {
            const hasHistory = record?.handoverMenu?.hasHistory;
            const demission = activeOtherKey === OtherListKeys.Disabled || record.teamRole === TeamRole.disabled;
            const hasToken = record?.handoverMenu?.token;
            const title =
              !hasHistory && hasToken
                ? s18n`管理${demission ? s18n('离职') : s18n('文件')}交接`
                : s18n`${demission ? s18n('离职') : s18n('文件')}交接`;

            if (!hasHistory) {
              return {
                key: MemberMenuKeys.fileHandover,
                label: title,
              };
            }
            return {
              key: MemberMenuKeys.fileHandover,
              label: title,
              children: [
                hasToken
                  ? {
                      label: s18n`管理${demission ? s18n('离职') : s18n('文件')}交接`,
                      key: MemberMenuKeys.manageFileHandover,
                    }
                  : {
                      label: s18n`发起${demission ? s18n('离职') : s18n('文件')}交接`,
                      key: MemberMenuKeys.launchFileHandover,
                    },
                {
                  label: s18n('查看历史交接文件'),
                  key: MemberMenuKeys.fileHandoverHistory,
                },
              ],
            };
          }

          if (key === MemberMenuKeys.inviteJoinEnterprise && record.teamId && record.teamId !== teamInfo?.id) {
            return {
              ...menuItem,
              label: <Tooltip title={s18n('该用户已加入其他企业，无法邀请')}>{s18n('邀请该用户加入企业')}</Tooltip>,
              disabled: true,
            };
          }
          return menuItem;
        });

        const handleMenuClick = ({ key }: { key: string }) => {
          changeCurrentTableUser(record);
          handleContextMenuClick({ key, record });
        };

        if (currentMenus.length === 0) {
          const roteText = getRoleText(record.teamRole || record.team_role);
          return roteText;
        }

        return <DropdownLink menu={{ items: currentMenus, onClick: handleMenuClick }} text={text} />;
      },
    },
  ];

  const getData = () => {
    if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
      return onlyOnRootList;
    } else if (activeOtherKey === OtherListKeys.OuterSider) {
      return outsiderList;
    } else if (activeOtherKey === OtherListKeys.Disabled) {
      return disableList;
    } else if (activeOtherKey === OtherListKeys.Inactivated) {
      return unactivated || [];
    } else if (activeOtherKey === OtherListKeys.DingOuters) {
      return external || [];
    }

    return [];
  };

  const getTotal = () => {
    if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
      return onlyOnRootTotal;
    } else if (activeOtherKey === OtherListKeys.OuterSider) {
      return outsiderTotal;
    } else if (activeOtherKey === OtherListKeys.Disabled) {
      return disableTotal;
    }

    return 0;
  };

  const tableContainerRef = useRef(null);
  const [tableScroll, setTableScroll] = useState(-1);
  const [showScroll, setShowScroll] = useState(false);

  useEffect(() => {
    if (tableContainerRef.current) {
      let dataLength = 0;
      if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
        dataLength = onlyOnRootList.length;
      } else if (activeOtherKey === OtherListKeys.OuterSider) {
        dataLength = outsiderList.length;
      } else if (activeOtherKey === OtherListKeys.Disabled) {
        dataLength = disableList.length;
      }

      const tbodyHeight = dataLength * TableRowHeight;
      const observer = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === tableContainerRef.current) {
            const scrollHeight = entry.contentRect.height + 18;
            if (tbodyHeight > scrollHeight) {
              setShowScroll(true);
            } else {
              setShowScroll(false);
            }
            setTableScroll(scrollHeight);
          }
        }
      });

      observer.observe(tableContainerRef.current);
    }
  }, [onlyOnRootList, outsiderList, disableList, activeOtherKey]);

  const handlePageChange = (page: number) => {
    if (!me) {
      return;
    }
    if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
      getOnlyOnRootMembers?.({ teamId: me.teamId, page }).catch();
    } else if (activeOtherKey === OtherListKeys.OuterSider) {
      getOutsiderMembers?.({ page }).catch();
    } else if (activeOtherKey === OtherListKeys.Disabled) {
      getDisabledMembers?.({ teamId: me.teamId, page }).catch();
    }
  };

  const getLoading = () => {
    if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
      return onlyOnRootLoading;
    } else if (activeOtherKey === OtherListKeys.OuterSider) {
      return outsiderLoading;
    } else if (activeOtherKey === OtherListKeys.Disabled) {
      return disableLoading;
    } else if (activeOtherKey === OtherListKeys.Inactivated) {
      return unactivatedLoading;
    }
    return false;
  };

  return (
    <div ref={tableContainerRef} className={styles['styledTableContainer']}>
      <Table
        columns={columns}
        // @ts-ignore
        dataSource={getData()}
        loading={getLoading()}
        pagination={
          getTotal() > PageSize
            ? {
                total: getTotal(),
                pageSize: PageSize,
                showSizeChanger: false,
                size: 'small',
                onChange: (page) => {
                  handlePageChange(page);
                },
              }
            : false
        }
        // @ts-ignore
        renderEmpty={() => {
          return (
            <div className={styles['styledEmptyContainer']} style={{ height: tableScroll }}>
              <img className={styles['styledEmptyIMG']} src={concatImageUrl(EmptyFilePNG)} />
              <div className={styles['styledEmptyText']}>{s18n('暂无数据')}</div>
            </div>
          );
        }}
        rowKey="id"
        scroll={showScroll ? { y: tableScroll } : undefined}
      />
    </div>
  );
};
