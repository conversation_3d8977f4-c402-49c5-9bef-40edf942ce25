import React, { lazy, useContext } from 'react';

import { PermissionContext } from '@/contexts/permissions';
import { AlphaFeatureKeys } from '@/contexts/permissions/type';

export const Members = lazy(async () => {
  const { MembersWithContext } = await import(
    /* webpackChunkName: "members" */
    './Members'
  );
  return { default: MembersWithContext };
});

export const SwitchMembers: React.FC = () => {
  const { alphaFeatures } = useContext(PermissionContext);
  if (alphaFeatures[AlphaFeatureKeys.organization_contacts]) {
    return <Members />;
  }

  return <Members />;
};
export default SwitchMembers;
