import { type MenuProps, message, Tooltip } from 'antd';
import type { Key } from 'react';

import { deleteBulkFile } from '@/api/File';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { FilePathPicker } from '@/components/Modal/FilePathPicker';
import { createDebouncedEditHandler } from '@/hooks/useFileEdit';
import type { DownLoadFileProps } from '@/hooks/useFileTypeDownload';
import { canEditAndViews, fileStar, openFile } from '@/utils/file';

import type { DataType } from './index';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

const canMoveFile = ({
  data,
  selectedRowKeys,
  record,
}: {
  data: DataType[];
  selectedRowKeys: Key[];
  record: DataType;
}): boolean => {
  if (!record.permissionsAndReasons) return false;
  if (selectedRowKeys.length) {
    const unCanMoveFilter = data.filter(
      (item) => selectedRowKeys.includes(item.guid) && !item.permissionsAndReasons.canRemove.value,
    );
    return !(unCanMoveFilter.length > 0);
  } else {
    return record.permissionsAndReasons?.canRemove.value;
  }
};

const unCanCopyTo = ({
  data,
  selectedRowKeys,
  record,
}: {
  data: DataType[];
  selectedRowKeys: Key[];
  record: DataType;
}): boolean => {
  if (selectedRowKeys.length) {
    const unCanCopyFilter = data.filter((item) => {
      if (item.isShortcut) {
        return selectedRowKeys.includes(item.guid) && item.shortcutSource.type === 'folder';
      } else {
        return selectedRowKeys.includes(item.guid) && item.type === 'folder';
      }
    });

    return unCanCopyFilter.length > 0;
  } else {
    if (record.isShortcut) {
      return record.shortcutSource.type === 'folder';
    } else {
      return record.type === 'folder';
    }
  }
};

export const items = ({
  record,
  i18nText,
  setShareVisible,
  selectedRowKeys,
  renameCallback,
  reload,
  meId,
  hidden = false,
  setTypeSetting,
  downloadDiffFile,
  data,
}: {
  record: any;
  setShareVisible: (visible: boolean) => void;
  selectedRowKeys: Key[];
  renameCallback: ({ visible, params }: { visible: boolean; params: { guid: string; fileName: string } }) => void;
  reload: () => void;
  i18nText: { [key: string]: string };
  meId: number | null;
  hidden?: boolean; // 文件夹目录隐藏 创建副本到...
  setTypeSetting?: (value: string) => void;
  downloadDiffFile: ({ type, guid, fileName }: DownLoadFileProps) => void;
  data: DataType[];
}): MenuItem[] => {
  return [
    // {
    //   label: '当前标签页打开',
    //   key: 'openTag',
    // },
    {
      label: i18nText.newTabOpens,
      key: 'newTabOpens',
      hidden: selectedRowKeys.length > 1,
      onClick: () => {
        const newArg = record;
        if (record.isShortcut) {
          const { url, type } = record.shortcutSource;
          newArg.type = type;
          newArg.url = url;
        }
        openFile({ type: newArg.type, guid: newArg.guid, url: newArg.url, model: 'new' });
      },
    },
    {
      label: i18nText.edit,
      key: 'edit',
      hidden:
        record.type === 'folder' ||
        selectedRowKeys.length > 1 ||
        !canEditAndViews({ type: record.isShortcut ? record.shortcutSource.type : record.type }),
      onClick: () => {
        const deboundedEditHandler = createDebouncedEditHandler({
          fileDetail: record,
          fileGuid: record.guid,
          i18n: {
            fileImportTypeErr: i18nText.fileImportTypeErr,
            fileImportErr: i18nText.fileImportErr,
            fileIsLoading: i18nText.fileIsLoading,
          },
        });
        deboundedEditHandler();
      },
    },
    // {
    //   label: '定位到所在文件夹',
    //   key: 'position',
    // },
    // {
    //   label: '添加到快速访问',
    //   key: 'quickAccess',
    // },
    {
      label: record.starred ? i18nText.removeStr : i18nText.star,
      key: 'star',
      hidden: selectedRowKeys.length > 1,
      onClick: () =>
        fileStar({ guid: record.guid, status: record.starred })
          .then(({ type }) => {
            if (type === 'star') {
              message.success(i18nText.starSuccess);
            } else {
              message.success(i18nText.removeStarSuccess);
            }
            reload();
          })
          .catch(({ type }) => {
            if (type === 'star') {
              message.error(i18nText.starError);
            } else {
              message.error(i18nText.removeStarError);
            }
          }),
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.share + i18nText.collaboration,
      hidden: selectedRowKeys.length > 1,
      key: 'collaborationShare',
      onClick: () => {
        setShareVisible(true);
        if (setTypeSetting) {
          setTypeSetting('');
        }
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.download,
      key: 'downloadOther',
      hidden: !record.name?.includes('.') || record.type === 'folder',
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.download,
      key: 'download',
      hidden:
        selectedRowKeys.length > 1 || ['board', 'form', 'table', 'folder', 'shortcut', 'zip'].includes(record.type),
      children: [
        {
          label: i18nText.png,
          key: 'jpg',
          hidden: !['mindmap', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'Xmind',
          key: 'xmind',
          hidden: !['mindmap'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'PPTX',
          key: 'pptx',
          hidden: !['presentation'].includes(record.type) || ['img'].includes(record.type), // presentation
        },
        {
          label: 'PDF',
          key: 'pdf',
          hidden: !['presentation', 'newdoc', 'modoc'].includes(record.type) || ['img'].includes(record.type), //presentation
        },
        {
          label: 'Excel',
          key: 'xlsx',
          hidden: !['mosheet'].includes(record.type) || ['img'].includes(record.type), // mosheet
        },
        {
          label: 'WPS',
          key: 'wps',
          hidden: !['modoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'Word',
          key: 'docx',
          hidden: !['modoc', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'MarkDown',
          key: 'md',
          hidden: !['newdoc'].includes(record.type) || ['img'].includes(record.type), // newdoc
        },
      ],
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.reName,
      key: 'reName',
      hidden: selectedRowKeys.length > 1,
      onClick: () => renameCallback({ visible: true, params: { fileName: record.name, guid: record.guid } }),
    },
    {
      label: (
        <Tooltip title={!canMoveFile({ data, selectedRowKeys, record }) ? i18nText.moveTips : null}>
          {i18nText.moveTo}
        </Tooltip>
      ),
      key: 'moveTo',
      disabled: !canMoveFile({ data, selectedRowKeys, record }),
      onClick: () => {
        let source: any = [];
        if (selectedRowKeys.length > 0) {
          const selectedRows: DataType[] = data.filter((item) => selectedRowKeys.includes(item.guid));
          source = selectedRows.map((item: DataType) => {
            return {
              name: item.name,
              fileGuid: item.guid,
              parentGuid: item.parent_guid,
              isAdmin: item.isAdmin || item.isFileAdmin,
              role: item.role,
            };
          });
        } else {
          source = [
            {
              name: record.name,
              fileGuid: record.guid,
              parentGuid: record.parent_guid,
              filename: record.name,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ];
        }

        FilePathPicker({
          type: 'move',
          locationGuid: 'desktop',
          source,
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      label: '创建快捷方式到...',
      key: 'shortcutTo',
      hidden: selectedRowKeys.length > 1,
      onClick: () => {
        FilePathPicker({
          type: 'shortcut',
          locationGuid: 'desktop',
          source: [
            {
              name: record.name,
              fileGuid: record.isShortcut ? record.shortcutSource.guid : record.guid,
              parentGuid: record.parent_guid,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ],
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      label: i18nText.copyTo,
      key: 'copyTo',
      hidden: unCanCopyTo({ data, selectedRowKeys, record }) || hidden,
      onClick: () => {
        let source: any = [];
        if (selectedRowKeys.length > 0) {
          const selectedRows: DataType[] = data.filter((item) => selectedRowKeys.includes(item.guid));
          source = selectedRows.map((item: DataType) => {
            return {
              name: item.name,
              fileGuid: item.guid,
              parentGuid: item.parent_guid,
              isAdmin: item.isAdmin || item.isFileAdmin,
              role: item.role,
            };
          });
        } else {
          source = [
            {
              name: record.name,
              fileGuid: record.guid,
              parentGuid: record.parent_guid,
              filename: record.name,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ];
        }

        FilePathPicker({
          type: 'create',
          locationGuid: 'desktop',
          source,
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.delete,
      key: 'delete',
      danger: true,
      disabled: meId !== record.userId, // 非创建人无法删除
      onClick: () => {
        if (selectedRowKeys.length > 50) {
          message.error(i18nText.deleteTips);
          return;
        }
        deleteConfirm({
          i18nText,
          data: selectedRowKeys.length ? { fileGuids: selectedRowKeys } : { fileGuids: [record['guid']] },
          api: deleteBulkFile,
          callback: reload,
        });
      },
    },
  ];
};
