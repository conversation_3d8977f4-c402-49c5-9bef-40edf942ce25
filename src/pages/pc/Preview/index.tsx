import { message } from 'antd';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { history, useParams } from 'umi';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { isSupportedImportExt } from '@/constants/fileTypes';
import { useFileDetail } from '@/hooks/useFileDetail';
import { useFileEdit } from '@/hooks/useFileEdit';
import useFileUpload from '@/hooks/useFileUpload';
import type { FileAncestors, FileDetail } from '@/types/api';
import { setBrowserTabTitle } from '@/utils/browser';
import { downloadFile } from '@/utils/file';
import { getSMEditor } from '@/utils/ShimoSDK';

import { BackButton, BackButtonMenu, CreateButton, FileMenu, type FileMenuRef, TogglePreviewImg } from './components';
import { useHeaderI18n } from './hooks/useHeaderI18n';
import { useImagePreviewNavigator } from './hooks/useImagePreviewNavigator';
import styles from './index.less';
import type { MenuItem } from './index.type';

export default () => {
  const { guid: paramsGuid } = useParams();
  const [messageApi] = message.useMessage();
  const fileMenuRef = useRef<FileMenuRef>(null);
  // 存储文件详情数据
  const [fileDetail, setFileDetail] = useState<FileDetail | null>(null);
  // 最近的祖先
  const [lastAncestor] = useState<FileAncestors | undefined>(undefined);
  // 是否显示新建文件夹弹窗
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isModalVisible, setIsModalVisible] = useState(false);
  // 正在收藏中的状态
  const [isStaring, setIsStaring] = useState(false);
  // 收藏按钮
  const [isStar, setIsStar] = useState(false);
  const { openFileDetail } = useFileDetail();
  // 是否显示保存版本弹窗
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [isSaveModalVisible, setIsSaveModalVisible] = useState(false);
  // 使用文件上传 hook
  const { triggerUpload } = useFileUpload({
    parentGuid: lastAncestor?.guid,
  });
  // 最近使用文件列表
  const [recentFiles, setRecentFiles] = useState<FileDetail[]>([]);
  // i18n
  const i18n = useHeaderI18n();
  const { i18nText, messageI18n } = i18n;

  const { showEmptyPage, disableNextBtn, handleClickPre, handleClickNext } = useImagePreviewNavigator(fileDetail);
  const { debouncedHandleEdit, initGetFile } = useFileEdit(fileDetail, paramsGuid);

  const initRecentFiles = async () => {
    // 获取最近使用的文件
    const getRecentFiles = async () => {
      const [, res] = await to(fileApi.files({ type: 'used', limit: 20 }));
      if (res?.status === 200) {
        const fileData = res.data.list as FileDetail[];
        setRecentFiles(fileData.slice(0, 5));
      }
    };

    getRecentFiles();
  };
  const init = useCallback(async () => {
    const res = await initGetFile(paramsGuid || '');
    if (!res) return;
    setFileDetail(res);
    setIsStar(res.starred);

    initRecentFiles();
    setBrowserTabTitle(res.name);
  }, [initGetFile, paramsGuid]);

  const debouncedHandleDownload = useMemo(() => {
    return debounce(
      async () => {
        try {
          if (!fileDetail) return;
          const downloadUrl = `/api/v1/files/${fileDetail.guid}/download`;

          // 预检查
          const res = await fetch(downloadUrl, { method: 'HEAD' });
          if (!res.ok) {
            throw new Error(messageI18n.fileDownloadErr);
          }

          downloadFile(downloadUrl, fileDetail.name);
          message.open({
            type: 'success',
            content: messageI18n.fileStartDownload,
          });
        } catch (error) {
          message.open({
            type: 'error',
            content: messageI18n.fileDownloadErr,
          });
        }
      },
      1000,
      { leading: true, trailing: false },
    );
  }, [fileDetail, messageI18n.fileDownloadErr, messageI18n.fileStartDownload]);

  useEffect(() => {
    init();

    return () => {
      debouncedHandleDownload.cancel();
    };
  }, [debouncedHandleDownload, init]);

  const isShowEditBtn = useMemo(() => {
    return isSupportedImportExt(fileDetail?.subType || '');
  }, [fileDetail?.subType]);

  const isShowTogglePreviewImg = useMemo(() => fileDetail?.type === 'img', [fileDetail?.type]);

  function showDownTip() {
    messageApi.open({
      type: 'loading',
      content: i18n.loadingText,
      duration: 0,
      style: {
        marginTop: '50px',
      },
    });
  }

  // 轮训获取导出进度
  async function getExportProgress(taskId: string) {
    const [, res] = await to(fileApi.exportProgress({ taskId }));
    if (res?.status !== 200) return messageApi.destroy();

    const { progress, downloadUrl } = res.data.data;
    if (progress < 100) {
      setTimeout(() => {
        getExportProgress(taskId);
      }, 1500);
    } else if (progress === 100) {
      downloadFile(downloadUrl, `${fileDetail?.name}`);
      messageApi.destroy();
    }
  }

  // 导出不同类型文件
  async function exportFile(exportType: string) {
    showDownTip();
    const [, res] = await to(fileApi.exportFile(paramsGuid || '', { type: exportType }));
    if (res?.status !== 200) return messageApi.destroy();

    const { taskId } = res.data.data;
    getExportProgress(taskId);
  }

  function back() {
    // 对上级目录没有权限时或者上级目录是桌面时，返回桌面
    if (!lastAncestor || lastAncestor.guid === 'Desktop') {
      history.push('/desktop');
      return;
    }
    // 进入对应的文件夹
    history.push(`/folder/${lastAncestor.guid}`);
  }

  function createFile(fileType: string) {
    window.open(
      `/api/v1/files/create/${fileType}?parentGuid=${lastAncestor?.guid}&name=${i18n.inputPlaceholder}`,
      '_self',
    );
  }

  function openFile_(fileInfo: { fileType?: string; fileGuid?: string }) {
    let type = fileInfo.fileType || '';
    if (['newdoc', 'modoc', 'doc'].includes(type)) {
      type = 'docs';
    } else if (['modoc', 'docx'].includes(type)) {
      type = 'docx';
    } else if (['mosheet', 'xls', 'xlsx', 'excel', 'csv'].includes(type)) {
      type = 'sheets';
    } else if (['form'].includes(type)) {
      type = 'forms';
    } else if (['table'].includes(type)) {
      type = 'tables';
    } else if (['presentation', 'ppt', 'pptx'].includes(type)) {
      type = 'presentation';
    } else if (['img', 'mp3', 'mp4', 'mov', 'qt', 'wps', 'zip', 'rar'].includes(type)) {
      type = 'files';
    }
    if (fileInfo.fileGuid) {
      history.push(`/${type}/${fileInfo.fileGuid}`);
    }
  }

  // 切换收藏
  async function toggleStar() {
    if (!fileDetail) return;
    if (isStaring) return;
    setIsStaring(true);

    if (isStar) {
      // 取消收藏
      const [, res] = await to(fileApi.cancelStar(paramsGuid || ''));
      if (res?.status === 204) {
        setIsStar(false);
        message.info(`「${fileDetail?.name}」${i18n.unfavorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: false,
        });
      }
    } else {
      // 收藏
      const [, res] = await to(fileApi.star(paramsGuid || ''));
      if (res?.status === 204) {
        setIsStar(true);
        message.success(`「${fileDetail?.name}」${i18n.favorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: true,
        });
      }
    }
    // 处理完成，重置状态
    setIsStaring(false);
  }

  // 导出表格文件
  async function exportTableFile() {
    showDownTip();
    const [, res] = await to(fileApi.exportTable(paramsGuid || ''));
    if (res?.status === 200) {
      downloadFile(res.data.downloadUrl, `${fileDetail?.name}`);
    }
    messageApi.destroy();
  }

  // 删除文件
  function deleteFile() {
    deleteConfirm({
      i18nText,
      data: paramsGuid,
      api: fileApi.deleteFile,
      callback: () => {
        history.push('/desktop');
      },
    });
  }

  // 处理导航项点击
  function handleMenuItemClick({ key, exportType, fileType, fileGuid, noSupport }: MenuItem) {
    fileMenuRef.current?.setVisible(false);

    if (noSupport) {
      message.warning(i18nText.noSupport);
      return;
    }
    if (key.includes('down') && exportType) {
      exportFile(exportType);
      return;
    }
    // 处理创建文件类型
    const createFileTypes = [
      'newdoc',
      'modoc',
      'mosheet',
      'presentation',
      'table',
      'form',
      'normalForm',
      'tableViewForm',
      'quizForm',
      'template',
    ];
    if (createFileTypes.includes(key)) {
      if (['normalForm', 'tableViewForm', 'quizForm'].includes(key)) {
        return createFile('form');
      }
      return createFile(key);
    }
    if (key === 'upload') {
      return triggerUpload();
    }
    if (key === 'uploadFolder') return;

    // 处理导航
    switch (key) {
      case 'back':
        back();
        break;
      case 'folder':
        setIsModalVisible(true);
        break;
      case 'myDesktop':
        history.push('/desktop');
        break;
      case 'workbench':
        history.push('/recent');
        break;
      case 'recent':
        openFile_({ fileType, fileGuid });
        break;
      case 'favorite':
        toggleStar();
        break;
      case 'downToExcel':
        exportTableFile();
        break;
      case 'delete':
        deleteFile();
        break;
      case 'viewCommentList':
      case 'viewComment':
        getSMEditor()?.showComments();
        break;
      case 'addComment':
        getSMEditor()?.addComment();
        break;
      case 'fileInfo':
        if (!fileDetail) return;
        openFileDetail({ guid: paramsGuid, title: i18n.fileInfo, type: fileDetail.type });
        break;
      case 'print':
        if (fileDetail?.type === 'modoc') {
          getSMEditor()?.printAll();
          return;
        }
        getSMEditor()?.print();
        break;
      case 'saveVersion':
        if (['modoc', 'presentation'].includes(fileDetail?.type || '')) {
          //弹框输入名字
          setIsSaveModalVisible(true);
          return;
        }
        getSMEditor()?.createRevision();
        break;
      case 'viewHistory':
        getSMEditor()?.showHistory();
        break;
      case 'startDemo':
        getSMEditor()?.startDemonstration();
        break;
    }

    fileMenuRef.current?.setVisible(false);
  }

  const handleSharingCollaboration = () => {};

  return (
    <div className={styles.page}>
      <div className={styles.header}>
        <BackButton title={i18n.backButtonTipText} onClick={() => handleMenuItemClick({ key: 'back' })} />
        <BackButtonMenu recentFiles={recentFiles} title={i18n.backToButtonTipText} onClick={handleMenuItemClick} />
        <CreateButton title={i18n.createButtonTipText} onItemClick={handleMenuItemClick} onOpenInNewTab={() => {}} />
        <div className={styles.fileTitle}>{fileDetail?.name}</div>

        {/* 编辑按钮 */}
        {isShowEditBtn && (
          <div
            className={classNames(styles.btn, styles.textBtn, styles.btnLayout)}
            onClick={() => debouncedHandleEdit()}
          >
            {i18n.editButtonText}
          </div>
        )}
        {/* 下载按钮 */}
        <div
          className={classNames(styles.btn, styles.textBtn, {
            [styles.btnLayout]: !isShowEditBtn,
          })}
          onClick={() => debouncedHandleDownload()}
        >
          {i18n.downloadButtonText}
        </div>
        {/* 分享协作 */}
        <div className={classNames(styles.btn, styles.textBtn)} onClick={() => handleSharingCollaboration()}>
          {i18n.sharingCollaborationButtonText}
        </div>

        <FileMenu
          ref={fileMenuRef}
          fileType={fileDetail?.type || ''}
          isStar={isStar}
          title={i18n.fileMenuButtonTipText}
          onItemClick={handleMenuItemClick}
        />
      </div>

      {isShowTogglePreviewImg && (
        <TogglePreviewImg
          disableNextBtn={disableNextBtn}
          showEmptyPage={showEmptyPage}
          onClickNext={handleClickNext}
          onClickPre={handleClickPre}
        />
      )}

      <iframe className={styles.iframe} id="preview-iframe" src={`/api/v1/files/${paramsGuid}/preview`} />
    </div>
  );
};
