export enum Size {
  Bytes = 'Bytes',
  KB = 'KB',
  MB = 'MB',
  GB = 'GB',
  TB = 'TB',
}

const CONVERSION_BASIS = 1024;

type SizeMapping = { [key in keyof typeof Size]: number };

const SIZE_MAPPING: SizeMapping = {
  [Size.Bytes]: 0,
  [Size.KB]: 1,
  [Size.MB]: 2,
  [Size.GB]: 3,
  [Size.TB]: 4,
};

export type AllowedSize = Size.KB | Size.GB | Size.TB;

export interface FormatBytes {
  number: number;
  size: AllowedSize;
  text: string;
}

export function isAllowedSize(size?: string): size is AllowedSize {
  return [Size.KB, Size.GB, Size.TB].includes(size as Size);
}

function getSizeByNumber(number: number) {
  return Object.entries(SIZE_MAPPING).find(([, index]) => {
    return index === number;
  })?.[0] as Size | undefined;
}

// 石墨中的文件大小只支持 KB GB TB
function getSizeNumber(bytes: number): { size: AllowedSize; number: number } {
  const sizeNumber = Math.min(Math.floor(Math.log(Math.abs(bytes)) / Math.log(CONVERSION_BASIS)), 4);
  if (sizeNumber === SIZE_MAPPING[Size.Bytes]) {
    return { size: Size.KB, number: SIZE_MAPPING[Size.KB] };
  }
  if (sizeNumber === SIZE_MAPPING[Size.MB]) {
    return { size: Size.GB, number: SIZE_MAPPING[Size.GB] };
  }
  const size = getSizeByNumber(sizeNumber);
  if (size && isAllowedSize(size)) {
    return { size, number: sizeNumber };
  }
  return { size: Size.KB, number: SIZE_MAPPING[Size.KB] };
}

export function formatBytes(bytes: number, size?: AllowedSize, decimals = 3): FormatBytes {
  if (bytes === 0) {
    return {
      number: 0,
      size: Size.GB,
      text: `0 ${Size.GB}`,
    };
  }

  const dm = decimals < 0 ? 0 : decimals;
  const { size: currentSize, number: sizeNumber } = size ? { size, number: SIZE_MAPPING[size] } : getSizeNumber(bytes);
  // ** 等价于 Math.pow()，不同之处在于，它还接受 BigInt 作为操作数
  const targetNumber = parseFloat((bytes / CONVERSION_BASIS ** sizeNumber).toFixed(dm));

  return {
    number: targetNumber,
    size: currentSize,
    text: `${targetNumber} ${currentSize}`,
  };
}

export function formatToBytes(number: number, size: Size, decimals = 0) {
  const dm = decimals < 0 ? 0 : decimals;
  return parseFloat((number * CONVERSION_BASIS ** SIZE_MAPPING[size]).toFixed(dm));
}
