import { ReactComponent as AeIcon } from '@/pages/pc/Enterprise/assets/fileIcon/ae.svg';
import { ReactComponent as AiIcon } from '@/pages/pc/Enterprise/assets/fileIcon/ai.svg';
import { ReactComponent as BoardIcon } from '@/pages/pc/Enterprise/assets/fileIcon/board.svg';
import { ReactComponent as CloudIcon } from '@/pages/pc/Enterprise/assets/fileIcon/cloud.svg';
import { ReactComponent as DocIcon } from '@/pages/pc/Enterprise/assets/fileIcon/doc.svg';
import { ReactComponent as FolderIcon } from '@/pages/pc/Enterprise/assets/fileIcon/folder.svg';
import { ReactComponent as FormIcon } from '@/pages/pc/Enterprise/assets/fileIcon/form.svg';
import { ReactComponent as MindIcon } from '@/pages/pc/Enterprise/assets/fileIcon/mind.svg';
import { ReactComponent as MindmapIcon } from '@/pages/pc/Enterprise/assets/fileIcon/mindmap.svg';
import { ReactComponent as ModocIcon } from '@/pages/pc/Enterprise/assets/fileIcon/modoc.svg';
import { ReactComponent as PdfIcon } from '@/pages/pc/Enterprise/assets/fileIcon/pdf.svg';
import { ReactComponent as PicIcon } from '@/pages/pc/Enterprise/assets/fileIcon/pic.svg';
import { ReactComponent as PresentationIcon } from '@/pages/pc/Enterprise/assets/fileIcon/presentation.svg';
import { ReactComponent as PsIcon } from '@/pages/pc/Enterprise/assets/fileIcon/ps.svg';
import { ReactComponent as SheetIcon } from '@/pages/pc/Enterprise/assets/fileIcon/sheet.svg';
import { ReactComponent as SketchIcon } from '@/pages/pc/Enterprise/assets/fileIcon/sketch.svg';
import { ReactComponent as SpaceIcon } from '@/pages/pc/Enterprise/assets/fileIcon/space.svg';
import { ReactComponent as TableIcon } from '@/pages/pc/Enterprise/assets/fileIcon/table.svg';
import { ReactComponent as VideoIcon } from '@/pages/pc/Enterprise/assets/fileIcon/video.svg';
import { ReactComponent as WordIcon } from '@/pages/pc/Enterprise/assets/fileIcon/word.svg';
import { ReactComponent as WpsIcon } from '@/pages/pc/Enterprise/assets/fileIcon/wps.svg';
import { ReactComponent as XlsIcon } from '@/pages/pc/Enterprise/assets/fileIcon/xls.svg';
import { ReactComponent as ZipIcon } from '@/pages/pc/Enterprise/assets/fileIcon/zip.svg';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';

export function useGetFileTypeIcon(): (type: `${FileIconType}`) => any {
  return (type: `${FileIconType}`) => {
    switch (type) {
      // 石墨文件
      case FileIconType.Folder:
        return <FolderIcon />;
      case FileIconType.Document:
        return <ModocIcon />;
      case FileIconType.Spreadsheet:
        return <SheetIcon />;
      case FileIconType.Doc:
        return <DocIcon />;
      case FileIconType.Sheet:
        return <SheetIcon />;
      case FileIconType.Mosheet:
        return <SheetIcon />;
      case FileIconType.Modoc:
        return <ModocIcon />;
      case FileIconType.Mindmap:
        return <MindmapIcon />;
      case FileIconType.Form:
        return <FormIcon />;
      case FileIconType.TableViewForm:
        return <FormIcon />;
      case FileIconType.QuizForm:
        return <FormIcon />;
      case FileIconType.Board:
        return <BoardIcon />;
      case FileIconType.Presentation:
        return <PresentationIcon />;
      case FileIconType.Table:
        return <TableIcon />;

      // 云文件
      case FileIconType.Xmind:
        return <MindIcon />;
      case FileIconType.Wps:
        return <WpsIcon />;
      case FileIconType.Video:
        return <VideoIcon />;
      case FileIconType.Zip:
        return <ZipIcon />;
      case FileIconType.Ppt:
        return <PresentationIcon />;
      case FileIconType.Word:
        return <WordIcon />;
      case FileIconType.Excel:
        return <XlsIcon />;
      case FileIconType.Pdf:
        return <PdfIcon />;
      case FileIconType.Ae:
        return <AeIcon />;
      case FileIconType.Image:
        return <PicIcon />;
      case FileIconType.Ai:
        return <AiIcon />;
      case FileIconType.Psd:
        return <PsIcon />;
      case FileIconType.Sketch:
        return <SketchIcon />;

      // 其他格式
      case FileIconType.Cloud:
        return <CloudIcon />;
      case FileIconType.Space:
        return <SpaceIcon />;

      case FileIconType.Unknown:
      default:
        return <CloudIcon />;
    }
  };
}

export function useFileTypeIcon(type: `${FileIconType}`): any {
  return useGetFileTypeIcon()(type);
}
