import * as images from '../assets/fileIcon';

export type ImageAsset = string;

// 所有图片资源的 key
type ImageKey = keyof typeof images;

// 所有图片资源名称，不带 2x 或 3x 后缀的 key
type ImageName = Exclude<ImageKey, `${string}2x` | `${string}3x`>;

/**
 * 判断传入 string 是否是图片资源的 key
 * @param key
 * @returns
 */
function isImageKey(key: string): key is ImageKey {
  return key in images;
}

/**
 * 通过图片名称和缩放值获取的图片资源
 * @param name
 * @param scale
 * @returns
 */
function getAssetByName(name: ImageName, scale?: 2 | 3): ImageAsset {
  if (scale !== 2 && scale !== 3) {
    return images[name];
  }

  const key = `${name}${scale}x`;
  if (isImageKey(key)) {
    return images[key as ImageKey];
  } else {
    return images[name];
  }
}

/**
 * 获取基础的图片资源
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function getImage(name: ImageName): ImageAsset {
  return getAssetByName(name, 2);
}

/**
 * 获取图片 srcset
 * @returns
 */
export function getImageSrcset(name: ImageName): string {
  return `${getAssetByName(name)} 1x, ${getAssetByName(name, 2)} 2x, ${getAssetByName(name, 3)} 3x`;
}

/**
 * 获取背景图 image-set
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function getBgImageSet(name: ImageName): string {
  return `-webkit-image-set(url(${getAssetByName(name)}) 1x, url(${getAssetByName(name, 2)}) 2x, url(${getAssetByName(
    name,
    3,
  )}) 3x)`;
}

/**
 * 获取默认背景图 url
 * @param name 资源名称，不带 2x 或 3x 后缀
 * @returns
 */
export function getDefaultBgImage(name: ImageName): string {
  return `url(${getAssetByName(name, 2)})`;
}
