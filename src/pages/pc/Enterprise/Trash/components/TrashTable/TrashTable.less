.styledTrashTable {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  padding: 0 16px;
  box-sizing: border-box;
}

.styledHeader {
  padding: 16px 12px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.styledTitle {
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.styledButtonGroup {
  display: flex;
  align-items: center;

  .restoreBtn {
    padding: 0 29px;
  }

  > button {
    margin-left: 4px;
  }

  :global {
    .ant-btn-dangerous {
      border-color: var(--theme-separator-color-lighter);
    }
  }
}

.styledTableContainer {
  position: relative;
  flex: 1;
}

.styledTable {
  position: absolute;
  width: 100%;
  height: 100%;

  :global {
    .ant-empty {
      height: calc(100vh - 338px);
    }

    .ant-table-header {
      border-bottom: 1px solid var(--theme-separator-color-lighter);
    }

    .ant-table-tbody > tr > .ant-table-cell {
      padding: 10px 16px;
      border-bottom: none;
      color: var(--theme-text-color-default);
      background-color: var(--theme-layout-color-bg-white);
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 13px;
      line-height: 15px;

      &.ant-table-selection-column {
        padding: 10px 0 10px 16px;
      }

      &.file-columns {
        padding: 4px;
      }
    }

    .ant-table-thead > tr > .ant-table-cell {
      color: var(--theme-text-color-medium);
      font-size: 13px;
      font-weight: 500;
      line-height: 20px;
      padding: 12px 16px;
      background-color: var(--theme-layout-color-bg-white);

      &::before {
        display: none;
      }
    }

    .ant-spin-nested-loading > div > .ant-spin {
      max-height: unset;
    }
  }
}

.styledEmptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.styledEmptyText {
  color: var(--theme-text-color-secondary);
}
