.styledSpaceItem {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 2px;
  background-color: transparent;
  cursor: pointer;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.selectedItem {
  background-color: var(--theme-menu-color-bg-hover);

  &:hover {
    background-color: var(--theme-menu-color-bg-active);
  }
}

.styleName {
  flex: 1;
  margin-left: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 400px;
  color: var(--theme-text-color-default);
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
}

.styledFileIcon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;

  & > svg {
    transform: scale(1.6);
    transform-origin: 0 0;
  }
}
