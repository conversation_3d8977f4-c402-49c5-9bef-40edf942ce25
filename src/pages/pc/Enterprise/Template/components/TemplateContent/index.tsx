import { Button, Empty, Form, message, Modal, Spin, Table } from 'antd';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { to } from '@/api/Request';
import { deleteTemplate, loadTemplateTypeData } from '@/api/Template';
import { ReactComponent as EmptySvg } from '@/assets/images/enterprise/template/empty.svg';
import { FileCreateTypeKey, FileCreateValueKey, FilePathTypeKey, TableActionKey } from '@/model/Common';
import type { OptionType, TemplateDataItem } from '@/model/Template';
import { UserFeature } from '@/model/UserPermission';
import { fm2 } from '@/modules/Locale';
import { getFileIcon } from '@/pages/pc/Enterprise/utils';
import { useUserCheckPoint } from '@/service/Me';

import { AddTemplatePop } from '../AddTemplatePop';
import { TemplateTypeSelect } from '../TemplateTypeSelect';
import { columns } from './columns';
import styles from './index.less';

const siderMenuCreateDocText = fm2('SiderMenu.siderMenuCreateDocText');
const siderMenuCreateMoDocText = fm2('SiderMenu.siderMenuCreateMoDocText');
const siderMenuCreateTableText = fm2('SiderMenu.siderMenuCreateTableText');
const siderMenuCreateMoTableText = fm2('SiderMenu.siderMenuCreateMoTableText');
const siderMenuCreatePptText = fm2('SiderMenu.siderMenuCreatePptText');
const siderMenuCreateFormText = fm2('SiderMenu.siderMenuCreateFormText');

export function TemplateContent() {
  const optionRender = useCallback(
    (option: { data: OptionType }) => (
      <div className={classNames('selectIcon', styles.selectIcon)}>
        <span>{getFileIcon(2, option.data.value)}</span>
        <span>{option.data.label}</span>
      </div>
    ),
    [],
  );

  const { isNoFeaturePoint } = useUserCheckPoint();

  const options = useMemo<OptionType[]>(() => {
    return [
      {
        typeValue: FileCreateTypeKey.doc,
        label: siderMenuCreateDocText,
        icon: null,
        value: FileCreateValueKey.doc,
        fileValue: FilePathTypeKey.doc,
        hidden: isNoFeaturePoint(UserFeature.SupportNewdoc),
      },
      {
        typeValue: FileCreateTypeKey.docx,
        label: siderMenuCreateMoDocText,
        icon: null,
        value: FileCreateValueKey.docx,
        fileValue: FilePathTypeKey.docx,
        hidden: isNoFeaturePoint(UserFeature.SupportModoc),
      },
      {
        typeValue: FileCreateTypeKey.sheet,
        label: siderMenuCreateTableText,
        icon: null,
        value: FileCreateValueKey.sheet,
        fileValue: FilePathTypeKey.sheet,
        hidden: isNoFeaturePoint(UserFeature.SupportMosheet),
      },
      {
        typeValue: FileCreateTypeKey.table,
        label: siderMenuCreateMoTableText,
        icon: null,
        value: FileCreateValueKey.table,
        fileValue: FilePathTypeKey.table,
        hidden: isNoFeaturePoint(UserFeature.SupportTable),
      },
      {
        typeValue: FileCreateTypeKey.presentation,
        label: siderMenuCreatePptText,
        icon: null,
        value: FileCreateValueKey.presentation,
        fileValue: FilePathTypeKey.presentation,
        hidden: isNoFeaturePoint(UserFeature.SupportPresentation),
      },
      {
        typeValue: FileCreateTypeKey.form,
        label: siderMenuCreateFormText,
        icon: null,
        value: FileCreateValueKey.form,
        fileValue: FilePathTypeKey.form,
        hidden: isNoFeaturePoint(UserFeature.SupportForm),
      },
    ];
  }, [isNoFeaturePoint]);

  const [maxHeight, setMaxHeight] = useState(900);

  const [dataList, setDataList] = useState<TemplateDataItem[]>([]);
  const [selectType, setSelectType] = useState<number>();
  const [isLoading, setIsLoading] = useState(true);
  // 加载数据的函数
  const fetchData = useCallback(
    async (value?: number) => {
      setIsLoading(true);
      const [err, res] = await to(loadTemplateTypeData({ type: value, page: 1, pageSize: 20 }));
      setIsLoading(false);
      if (res?.status !== 200) return message.error(err?.data?.msg);
      setDataList(res?.data?.publicTemplates || []);
    },
    [setIsLoading, setDataList],
  );

  const typeChange = useCallback(
    (item?: OptionType) => {
      setSelectType(item?.value);
      fetchData(item?.value);
    },
    [setSelectType, fetchData],
  );

  const typeSelectProp = useMemo(() => {
    return {
      options: options.filter((it) => !it.hidden),
      optionRender,
      typeChange,
    };
  }, [options, optionRender, typeChange]);

  const handleCreate = useCallback(() => {
    AddTemplatePop({
      type: TableActionKey.add,
      typeSelectProp: { ...typeSelectProp, disabled: true },
      onOk: () => {
        fetchData(selectType);
      },
    });
  }, [typeSelectProp, selectType, fetchData]);

  const calculateMaxHeight = () => {
    const containerHeight = window.innerHeight || document.documentElement.clientHeight;
    const tableHeaderHeight = 54;
    const headerHeight = 90;
    const padding = 16;
    const topHeader = 48 + 102;
    const contentHeight = containerHeight - topHeader - tableHeaderHeight - headerHeight - padding * 2;

    setMaxHeight(Math.max(contentHeight, 400));
  };

  useEffect(() => {
    fetchData();
    calculateMaxHeight();
  }, [fetchData]);

  const handleEdit = useCallback(
    (record: TemplateDataItem) => {
      AddTemplatePop({
        type: TableActionKey.edit,
        formValues: record,
        typeSelectProp: { ...typeSelectProp, disabled: true, defaultValue: record.type },
        onOk: () => {
          fetchData(selectType);
        },
      });
    },
    [typeSelectProp, selectType, fetchData],
  );

  const handleDelete = useCallback(
    async (record: TemplateDataItem) => {
      Modal.confirm({
        title: fm2('TemplateContent.deleteTemplate'),
        width: 400,
        content: fm2('TemplateContent.deleteTemplateContent'),
        className: 'delete-confirm-modal',
        icon: null,
        centered: true,
        closable: false,
        maskClosable: true,
        onOk: async () => {
          const [err, res] = await to(deleteTemplate(record?.id));
          if (res?.status !== 204) return message.error(err?.data?.msg || fm2('TemplateContent.deleteFail'));
          message.success(fm2('TemplateContent.deleteSuccess'));
          fetchData(selectType);
        },
      });
    },
    [selectType, fetchData],
  );

  const tableColumns = columns(options, handleEdit, handleDelete);

  return (
    <div className={styles.content}>
      <div className={styles.filterTop}>
        <Form>
          <TemplateTypeSelect {...typeSelectProp} />
        </Form>
        <Button type="primary" onClick={() => handleCreate()}>
          {fm2('TemplateContent.create')}
        </Button>
      </div>
      {isLoading ? (
        <Spin fullscreen />
      ) : (
        <div className={styles.templateList}>
          {dataList.length ? (
            <Table
              virtual
              columns={tableColumns}
              dataSource={dataList}
              pagination={false}
              rowKey="id"
              scroll={{ y: maxHeight }}
            />
          ) : (
            <Empty
              className={styles.emptyContent}
              description={
                <>
                  <div className={styles.emptyText}>
                    {fm2('TemplateContent.createTip')}
                    <Button className={styles.createBtn} type="link" onClick={() => handleCreate()}>
                      {fm2('TemplateContent.createRightNow')}
                    </Button>
                  </div>
                  <div className={styles.desc}>{fm2('TemplateContent.youCanSeeTemplate')}</div>
                </>
              }
              image={<EmptySvg />}
              styles={{ image: { height: 260 } }}
            />
          )}
        </div>
      )}
    </div>
  );
}
