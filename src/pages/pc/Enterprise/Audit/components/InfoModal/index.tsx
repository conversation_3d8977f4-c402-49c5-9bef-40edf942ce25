import { Modal, Typography } from 'antd';
import type { FC } from 'react';

import type { LogRecord } from '@/model/audit';
import { useFormatTime } from '@/utils/file';

import { useAuditStore } from '../../store';
import styles from './index.less';

interface Props {
  visible: boolean;
  item: LogRecord | null;
  onCancel: () => void;
  fm: Record<string, string>;
}

export const InfoModal: FC<Props> = ({ item, visible, onCancel, fm }) => {
  const { description } = useAuditStore((state) => state);
  const { formatTime } = useFormatTime();
  if (!item) {
    return null;
  }

  const { targetFile } = item;
  let targeContent = null;
  if (targetFile) {
    targeContent = (
      <Typography.Link underline className={styles.styles} href={targetFile.url} target="_blank">
        {item.targetFile.name}
      </Typography.Link>
    );
  } else {
    targeContent = <Typography.Text ellipsis={{ tooltip: item.notes }}>{item.notes ?? ''}</Typography.Text>;
  }

  const renderTime = (time: string | number) => {
    return typeof time === 'number' ? formatTime(time * 1000) : '';
  };

  const renderOperator = (operator: string) => {
    const regEx = /\](.*)</;
    const result = regEx.exec(operator);
    let operatorString = operator;
    if (result) {
      operatorString = result[1];
    }
    return operatorString;
  };

  return (
    <Modal centered footer={false} open={visible} title={fm.operateDetail} width={618} onCancel={onCancel}>
      <div className={styles.content}>
        <div className={styles.infoRow}>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.createdAt}</div>
            <div className={styles.nfoItemValue}>{renderTime(item.createdAt)}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.ip}</div>
            <div className={styles.nfoItemValue}>{item.ip}</div>
          </div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.action}</div>
            <div className={styles.nfoItemValue}>{description[item.action]}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.operator}</div>
            <div className={styles.nfoItemValue}>{renderOperator(item.operator)}</div>
          </div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.operand}</div>
            {targeContent}
          </div>
          <div className={styles.infoItem}>
            <div className={styles.infoItemTitle}>{fm.relate}</div>
            <div className={styles.nfoItemValue}>{item.operator}</div>
          </div>
        </div>
        <div>
          <div className={styles.infoItemTitle}>{fm.terminal}</div>
          <div className={styles.nfoItemValue}>{item.ua}</div>
        </div>
      </div>
    </Modal>
  );
};
