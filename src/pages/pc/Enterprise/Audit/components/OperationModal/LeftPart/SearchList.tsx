import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { FC } from 'react';

import type { operationNode } from '@/model/audit';
import { fm } from '@/modules/Locale';
import searchEmpty from '@/pages/pc/Enterprise/assets/audit/search_empty.png';

import { useAuditStore } from '../../../store';
import { GroupItem } from './GroupItem';
import styles from './index.less';

interface Props {
  removeOperationFromList: (id: string) => void;
  addOperationToList: (v: { operationId: string; groupId: string; tabId: string; name: string }) => void;
  searchList: operationNode[];
  listSets: Set<string>[];
  keyword: string;
  changeListSets: (v: Set<string>[]) => void;
}

export const SearchList: FC<Props> = ({
  removeOperationFromList,
  addOperationToList,
  searchList,
  listSets,
  keyword,
  changeListSets,
}) => {
  const { description } = useAuditStore((state) => state);

  /**
   * 整个组选择变化
   */
  const groupOperationCheckChange = ({
    e,
    groupIndex,
    groupId,
    tabId,
  }: {
    e: CheckboxChangeEvent;
    groupIndex: number;
    groupId: string;
    tabId: string;
  }) => {
    const operationSet = listSets[groupIndex];
    const currentOperationList = searchList[groupIndex].children || [];
    currentOperationList.forEach((item) => {
      if (!e.target.checked) {
        // 删除
        removeOperationFromList(item.id);
      } else {
        // 添加
        operationSet.add(item.id);
        addOperationToList({
          operationId: item.id,
          groupId,
          tabId,
          name: description[item.id] || '',
        });
      }
    });
    if (!e.target.checked) {
      operationSet.clear();
    }

    changeListSets([...listSets]);
  };

  /**
   * 单个操作选中状态发生变化时
   */
  const operationChange = ({
    e,
    groupIndex,
    operationItemId,
    groupId,
    tabId,
  }: {
    e: CheckboxChangeEvent;
    groupIndex: number;
    operationItemId: string;
    groupId: string;
    tabId: string;
  }) => {
    const groupSet = listSets[groupIndex];

    if (e.target.checked) {
      groupSet.add(operationItemId);
      addOperationToList({
        groupId,
        operationId: operationItemId,
        tabId,
        name: description[operationItemId] || '',
      });
    } else {
      removeOperationFromList(operationItemId);
      groupSet.delete(operationItemId);
    }

    // 触发更新
    changeListSets([...listSets]);
  };

  return (
    <div className={styles.searchList}>
      {searchList.length === 0 ? (
        <div className={styles.emptyHolder}>
          <img alt="" src={searchEmpty} />
          <div className={styles.emptyText}>{fm('Operation.noSearchResult')}</div>
        </div>
      ) : (
        searchList.map((groupItem, groupIndex) => {
          const currentGroupSet = listSets[groupIndex];
          const total = searchList?.[groupIndex]?.children?.length || 0;

          return (
            <GroupItem
              key={groupItem.id}
              currentGroupSet={currentGroupSet}
              groupCheckChange={(e) => {
                groupOperationCheckChange({
                  e,
                  groupIndex,
                  groupId: groupItem.id,
                  tabId: groupItem.tabId!,
                });
              }}
              groupIndeterminate={currentGroupSet.size > 0 && currentGroupSet.size < total}
              groupItem={groupItem}
              groupItemChecked={currentGroupSet.size === total}
              keyword={keyword}
              operationCheckChange={(e, id) => {
                operationChange({
                  e,
                  groupIndex,
                  operationItemId: id,
                  groupId: groupItem.id,
                  tabId: groupItem.tabId!,
                });
              }}
            />
          );
        })
      )}
    </div>
  );
};
