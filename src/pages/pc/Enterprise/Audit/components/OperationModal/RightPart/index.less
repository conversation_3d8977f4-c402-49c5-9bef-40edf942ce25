.rightPart {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.selectedNotice {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 32px;

  &.error {
    color: var(--theme-text-color-alert);
  }
}

.selectedListContainer {
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  margin-top: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
}

.clearTextContainer {
  text-align: right;
  padding: 7px 12px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.clearText {
  color: var(--theme-separator-color-guidance);
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;

  &.disabled {
    color: var(--theme-text-color-disabled);
    cursor: not-allowed;
  }
}

.listContainer {
  overflow-y: auto;
  padding-bottom: 24px;
  flex: 1;
  height: 0;
}

.selectedItem {
  padding: 4px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--theme-menu-color-bg-hover);
  margin-top: 8px;
}

.itemName {
  color: var(--theme-text-color-default);
  font-size: 12px;
  line-height: 20px;
  flex: 1 0 0;
}

.closeContainer {
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);

  &:hover {
    color: var(--theme-text-color-default);
  }

  cursor: pointer;
}
