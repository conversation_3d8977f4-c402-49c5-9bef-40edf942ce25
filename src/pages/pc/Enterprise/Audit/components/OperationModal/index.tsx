import { Modal } from 'antd';
import { cloneDeep } from 'lodash';
import type { FC } from 'react';
import { useEffect, useState } from 'react';

import type { SelectionItem } from '@/model/audit';
import { fm } from '@/modules/Locale';

import styles from './index.less';
import { LeftPart } from './LeftPart/index';
import { MAX_LENGTH, RightPart } from './RightPart/index';

interface Props {
  visible: boolean;
  hideModal: () => void;
  setSelectOperations: (list: SelectionItem[]) => void;
  outerSelectionList: SelectionItem[];
}

export const OperationModal: FC<Props> = ({ visible, hideModal, setSelectOperations, outerSelectionList }) => {
  const i8nText = {
    addFilter: fm('Operation.addFilter'),
  };
  const [selectionList, setSelectionList] = useState<SelectionItem[]>([]);

  useEffect(() => {
    if (visible) {
      const newList = cloneDeep(outerSelectionList);
      setSelectionList(newList);
    }
  }, [outerSelectionList, visible]);

  const changeSelectionList = (list: SelectionItem[]) => {
    const newList = cloneDeep(list);
    setSelectionList(newList);
  };

  const addOperationToList = ({
    operationId,
    groupId,
    tabId,
    name,
  }: {
    operationId: string;
    groupId: string;
    name: string;
    tabId: string;
  }) => {
    if (!selectionList.find((item: SelectionItem) => item.id === operationId)) {
      selectionList.push({
        groupId,
        id: operationId,
        name,
        tabId,
      });

      changeSelectionList([...selectionList]);
    }
  };

  const removeOperationFromList = (id: string) => {
    const removeIndex = selectionList.findIndex((item) => item.id === id);
    if (removeIndex > -1) {
      selectionList.splice(removeIndex, 1);
      changeSelectionList([...selectionList]);
    }
  };

  const clearAll = () => {
    changeSelectionList([]);
  };

  const onOK = () => {
    const newSelectionList = cloneDeep(selectionList);
    setSelectOperations(newSelectionList);
    hideModal();
  };

  return (
    <Modal
      okButtonProps={{ disabled: selectionList.length > MAX_LENGTH }}
      open={visible}
      title={i8nText.addFilter}
      width={670}
      onCancel={hideModal}
      onOk={onOK}
    >
      <div className={styles.operationContainer}>
        <LeftPart
          addOperationToList={addOperationToList}
          removeOperationFromList={removeOperationFromList}
          selectionList={selectionList}
        />
        <RightPart
          clearAll={clearAll}
          removeOperationFromList={removeOperationFromList}
          selectionList={selectionList}
        />
      </div>
    </Modal>
  );
};
