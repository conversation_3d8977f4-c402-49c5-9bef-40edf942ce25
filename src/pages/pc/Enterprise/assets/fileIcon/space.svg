<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55722)">
<g filter="url(#filter0_d_2040_55722)">
<path d="M19.37 5.07388C19.5433 5.12345 19.5433 5.3765 19.37 5.42608L12.2374 7.46666C12.082 7.51111 11.9178 7.51111 11.7624 7.46666L4.62996 5.42612C4.45668 5.37655 4.45668 5.1235 4.62996 5.07392L11.7626 3.03334C11.918 2.98889 12.0822 2.98889 12.2376 3.03334L19.37 5.07388Z" fill="#B8C9DA"/>
</g>
<g filter="url(#filter1_d_2040_55722)">
<path d="M11.25 9.30635C11.25 9.10648 11.1157 8.93055 10.9203 8.87444L3.58701 6.76838C3.2936 6.68412 3 6.90014 3 7.2003V19.1936C3 19.3935 3.13428 19.5694 3.32966 19.6256L10.663 21.7316C10.9564 21.8159 11.25 21.5999 11.25 21.2997V9.30635Z" fill="url(#paint0_linear_2040_55722)"/>
</g>
<g filter="url(#filter2_d_2040_55722)">
<path d="M12.75 21.2997C12.75 21.5999 13.0436 21.8159 13.337 21.7316L20.6703 19.6256C20.8657 19.5694 21 19.3935 21 19.1936V7.2003C21 6.90014 20.7064 6.68412 20.413 6.76838L13.0797 8.87444C12.8843 8.93055 12.75 9.10648 12.75 9.30636V21.2997Z" fill="url(#paint1_linear_2040_55722)"/>
</g>
</g>
<defs>
<filter id="filter0_d_2040_55722" x="0.5" y="1" width="23" height="12.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55722"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55722" result="shape"/>
</filter>
<filter id="filter1_d_2040_55722" x="-1" y="4.75" width="16.25" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55722"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55722" result="shape"/>
</filter>
<filter id="filter2_d_2040_55722" x="8.75" y="4.75" width="16.25" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55722"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55722" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55722" x1="3" y1="6.75" x2="3" y2="21.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#B2C2D5"/>
<stop offset="0.971281" stop-color="#9EB1C4"/>
</linearGradient>
<linearGradient id="paint1_linear_2040_55722" x1="12.75" y1="6.75" x2="12.75" y2="21.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#B2C2D5"/>
<stop offset="0.971281" stop-color="#9EB1C4"/>
</linearGradient>
<clipPath id="clip0_2040_55722">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
