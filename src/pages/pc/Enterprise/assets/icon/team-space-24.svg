<svg width="24" height="24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_2640_60236)">
    <g filter="url(#filter0_d_2640_60236)">
      <path d="M19.37 5.074c.173.05.173.303 0 .352l-7.133 2.04a.863.863 0 01-.475 0L4.63 5.427c-.173-.05-.173-.303 0-.352l7.133-2.04a.863.863 0 01.475 0l7.132 2.04z" fill="#B8C9DA"/>
    </g>
    <g filter="url(#filter1_d_2640_60236)">
      <path d="M11.25 9.306c0-.2-.134-.375-.33-.432L3.587 6.768A.457.457 0 003 7.2v11.994c0 .2.134.375.33.432l7.333 2.106a.457.457 0 00.587-.432V9.306z" fill="url(#paint0_linear_2640_60236)"/>
    </g>
    <g filter="url(#filter2_d_2640_60236)">
      <path d="M12.75 21.3c0 .3.294.516.587.432l7.333-2.106a.451.451 0 00.33-.432V7.2c0-.3-.294-.516-.587-.432L13.08 8.874a.451.451 0 00-.33.432V21.3z" fill="url(#paint1_linear_2640_60236)"/>
    </g>
  </g>
  <defs>
    <filter id="filter0_d_2640_60236" x=".5" y="1" width="23" height="12.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2640_60236"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_2640_60236" result="shape"/>
    </filter>
    <filter id="filter1_d_2640_60236" x="-1" y="4.75" width="16.25" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2640_60236"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_2640_60236" result="shape"/>
    </filter>
    <filter id="filter2_d_2640_60236" x="8.75" y="4.75" width="16.25" height="23" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_2640_60236"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_2640_60236" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_2640_60236" x1="3" y1="6.75" x2="3" y2="21.75" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B2C2D5"/>
      <stop offset=".971" stop-color="#9EB1C4"/>
    </linearGradient>
    <linearGradient id="paint1_linear_2640_60236" x1="12.75" y1="6.75" x2="12.75" y2="21.75" gradientUnits="userSpaceOnUse">
      <stop stop-color="#B2C2D5"/>
      <stop offset=".971" stop-color="#9EB1C4"/>
    </linearGradient>
    <clipPath id="clip0_2640_60236">
      <path fill="#fff" d="M0 0h24v24H0z"/>
    </clipPath>
  </defs>
</svg>
