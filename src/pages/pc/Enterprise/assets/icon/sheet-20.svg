<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_3776_12132)">
    <g filter="url(#filter0_d_3776_12132)">
      <path fill="#fff" d="M1.25 3.75h17.5v13.125H1.25z"/>
      <path stroke="#41464B" stroke-opacity=".15" d="M.75 3.25h18.5v14.125H.75z"/>
    </g>
    <path fill="url(#paint0_linear_3776_12132)" stroke="#fff" d="M1.75 4.25h16.5v12.125H1.75z"/>
    <path fill="#000" fill-opacity=".04" d="M1.25 7.5h5.625v9.375H1.25z"/>
    <path fill="#BCDDC3" fill-opacity=".8" d="M1.25 3.75h17.5V7.5H1.25z"/>
    <path fill="#41464B" fill-opacity=".1" d="M6.875 10h11.25v.625H6.875zM6.875 13.125h11.25v.625H6.875zM1.25 6.875h17.5V7.5H1.25z"/>
    <path fill="#41464B" fill-opacity=".1" d="M6.25 3.75h.625v13.125H6.25zM12.5 3.75h.625v12.5H12.5z"/>
  </g>
  <defs>
    <linearGradient id="paint0_linear_3776_12132" x1="18.75" y1="16.875" x2="18.75" y2="3.75" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FCFCFC"/>
      <stop offset="1" stop-color="#F5F5F5"/>
      <stop offset="1" stop-color="#F5F5F5"/>
    </linearGradient>
    <clipPath id="clip0_3776_12132">
      <path fill="#fff" d="M0 0h20v20H0z"/>
    </clipPath>
    <filter id="filter0_d_3776_12132" x="-3.75" y=".75" width="27.5" height="23.125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3776_12132"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_3776_12132" result="shape"/>
    </filter>
  </defs>
</svg>
