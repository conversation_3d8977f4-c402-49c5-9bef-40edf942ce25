<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_3776_12157)">
    <g filter="url(#filter0_d_3776_12157)">
      <path fill="#fff" d="M1.25 3.75h17.5v13.125H1.25z"/>
      <path stroke="#41464B" stroke-opacity=".15" d="M.75 3.25h18.5v14.125H.75z"/>
    </g>
    <path fill="url(#paint0_linear_3776_12157)" stroke="#fff" d="M1.75 4.25h16.5v12.125H1.75z"/>
    <path d="M6.25 14.625v-.218l1.123-.546V6.39L6.25 5.843v-.218h3.882c1.09 0 1.964.195 2.62.586.665.391.998 1.01.998 1.855 0 .5-.176.945-.527 1.336-.342.382-.809.687-1.4.914-.591.218-1.266.327-2.024.327h-.97v3.218l1.275.546v.218H6.25zm2.579-8.25V9.92h1.053c.73 0 1.303-.163 1.72-.49.415-.328.623-.746.623-1.255 0-.636-.213-1.095-.638-1.377-.416-.282-1.016-.423-1.802-.423H8.83z" fill="#D0692F" fill-opacity=".75"/>
  </g>
  <defs>
    <linearGradient id="paint0_linear_3776_12157" x1="18.75" y1="16.875" x2="18.75" y2="3.75" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FCFCFC"/>
      <stop offset="1" stop-color="#F5F5F5"/>
      <stop offset="1" stop-color="#F5F5F5"/>
    </linearGradient>
    <clipPath id="clip0_3776_12157">
      <path fill="#fff" d="M0 0h20v20H0z"/>
    </clipPath>
    <filter id="filter0_d_3776_12157" x="-3.75" y=".75" width="27.5" height="23.125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3776_12157"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_3776_12157" result="shape"/>
    </filter>
  </defs>
</svg>
