<svg width="20" height="20" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_3776_12120)">
    <g filter="url(#filter0_d_3776_12120)">
      <path d="M12.495 1.25h-9.37v17.5h13.75V5.63l-4.38-4.38z" fill="#fff"/>
      <path d="M12.849.896L12.703.75H2.625v18.5h14.75V5.423l-.146-.147-4.38-4.38z" stroke="#41464B" stroke-opacity=".15"/>
    </g>
    <path d="M3.625 18.25V1.75h8.663l4.087 4.087V18.25H3.625z" fill="url(#paint0_linear_3776_12120)" stroke="#fff"/>
    <g filter="url(#filter1_d_3776_12120)">
      <path d="M12.509 1.25H12.5v4.375h4.375v-.009L12.509 1.25z" fill="#fff"/>
    </g>
    <path d="M14.815 7.517l-.14-.017a6.82 6.82 0 00-1.254.545l-.209.112.166.171c.41.42.61.87.61 1.356 0 .18-.035.404-.106.67a7.773 7.773 0 01-.298.88c-.127.316-.276.637-.448.966l-.13.24c-.09.159-.18.31-.276.457l-.052.077-.386-1.618c-.04-.168-.08-.355-.126-.562l-.261-1.24a96.048 96.048 0 01-.183-.906l-.188-.97-1.252.287-.028.103a4.427 4.427 0 01-.078.246l-.092.258c-.055.146-.129.34-.224.583l-.24.605-1.17 2.843-.633-4.098.259-.069.082-.05.623-.683H5.435l-.095.028-.965.63 1.501.172L7.2 15H8.31l.037-.13c.057-.201.135-.443.233-.723l.11-.303c.04-.109.083-.224.13-.346l.15-.389c.162-.41.36-.9.592-1.467l.09-.217c.122-.295.259-.615.409-.96l.238-.546L11.34 15h1.183l.257-.615c.065-.155.152-.343.26-.564l.084-.172c.124-.248.278-.538.462-.87l.114-.203c.235-.417.54-.92.912-1.511.674-1.04 1.013-1.774 1.013-2.233 0-.552-.277-.996-.81-1.315z" fill="#8AB3E9"/>
  </g>
  <defs>
    <filter id="filter0_d_3776_12120" x="-1.875" y="-1.75" width="23.75" height="27.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="2"/>
      <feGaussianBlur stdDeviation="2"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3776_12120"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_3776_12120" result="shape"/>
    </filter>
    <filter id="filter1_d_3776_12120" x="10.5" y=".25" width="8.375" height="8.375" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="1"/>
      <feGaussianBlur stdDeviation="1"/>
      <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_3776_12120"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_3776_12120" result="shape"/>
    </filter>
    <linearGradient id="paint0_linear_3776_12120" x1="16.875" y1="18.75" x2="16.875" y2="1.25" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FCFCFC"/>
      <stop offset="1" stop-color="#F5F5F5"/>
      <stop offset="1" stop-color="#F5F5F5"/>
    </linearGradient>
    <clipPath id="clip0_3776_12120">
      <path fill="#fff" d="M0 0h20v20H0z"/>
    </clipPath>
  </defs>
</svg>
