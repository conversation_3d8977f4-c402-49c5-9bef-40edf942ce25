import { Button, Progress, Switch } from 'antd';

import { UploadLogo } from '@/components/Modal/UploadLogo';
import { fm2 } from '@/modules/Locale';
import { formatFileSizeHuman } from '@/utils/file';

import css from './index.less';
import type { Capacity, CurrentView, EnterpriseInfo, Settings } from './types';

interface Props {
  info: EnterpriseInfo;
  capacity: Capacity | null;
  settings: Settings;
  onWatermarkChange: (checked: boolean) => void;
  onLogoChange: (url: string) => void;
  setCurrentView: (view: CurrentView) => void;
}

export default function Main({ info, capacity, settings, onWatermarkChange, onLogoChange, setCurrentView }: Props) {
  function modifyLogo() {
    UploadLogo({
      type: info.avatar ? 'modify' : 'upload',
      title: fm2('Enterprise.logo'),
      logoUrl: info.avatar,
      onOk: (url: string) => {
        onLogoChange(url);
      },
    });
  }

  // 普通成员时，禁用“上传头像”、“协作水印设置”、“企业回收站” 功能
  const disabled = !['creator', 'admin'].includes(settings.teamRole);

  return (
    <div className={css.mainContainer}>
      {/* 企业信息 */}
      <div className={css.section}>
        <div className={css.title}>{fm2('Enterprise.info')}</div>

        {/* 企业logo */}
        <div className={css.row}>
          <div>
            <div className={css.label}>{fm2('Enterprise.logo')}</div>
            <div className={css.logo}>
              {info.avatar ? (
                <img className={css.logoImg} src={info.avatar} />
              ) : (
                <span className={css.logoText}>{fm2('Enterprise.logoTip')}</span>
              )}
            </div>
          </div>
          <Button className={css.btn} disabled={disabled} onClick={modifyLogo}>
            {info.avatar ? fm2('Enterprise.modify') : fm2('Enterprise.upload')}
          </Button>
        </div>

        <div className={css.divider} />

        {/* 企业名称 */}
        <div>
          <div className={css.label}>{fm2('Enterprise.name')}</div>
          <div className={css.desc}>{info.name}</div>
        </div>

        <div className={css.divider} />

        {/* 企业ID */}
        <div>
          <div className={css.label}>{fm2('Enterprise.id')}</div>
          <div className={css.desc}>{info.id}</div>
        </div>

        <div className={css.divider} />

        {/* 企业授权情况 */}
        <div className={css.row}>
          <div>
            <div className={css.smallLabel}>{fm2('Enterprise.auth')}</div>
            <div className={css.desc}>{fm2('Enterprise.authDesc')}</div>
          </div>

          <Button className={css.btn} disabled={disabled} onClick={() => setCurrentView('auth')}>
            {fm2('Enterprise.view')}
          </Button>
        </div>
        <div className={css.divider} />
      </div>

      {/* 文件设置 */}
      <div className={css.section}>
        <div className={css.title}>{fm2('Enterprise.fileSettings')}</div>

        {/* 容量管理 */}
        <div className={css.row}>
          <div className={css.smallLabel}>{fm2('Enterprise.capacityManage')}</div>
          <div className={css.storageSection}>
            {capacity && (
              <>
                <Progress
                  className={css.progressBar}
                  percent={Number(((capacity.used / capacity.total) * 100).toFixed(2))}
                  showInfo={false}
                  size={{ width: 175, height: 4 }}
                  strokeColor="var(--theme-basic-color-guidance)"
                />
                <div className={css.storageText}>
                  {`${formatFileSizeHuman(capacity.used)}/${formatFileSizeHuman(capacity.total)}`}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      <div className={css.divider} />

      {/* 模版库设置 */}
      <div className={css.section}>
        <div className={css.title}>{fm2('Enterprise.templateSettings')}</div>

        {/* 模版库管理 */}
        <div className={css.row}>
          <div className={css.smallLabel}>{fm2('Enterprise.templateManage')}</div>
          <Button className={css.btn} disabled={disabled} onClick={() => setCurrentView('template')}>
            {fm2('Enterprise.toSet')}
          </Button>
        </div>
      </div>

      <div className={css.divider} />

      {/* 安全设置 */}
      <div className={css.section}>
        <div className={css.title}>{fm2('Enterprise.safetySettings')}</div>

        {/* 为企业展示协作者水印 */}
        <div className={css.row}>
          <div>
            <div className={css.smallLabel}>{fm2('Enterprise.watermark')}</div>
            <div className={css.desc}>
              <div>{fm2('Enterprise.watermarkTip')}</div>
              <div>{fm2('Enterprise.watermarkTip2')}</div>
              <div>{fm2('Enterprise.watermarkTip3')}</div>
            </div>
          </div>
          <Switch
            checked={settings.watermark}
            className={css.switch}
            disabled={disabled}
            onChange={onWatermarkChange}
          />
        </div>

        <div className={css.divider} />

        {/* 企业回收站 */}
        <div className={css.row}>
          <div>
            <div className={css.smallLabel}>{fm2('Enterprise.recycleBin')}</div>
            <div className={css.desc}>{fm2('Enterprise.recycleBinDesc')}</div>
          </div>
          <Button className={css.btn} disabled={disabled} onClick={() => setCurrentView('recycleBin')}>
            {fm2('Enterprise.view')}
          </Button>
        </div>

        <div className={css.divider} />
      </div>
    </div>
  );
}
