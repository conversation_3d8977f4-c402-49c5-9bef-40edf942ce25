export interface EnterpriseInfo {
  avatar: string;
  name: string;
  id: string;
}

export interface Capacity {
  total: number;
  used: number;
}

export interface Settings {
  watermark: boolean;
  teamRole: string;
}

export interface AuthStatus {
  memberLimit: number;
  userCount: number;
  validFrom: number;
  validUntil: number;
}

export type CurrentView = 'main' | 'auth' | 'template' | 'recycleBin';
