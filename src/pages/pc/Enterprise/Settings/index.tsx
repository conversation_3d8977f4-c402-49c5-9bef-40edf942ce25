import { message, Spin } from 'antd';
import { useEffect, useState } from 'react';

import * as EnterpriseApi from '@/api/EnterpriseSettings';
import * as MeApi from '@/api/Me';
import { to } from '@/api/Request';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import Template from '../Template';
import Trash from '../Trash';
import Auth from './Auth';
import css from './index.less';
import Main from './Main';
import type { AuthStatus, Capacity, CurrentView, EnterpriseInfo, Settings } from './types';

export default function EnterpriseSettings() {
  const me = useMeStore((state) => state.me);
  const [currentView, setCurrentView] = useState<CurrentView>('main');
  const [loading, setLoading] = useState(true);
  const [info, setInfo] = useState({} as EnterpriseInfo);
  const [capacity, setCapacity] = useState<Capacity | null>(null);
  const [settings, setSettings] = useState<Settings>({ watermark: false, teamRole: me.teamRole || '' });
  const [updating, setUpdating] = useState(false);
  const [authStatus, setAuthStatus] = useState<AuthStatus | null>(null);

  const i18n = {
    watermarkOn: fm('Enterprise.watermarkOn'),
    watermarkOff: fm('Enterprise.watermarkOff'),
    auth: fm('Enterprise.auth'),
    template: fm('Enterprise.template'),
    recycleBin: fm('Enterprise.recycleBin'),
    id: fm('Enterprise.id'),
    settings: fm('Enterprise.settings'),
  };

  async function getInfo() {
    const [, res] = await to(EnterpriseApi.info(me.teamId as number));
    setLoading(false);
    if (res?.status === 200) {
      setInfo(res.data);
    }
  }

  async function getAuthStatus() {
    const [, res] = await to(EnterpriseApi.license());
    if (res?.status === 200) {
      setAuthStatus(res.data);
    }
  }

  async function getCapacity() {
    const [, res] = await to(MeApi.quota());
    if (res?.status === 200) {
      const { teamDiskVolume } = res.data;
      setCapacity({
        total: teamDiskVolume.quota,
        used: teamDiskVolume.used,
      });
    }
  }

  async function getSettings() {
    const [, res] = await to(EnterpriseApi.settings('watermark'));
    if (res?.status === 200) {
      setSettings((prev) => ({ ...prev, watermark: res.data.status }));
    }
  }

  async function onWatermarkChange(checked: boolean) {
    // 防止频繁点击
    if (updating) return;
    setUpdating(true);
    const params = {
      status: checked,
      extra: '',
    };
    const [err, res] = await to(EnterpriseApi.updateSettings('watermark', params));
    setUpdating(false);
    if (res?.status !== 204) return message.error(err?.data?.msg);
    const tip = checked ? i18n.watermarkOn : i18n.watermarkOff;
    message.success(tip);
    setSettings((prev) => ({ ...prev, watermark: checked }));
  }

  function onLogoChange(url: string) {
    setInfo((prev) => ({ ...prev, avatar: url }));
  }

  useEffect(() => {
    getInfo();
    getCapacity();
    getSettings();
    getAuthStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 只在组件挂载时执行一次，无需依赖任何变量

  // 返回主页面
  function back() {
    setCurrentView('main');
  }

  const viewTitleMap = {
    auth: i18n.auth,
    template: i18n.template,
    recycleBin: i18n.recycleBin,
  };

  function renderHeader() {
    if (currentView === 'main') {
      return <div className={css.header}>{i18n.settings}</div>;
    }
    return (
      <div className={css.authHeader}>
        <div className={css.authTitle} onClick={back}>
          {i18n.settings}
        </div>
        <div className={css.authInfo}>
          <span className={css.authLabel}>{viewTitleMap[currentView]}</span>
          <span className={css.authDesc}>
            {info.name} / {i18n.id} {info.id}
          </span>
        </div>
      </div>
    );
  }

  function renderContent() {
    switch (currentView) {
      case 'auth':
        return <Auth authStatus={authStatus} />;
      case 'template':
        return <Template />;
      case 'recycleBin':
        return <Trash />;
      default:
        return (
          <Main
            capacity={capacity}
            info={info}
            setCurrentView={setCurrentView}
            settings={settings}
            onLogoChange={onLogoChange}
            onWatermarkChange={onWatermarkChange}
          />
        );
    }
  }

  return (
    <div className={css.container}>
      {renderHeader()}

      <div className={css.content}>
        <Spin spinning={loading}>{renderContent()}</Spin>
      </div>
    </div>
  );
}
