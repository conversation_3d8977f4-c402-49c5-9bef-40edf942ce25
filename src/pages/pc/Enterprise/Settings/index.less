.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme-layout-color-bg-new-page);
  border-radius: 8px;
  overflow: hidden;
}

.content {
  flex: 1;
  overflow: auto;
  margin: 16px;
  border-radius: 8px;
  background: var(--theme-basic-color-bg-default);

  :global {
    .ant-spin-nested-loading {
      height: 100%;

      .ant-spin-container {
        height: 100%;
      }

      .ant-spin {
        max-height: 650px;
      }
    }
  }
}

.mainContainer {
  padding: 20px;
  border: 1px solid var(--theme-separator-color-lighter);
}

.authContainer {
  padding: 20px;
  border: 1px solid var(--theme-separator-color-lighter);
}

.divider {
  height: 1px;
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin: 20px 0;
}

.header {
  display: flex;
  align-items: center;
  padding-left: 24px;
  height: 72px;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  color: var(--theme-brand-color);
  border-bottom: 1px solid var(--theme-basic-color-lighter);
}

.authHeader {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 102px;
  padding-left: 24px;
  border-bottom: 1px solid var(--theme-basic-color-lighter);

  .authInfo {
    margin-top: 14px;
    display: flex;
    align-items: center;
    gap: 12px;

    .desc {
      margin-top: 0;
    }
  }

  .authTitle {
    line-height: 22px;
    color: var(--theme-text-color-secondary);
    cursor: pointer;
  }

  .authLabel {
    font-size: 20px;
    font-weight: 500;
    line-height: 28px;
    color: var(--theme-brand-color);
  }

  .authDesc {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: var(--theme-text-color-secondary); // todo: 颜色要修改
  }
}

.section {
  margin-top: 60px;

  &:first-child {
    margin-top: 0;
  }
}

.row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  margin-bottom: 30px;
  font-size: 20px;
  font-weight: 500;
  line-height: 32px;
  color: var(--theme-brand-color);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 260px;
  height: 60px;
  margin-top: 20px;
  font-size: 20px;
  color: var(--theme-brand-color);
  background-color: var(--theme-text-button-color-hover);
}

.logoText {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: var(--theme-basic-color-black);
}

.logoImg {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.label {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-brand-color);
  line-height: 24px;
}

.smallLabel {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-brand-color);
  line-height: 24px;
}

.desc {
  margin-top: 8px;
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  color: var(--theme-text-color-secondary);
}

.btn {
  padding: 0 31px;
}

.storageSection {
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.progressBar {
  line-height: 1;
  font-size: 0;
}

.storageText {
  font-size: 12px;
  color: var(--theme-text-color-secondary);
  line-height: 20px;
}

.switch {
  &:global(.ant-switch-checked) {
    background-color: var(--theme-basic-color-notice);
  }

  &:global(.ant-switch-checked):hover:not(.ant-switch-disabled) {
    background-color: var(--theme-button-color-primary-hover);
  }
}
