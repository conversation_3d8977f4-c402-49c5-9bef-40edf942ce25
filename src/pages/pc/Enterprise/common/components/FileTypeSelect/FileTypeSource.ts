import { UserFeature } from '@/model/UserPermission';
import { fm2 } from '@/modules/Locale';

import { FileSubType, FileType } from './type';

export function getFileTypeSource() {
  return [
    {
      name: fm2('SiderMenu.siderMenuCreateDocText'),
      type: FileType.ShimoFile,
      subType: [FileSubType.document, FileSubType.newdoc],
      id: '1',
      supportType: UserFeature.SupportNewdoc,
      hidden: false,
    },
    {
      name: fm2('SiderMenu.siderMenuCreateMoDocText'),
      type: FileType.ShimoFile,
      subType: [FileSubType.modoc],
      id: '2',
      supportType: UserFeature.SupportModoc,
      hidden: false,
    },
    {
      name: fm2('SiderMenu.siderMenuCreateTableText'),
      type: FileType.ShimoFile,
      subType: [FileSubType.spreadsheet, FileSubType.sheet, FileSubType.mosheet],
      id: '3',
      supportType: UserFeature.SupportMosheet,
      hidden: false,
    },
    {
      name: fm2('SiderMenu.siderMenuCreatePptText'),
      type: FileType.ShimoFile,
      subType: [FileSubType.slide, FileSubType.presentation],
      id: '4',
      supportType: UserFeature.SupportPresentation,
      hidden: false,
    },
    {
      name: fm2('SiderMenu.siderMenuCreateThink'),
      type: FileType.ShimoFile,
      subType: [FileSubType.mindmap],
      id: '5',
    },
    {
      name: fm2('SiderMenu.siderMenuCreateFormText'),
      type: FileType.ShimoFile,
      subType: [FileSubType.form],
      id: '6',
      supportType: UserFeature.SupportForm,
      hidden: false,
    },
    {
      name: fm2('SiderMenu.siderMenuCreateBlank'),
      type: FileType.ShimoFile,
      subType: [FileSubType.board],
      id: '7',
    },
    {
      name: fm2('SiderMenu.siderMenuCreateCloud'),
      type: FileType.CloudFile,
      subType: [
        FileSubType.img,
        FileSubType.pdf,
        FileSubType.xls,
        FileSubType.docx,
        FileSubType.ppt,
        FileSubType.mp3,
        FileSubType.zip,
        FileSubType.mp4,
        FileSubType.wps,
        FileSubType.xmind,
      ],
      id: '8',
      hasDiverter: true,
    },
    {
      name: fm2('SiderMenu.siderMenuCreateSpace'),
      type: FileType.FolderOrSpace,
      subType: [FileSubType.space],
      id: '9',
    },
    {
      name: fm2('SiderMenu.siderMenuCreateFolderText'),
      type: FileType.FolderOrSpace,
      subType: [FileSubType.folder],
      id: '10',
    },
  ];
}
