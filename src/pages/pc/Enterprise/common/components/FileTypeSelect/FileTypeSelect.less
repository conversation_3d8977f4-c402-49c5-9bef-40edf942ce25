.styledDropdown {
  :global {
    .ant-select-tree .ant-select-tree-switcher {
      display: none;
    }

    .ant-select-tree {
      position: relative;
    }

    .ant-select-tree .ant-select-tree-treenode {
      padding: 6px 12px;

      &:hover {
        background-color: var(--theme-menu-color-bg-hover);
      }

      .ant-select-tree-checkbox {
        margin-right: 8px;
      }

      .ant-select-tree-node-content-wrapper {
        padding: 0;
        background: unset;

        &:hover {
          background-color: unset;
        }
      }
    }

    .ant-select-item-option {
      flex-direction: row-reverse;
    }

    .ant-select-item-group {
      padding: 0 !important;
      min-height: 0 !important;
    }

    .ant-divider-horizontal {
      width: unset;
      min-width: unset;
      margin: 4px 12px;
    }

    .rc-virtual-list-holder {
      max-height: unset !important;

      .ant-select-item-option-selected {
        background-color: var(--theme-menu-color-bg-hover);
      }
    }

    .ant-select-item-option-grouped {
      padding-left: 12px !important;
    }
  }
}

.styledFileTypeTag {
  padding-left: 11px;
}

.styledFileType {
  display: flex;
  align-items: center;

  > div {
    margin-right: 8px;
  }
}

.hasDiverter {
  &::before {
    position: absolute;
    top: -8px;
    left: -32px;
    content: '';
    height: 1px;
    width: 120%;
    background: var(--theme-menu-color-bg-hover);
  }
}
