import { TreeSelect } from 'antd';
import { memo, useMemo } from 'react';

import { fm } from '@/modules/Locale';
import { getFileIcon } from '@/pages/pc/Enterprise/utils';
import { useUserCheckPoint } from '@/service/Me';

import styles from './FileTypeSelect.less';
import { getFileTypeSource } from './FileTypeSource';

interface FileTypeSelectProps {
  value: string[];
  onChange: (value: string[]) => void;
}

export const FileTypeSelect = memo((props: FileTypeSelectProps) => {
  const { isNoFeaturePoint } = useUserCheckPoint();
  const { value, onChange } = props;
  const i18nText = {
    noSelectType: fm('FileTypeSelect.noSelectType'),
    fileType: fm('FileTypeSelect.fileType'),
  };
  const fileTypeSource = getFileTypeSource().map((item) => {
    item.hidden = item.supportType && isNoFeaturePoint(item.supportType || '');
    return item;
  });
  /**
   * 这一套很奇怪的写法 是为了用antd的组件还原设计师给的样式
   */
  const fileTypeGroup = useMemo(() => {
    const fileTypes = fileTypeSource
      .filter((it) => it.hidden !== true)
      .map(({ name, id, type, subType: subTypes, hasDiverter }) => {
        const subType = subTypes[subTypes.length - 1] as any;
        const Icon = getFileIcon(type, subType);
        return {
          key: id,
          title: (
            <div className={`${styles.styledFileType} ${hasDiverter ? styles.hasDiverter : ''}`}>
              {Icon}
              {name}
            </div>
          ),
          value: id,
        };
      });
    return fileTypes;
  }, [fileTypeSource, getFileIcon]);

  return (
    <TreeSelect
      allowClear
      className={'myTreeSelect'}
      dropdownRender={(menu) => <div className={styles.styledDropdown}> {menu}</div>} // 自定义下拉框样式
      getPopupContainer={(trigger) => trigger.parentNode}
      maxTagCount={0} // 显示溢出tag
      placeholder={i18nText.noSelectType}
      style={{ width: '100%' }}
      tagRender={() => <div className={styles.styledFileTypeTag}>{`${value.length} ${i18nText.fileType}`}</div>} // 自定义溢出tag达到期望的效果
      treeCheckable={true}
      treeData={fileTypeGroup}
      value={value}
      virtual={false}
      onChange={onChange}
    />
  );
});
