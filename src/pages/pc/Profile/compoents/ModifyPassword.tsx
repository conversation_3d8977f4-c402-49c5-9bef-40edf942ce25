import '../index.less';

import { Button, Form, Input, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';

import { changePassword } from '@/api/Profile';
import { getSecurityPassword } from '@/modules/encrypt';
import { useFormatMessage as $t } from '@/modules/Locale';
interface ModifyPasswordProps {
  visible: boolean;
  setVisible: (value: boolean) => void;
}
const ModifyPassword: React.FC<ModifyPasswordProps> = ({ visible, setVisible }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isFormValid, setIsFormValid] = useState(false);
  useEffect(() => {
    form.resetFields();
  }, [visible, form]);
  const i18nText: any = {
    changePd: $t('Profile.changePd'),
    forgetPd: $t('Profile.forgetPd'),
    currentPassword: $t('Profile.currentPassword'),
    newPd: $t('Profile.newPassword'),
    confirmNewPassword: $t('Profile.confirmNewPassword'),
    sure: $t('Space.sure'),
    currentPasswordRequired: $t('Profile.currentPasswordRequired'),
    currentPasswordPlaceholder: $t('Profile.currentPasswordPlaceholder'),
    newPasswordPlaceholder: $t('Profile.newPasswordPlaceholder'),
    newPasswordRequired: $t('Profile.newPasswordRequired'),
    newPasswordLength8: $t('Profile.newPasswordLength8'),
    newPasswordRule: $t('Profile.newPasswordRule'),
    confirmNewPasswordPlaceholder: $t('Profile.confirmNewPasswordPlaceholder'),
    confirmNewPasswordRequired: $t('Profile.confirmNewPasswordRequired'),
    confirmNewPasswordMatch: $t('Profile.confirmNewPasswordMatch'),
    changePdSuccess: $t('Profile.changePdSuccess'),
    changePdFailed: $t('Profile.changePdFailed'),
  };
  const handleFinish = (values: any) => {
    setLoading(true);
    const currentPassword = getSecurityPassword(values.currentPassword);
    const password = getSecurityPassword(values.newPassword);
    changePassword({ currentPassword, password })
      .then(() => {
        setLoading(false);
        setVisible(false);
        message.success(i18nText.changePdSuccess);
      })
      .catch(() => {
        setLoading(false);
        message.error(i18nText.changePdFailed);
      });
  };
  const checkFormValidity = () => {
    const values = form.getFieldsValue();
    const currentPassword = values.currentPassword;
    const newPassword = values.newPassword;
    const confirmPassword = values.confirmPassword;

    setIsFormValid(!!currentPassword && !!newPassword && !!confirmPassword);
  };
  return (
    <Modal
      centered
      footer={null}
      open={visible}
      title={i18nText.changePd}
      width={400}
      onCancel={() => setVisible(false)}
    >
      {/* 隐藏入口 */}
      {/* <div className="forgetPassword" onClick={forgetPassword}>
        <div>{i18nText.forgetPd}</div>
      </div> */}
      <Form form={form} layout="vertical" onFieldsChange={() => checkFormValidity()} onFinish={handleFinish}>
        <Form.Item
          className="confirm-password-no-mark"
          label={<div>{i18nText.currentPassword}</div>}
          name="currentPassword"
          rules={[{ required: true, message: i18nText.currentPasswordRequired }]}
        >
          <Input.Password placeholder={i18nText.currentPasswordPlaceholder} />
        </Form.Item>
        <Form.Item
          className="confirm-password-no-mark"
          label={i18nText.newPd}
          name="newPassword"
          rules={[
            { required: true, message: i18nText.newPasswordRequired },
            {
              validator(_, value) {
                if (!value) {
                  return Promise.resolve();
                }
                if (value.length < 8) {
                  return Promise.reject(new Error(i18nText.newPasswordLength8));
                }
                const hasLower = /[a-z]/.test(value);
                const hasUpper = /[A-Z]/.test(value);
                const hasDigit = /\d/.test(value);
                if (!hasLower || !hasUpper || !hasDigit) {
                  return Promise.reject(new Error(i18nText.newPasswordRule));
                }

                return Promise.resolve();
              },
            },
          ]}
        >
          <Input.Password placeholder={i18nText.newPasswordPlaceholder} />
        </Form.Item>
        <Form.Item
          className="confirm-password-no-mark"
          dependencies={['newPassword']}
          label={i18nText.confirmNewPassword}
          name="confirmPassword"
          rules={[
            { required: true, message: i18nText.confirmNewPasswordRequired },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error(i18nText.confirmNewPasswordMatch));
              },
            }),
          ]}
        >
          <Input.Password placeholder={i18nText.confirmNewPasswordPlaceholder} />
        </Form.Item>
        <Form.Item className="submitBtn" style={{ textAlign: 'right' }}>
          <Button
            color="default"
            disabled={!isFormValid}
            htmlType="submit"
            loading={loading}
            size="middle"
            variant="solid"
          >
            {i18nText.sure}
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};
export default ModifyPassword;
