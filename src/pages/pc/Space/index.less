.styledLink {
  color: var(--theme-text-color-default);
  text-decoration: none;
  display: inline-block;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    text-decoration: underline;
    color: var(--theme-text-color-default);
  }
}

.cardTitleFlex {
  display: flex;
  justify-content: space-between;
}

.cardTitleLeft {
  color: var(--theme-text-color-secondary);
  font-size: 12px;
}

.cardTitleRight {
  font-size: 13px;
  font-weight: 400;
  padding-right: 10px;
  cursor: pointer;
  color: var(--theme-text-color-medium);
}

.settingsModal {
  .ant-modal-content {
    .ant-modal-close {
      top: 20px;
      inset-inline-end: 26px;
    }
  }

  .settingTitle {
    margin: 16px 0 8px;
  }

  .line {
    height: 1px;
    background-color: var(--theme-basic-color-lighter);
    margin-bottom: 16px;
  }

  .ant-form-item {
    margin-bottom: 22px;
  }

  .toolTipTitle {
    margin-bottom: 8px;
    display: flex;

    .toolTipTitleLeft {
      margin-right: 5px;
    }
  }

  .cardTitleItem {
    padding-bottom: 4px;
  }

  .ant-btn {
    padding: 0 18px;
  }
}

.sortBtn {
  display: flex;
  align-items: center;

  .filterText {
    font-size: 12px;
  }

  .rotate180 {
    transform: rotate(180deg);
  }
}

.toolTipText {
  color: var(--theme-text-color-white);
  font-size: 13px;
  padding: 6px 12px;
  width: 172px;
  text-align: center;
}

.noData {
  padding: 44px 88px;
}

.tableMore {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
}

.ant-dropdown {
  .ant-dropdown-menu {
    width: 192px;

    .ant-dropdown-menu-title-content {
      font-size: 12px;
    }
  }
}

.spaceNameHeader {
  display: flex;
  align-items: center;

  .spaceNameIcon {
    display: flex;
    align-items: center;
    margin-right: 4px;
  }
}

.ant-btn.ant-btn-icon-only {
  width: 24px;
  height: 24px;
}

.enterCollaborator {
  margin-right: 74px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0 5px;
  color: var(--theme-text-color-guidance);
  font-size: 12px;
  font-weight: 500;
  border-radius: 2px;

  &:hover {
    background: var(--theme-box-shadow-color-level6);
  }

  .svg {
    margin-right: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
