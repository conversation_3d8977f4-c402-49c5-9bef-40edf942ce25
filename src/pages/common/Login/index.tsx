import { Select, Space } from 'antd';
import { useEffect, useState } from 'react';
import { FormattedMessage } from 'umi';

import { ReactComponent as CaretRightOutlined } from '@/assets/images/svg/CaretRightOutlined.svg';
import { ReactComponent as CheckSvg } from '@/assets/images/svg/check.svg';
import { ReactComponent as GlobalOutlined } from '@/assets/images/svg/GlobalOutlined.svg';
import { ReactComponent as ShimoLogo } from '@/assets/images/svg/shimoLogo.svg';
import LoginView from '@/components/Login/LoginView';
import { useHasLogin } from '@/hooks/Authorization';
import { setLocale } from '@/hooks/Locale';
import useWindowWidth from '@/hooks/useWindowWidth';
import { getStoredLocale } from '@/modules/Locale';
import { redirectToDefaultUrl } from '@/modules/Location';

import styles from './index.less';

// 提取屏幕宽度阈值为常量
const MOBILE_DEVICE_BREAKPOINT = 420;

export default function Login() {
  const hasLogin = useHasLogin();
  const windowWidth = useWindowWidth();
  const isMobileDevice = windowWidth < MOBILE_DEVICE_BREAKPOINT;

  const [selectedValue, setSelectedValue] = useState<string>('zh-CN');

  useEffect(() => {
    if (hasLogin) {
      redirectToDefaultUrl('/desktop');
    }
  }, [hasLogin]);

  const options = [
    {
      value: 'zh-CN',
      label: '简体中文',
    },
    {
      value: 'en-US',
      label: 'English',
    },
  ];

  const handleChange = (value: string) => {
    setSelectedValue(value);
    if (value === 'zh-CN') {
      setLocale('zh-CN');
    } else {
      setLocale('en-US');
    }
  };

  const LanguageSelector = () => {
    const currentLocale = getStoredLocale() || 'zh-CN';

    useEffect(() => {
      setSelectedValue(currentLocale === 'en-US' ? 'en-US' : 'zh-CN');
    }, [currentLocale]);

    // antd的官方文档也没有写option的类型
    const optionRender = (option: any) => {
      return (
        <Space size={75}>
          <span className={styles.selectOption}>{option.label}</span>
          {option.value === selectedValue ? <CheckSvg /> : null}
        </Space>
      );
    };

    return (
      <Select
        optionRender={optionRender}
        options={options}
        popupClassName={styles.customSelectDropdown}
        prefix={
          <Space style={{ margin: '5px 5px 0 0' }}>
            <GlobalOutlined />
          </Space>
        }
        style={{ width: 182 }}
        suffixIcon={<CaretRightOutlined />}
        value={selectedValue}
        onChange={handleChange}
      />
    );
  };

  return hasLogin ? null : (
    <div className={styles.loginContainer}>
      <div className={`${styles.loginHeader} ${isMobileDevice ? styles.loginHeaderChange : ''}`}>
        {isMobileDevice ? null : (
          <div className={styles.headerContent}>
            <LanguageSelector />
          </div>
        )}
        <div className={styles.headerContent}>
          <ShimoLogo />
        </div>
      </div>
      <div className={`${styles.loginContent} ${isMobileDevice ? '' : styles.loginContentBorder}`}>
        {isMobileDevice ? null : <div className={styles.loginBgTop} />}
        {isMobileDevice ? null : <div className={styles.loginBgBottom} />}
        <h1 className={styles.loginTitle}>
          <FormattedMessage id="Login.loginTitle" />
        </h1>
        <LoginView onSuccess={() => redirectToDefaultUrl('/desktop')} />
      </div>
      <div className={styles.loginFooter}>{isMobileDevice ? <LanguageSelector /> : null}</div>
    </div>
  );
}
