import { useLocation } from 'umi';

import FileErrorPage from '@/components-mobile/H5FileErrorPage';
import { ErrorCodeMap, type ErrorCodes } from '@/utils/request/ErrorCodeMap';

const isValidErrorCode = (code: number): code is ErrorCodes => {
  return Object.keys(ErrorCodeMap).includes(String(code));
};

export const Error = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const code = parseInt(queryParams.get('code') ?? '', 10);
  const errorCode = isNaN(code) || !isValidErrorCode(code) ? undefined : code;

  return <FileErrorPage errorCode={errorCode} />;
};
