import { Button } from 'antd-mobile';
import { useState } from 'react';
import { useMemo } from 'react';

import { CollaborationShareMobile } from '@/components-mobile/Collaboration/CollaborationShareMobile';
import CreateFileBtn from '@/components-mobile/CreateFileBtn';
import { FileEmpty } from '@/components-mobile/FileEmpty';
import { FileHeader, FileLoading, FileSort, withFileListHOC } from '@/components-mobile/FileLlist';
import { FileCardItem, FileListItem } from '@/components-mobile/FileLlist';
import { FileActionSheetMoreProvider } from '@/contexts/FileActionSheetMore';
import { useDisclosure } from '@/hooks/useDisclosure';
import { useFile } from '@/hooks/useFile';
import { ModeTypeEnum } from '@/model/FileList';
import type { FileItemProps } from '@/pages/pc/Desktop/model';

import style from './index.less';
export default () => {
  const [id, setId] = useState('');
  const { isOpen, open, close, toggle } = useDisclosure(false);

  function handleClick() {
    setId('0l3NVmWVr9C14G3R');
    open();
  }

  function handleVisibleChange() {
    close();
    setId('');
  }

  const { fileList, loading, fileSortProp, fileHeaderProp, clickItem } = useFile();
  const { layout } = fileSortProp.sortData;
  const itemComponent = useMemo(() => {
    return {
      [ModeTypeEnum.list]: FileListItem,
      [ModeTypeEnum.card]: FileCardItem,
    };
  }, []);
  const EnhancedFileList = withFileListHOC<FileItemProps>(itemComponent[layout]);
  return (
    <FileActionSheetMoreProvider>
      <Button onClick={handleClick}>test</Button>
      <CollaborationShareMobile close={handleVisibleChange} guid={id} toggle={toggle} visible={isOpen} />
      <CreateFileBtn />
    </FileActionSheetMoreProvider>
  );
};
