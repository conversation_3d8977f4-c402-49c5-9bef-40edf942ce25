.notification {
  color: var(--theme-basic-color-primary);
  height: 100%;
  display: flex;
  flex-direction: column;

  .notificationList {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    flex-basis: 0;
    overflow-y: scroll;
  }

  :global {
    .adm-list-item {
      padding-left: 0;

      .unread {
        background-color: var(--theme-notice-color-bg);
      }
    }

    .adm-list-item-content {
      padding-right: 0;
    }

    .adm-list-item-content-main {
      padding: 0;
    }

    .adm-list-header {
      background-color: var(--theme-card-default);
      padding-left: 16px;
    }

    .adm-list-body {
      @border-color: var(--theme-separator-color-lighter);

      border-top: 1px solid @border-color;
      border-bottom: 1px solid @border-color;
    }
  }

  .groupedTitle {
    font-size: 12px;
    color: var(--theme-basic-color-primary);
  }
}
