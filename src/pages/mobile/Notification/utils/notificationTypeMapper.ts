import { NotificationType } from '../components/NotificationType/Renderer';

export const msgTypeToNotificationType = (msgType: number, _invitedRole?: number): NotificationType => {
  // 特殊处理协作者邀请类型
  if (msgType === 2) {
    return _invitedRole === 4 ? NotificationType.COLLABORATION_INVITE_ADMIN : NotificationType.COLLABORATION_INVITE;
  }

  const typeMap: Record<number, NotificationType> = {
    0: NotificationType.COMMENT, // 评论
    1: NotificationType.MENTION, // 文档/表格被@
    8: NotificationType.COMMENT_LIKE, // 点赞
    6: NotificationType.DOC_DELETED, // 文档被删除
    7: NotificationType.DOC_DATE_REMINDER, // 日期提醒
    16: NotificationType.SHEET_DATE_REMINDER, // 表格日期提醒
    17: NotificationType.SHEET_CELL_WATCH, // 表格选区提醒
    11: NotificationType.ENTERPRISE_REMOVE, // 将你移出企业
    12: NotificationType.ENTERPRISE_TRANSFER, // 将企业移交给你
    13: NotificationType.ENTERPRISE_NAME_CHANGE, // 修改企业名称
    18: NotificationType.ENTERPRISE_ACCOUNT_NICKNAME_UPDATE, // 修改企业成员账号
    21: NotificationType.APPLY_FOR_PERMISSION, // 申请权限
  };

  return typeMap[msgType] || NotificationType.COMMENT; // 默认返回评论类型
};

// 后端所有的msgType如下, 目前前端只处理prd(通知)列举的msgType
/**
 *
  NotificationType_COMMENT                     NotificationType = 0  // 评论、讨论
  NotificationType_MENTION                     NotificationType = 1  // 文件内容里at人
  NotificationType_INVITATION                  NotificationType = 2  // 添加你为协作者
  NotificationType_TEAM_SET_ADMIN              NotificationType = 3  // 将你设置为企业管理员
  NotificationType_TEAM_INVITATION             NotificationType = 4  // 邀请你加入企业
  NotificationType_TEAM_NEW_MEMBER             NotificationType = 5  // 新成员加入
  NotificationType_SHARE_DESTROYED             NotificationType = 6  // 删除文档 | 取消共享
  NotificationType_TODO                        NotificationType = 7  // 待办事项通知
  NotificationType_LIKE                        NotificationType = 8  // 点赞通知
  NotificationType_TODO_MODIFY                 NotificationType = 9  // 待办事项修改通知
  NotificationType_COMMENT_MENTION             NotificationType = 10 // 评论讨论里at人
  NotificationType_MOVE_OUT_TEAM_MEMBER        NotificationType = 11 // 将你移出企业
  NotificationType_TEAM_TRANSFER               NotificationType = 12 // 将企业移交给你
  NotificationType_TEAM_NAME_MODIFIED          NotificationType = 13 // 修改了企业名称“${name}”
  NotificationType_TEAM_INVITATION_LINK_OPENED NotificationType = 14 // 打开企业邀请链接
  NotificationType_TEAM_INVITATION_LINK_CLOSED NotificationType = 15 // 关闭了企业邀请链接
  NotificationType_TODO_REMIND                 NotificationType = 16 // 任务提醒
  NotificationType_RANGE_REMIND                NotificationType = 17 // 表格选区提醒
  NotificationType_CHANGE_MEMBER_INFO          NotificationType = 18 // 修改企业成员账号（邮箱，昵称，密码）
  NotificationType_SYSTEM                      NotificationType = 20 // 系统通知
  NotificationType_ROLE_APPLY                  NotificationType = 21 //权限申请
  NotificationType_FILE_RECOVER                NotificationType = 22 // 文件被恢复通知 ${昵称}恢复了${文件名称}
  NotificationType_FILE_CONTENT_UPDATED        NotificationType = 23 // 用户1、用户2、用户3 等7人更新了文档「文档名称」
  NotificationType_FORM                        NotificationType = 24 // 表单通知
  NotificationType_QUOTA_ALERT_NOTIFICATION    NotificationType = 25 // 配额报警通知
  NotificationType_MEMBER_NOTIFY               NotificationType = 26 // 会员提醒，例如会员到期续费提醒、文件协作达到上限升级账号提醒
  NotificationType_FILE_ADD_UPDATE_EDIT_NOTIFY NotificationType = 27 // 文件的新增、修改、删除通知
 */
