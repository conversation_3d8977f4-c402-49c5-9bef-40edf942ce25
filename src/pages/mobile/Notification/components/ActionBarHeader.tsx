import classNames from 'classnames';

import { fm } from '@/modules/Locale';

import styles from './ActionBarHeader.less';

export interface ActionBarHeaderProps {
  unreadCount: number;
  onReadAll: () => void;
  type: 'all' | 'unread';
  onTypeChange: (type: 'all' | 'unread') => void;
}

const formatUnreadCount = (count: number): string => {
  if (count <= 0) return '';
  return count > 99 ? '(99+)' : `(${count.toString()})`;
};

const ActionBarHeader: React.FC<ActionBarHeaderProps> = ({ unreadCount, onReadAll, type, onTypeChange }) => {
  const i18n_all = fm('Notification.all');
  const i18n_unread = fm('Notification.unread');
  const i18n_readAll = fm('Notification.readAll');

  return (
    <>
      <div
        className={classNames(styles.title, { [styles.unactive]: type !== 'all' })}
        onClick={() => onTypeChange('all')}
      >
        {i18n_all}
      </div>
      <div
        className={classNames(styles.title, { [styles.unactive]: type !== 'unread' })}
        onClick={() => onTypeChange('unread')}
      >
        {i18n_unread} {formatUnreadCount(unreadCount)}
      </div>
      {unreadCount > 0 && (
        <div className={styles.readAll} onClick={onReadAll}>
          {i18n_readAll}
        </div>
      )}
    </>
  );
};

export default ActionBarHeader;
