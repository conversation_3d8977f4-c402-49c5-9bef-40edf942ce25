import type {
  ApplyForPermissionProps,
  CollaborationInviteAdminProps,
  CollaborationInviteProps,
  CommentLikeProps,
  CommentProps,
  DocDateReminderProps,
  DocStatusProps,
  EnterpriseAccountUpdateProps,
  EnterpriseAdminChangeProps,
  EnterpriseNameChangeProps,
  MentionProps,
  SheetCellWatchProps,
  SheetDateReminderProps,
} from './components';
import {
  ApplyForPermission,
  CollaborationInvite,
  CollaborationInviteAdmin,
  Comment,
  CommentLike,
  DocDateReminder,
  DocStatus,
  EnterpriseAccountUpdate,
  EnterpriseAdminChange,
  EnterpriseNameChange,
  Mention,
  SheetCellWatch,
  SheetDateReminder,
} from './components';

// 所有可能的通知类型的联合类型
export type CommonNotificationProps =
  | CollaborationInviteAdminProps
  | CollaborationInviteProps
  | CommentLikeProps
  | CommentProps
  | MentionProps
  | DocDateReminderProps
  | DocStatusProps
  | EnterpriseAdminChangeProps
  | EnterpriseNameChangeProps
  | EnterpriseAccountUpdateProps
  | SheetDateReminderProps
  | SheetCellWatchProps
  | ApplyForPermissionProps;

export enum NotificationType {
  // 基础交互通知
  COMMENT = 'COMMENT', // 评论
  MENTION = 'MENTION', // 文档/表格被@
  COLLABORATION_INVITE = 'COLLABORATION_INVITE', // 被添加协作者
  COLLABORATION_INVITE_ADMIN = 'COLLABORATION_INVITE_ADMIN', // 被添加为管理者

  // 文档相关通知
  DOC_DATE_REMINDER = 'DOC_DATE_REMINDER', // 新文档日期提醒
  DOC_MODIFIED = 'DOC_MODIFIED', // 文档被修改
  DOC_DELETED = 'DOC_DELETED', // 文档被删除
  COMMENT_LIKE = 'COMMENT_LIKE', // 新文档评论点赞通知

  // 表格相关通知
  SHEET_CELL_WATCH = 'SHEET_CELL_WATCH', // 表格关注选区
  SHEET_DATE_REMINDER = 'SHEET_DATE_REMINDER', // 表格日期（自动）提醒

  // 企业设置通知
  ENTERPRISE_ADMIN_CHANGE = 'ENTERPRISE_ADMIN_CHANGE', // 将你设置为企业管理员
  ENTERPRISE_REMOVE = 'ENTERPRISE_REMOVE', // 将你移出企业
  ENTERPRISE_TRANSFER = 'ENTERPRISE_TRANSFER', // 将企业移交给你
  ENTERPRISE_NAME_CHANGE = 'ENTERPRISE_NAME_CHANGE', // 修改企业名称
  ENTERPRISE_INVITE_LINK = 'ENTERPRISE_INVITE_LINK', // 打开/关闭邀请链接

  // 账号设置通知
  ENTERPRISE_ACCOUNT_NICKNAME_UPDATE = 'ENTERPRISE_ACCOUNT_NICKNAME_UPDATE', // 修改昵称
  ENTERPRISE_ACCOUNT_BIND_EMAIL_CREATE = 'ENTERPRISE_ACCOUNT_BIND_EMAIL_CREATE', // 新增绑定邮箱
  ENTERPRISE_ACCOUNT_BIND_EMAIL_UPDATE = 'ENTERPRISE_ACCOUNT_BIND_EMAIL_UPDATE', // 修改绑定邮箱
  ENTERPRISE_ACCOUNT_PASSWORD_RESET = 'ENTERPRISE_ACCOUNT_PASSWORD_RESET', // 重置密码

  // 申请权限
  APPLY_FOR_PERMISSION = 'APPLY_FOR_PERMISSION', // 申请权限
}

type NotificationTypeToProps = Record<NotificationType, unknown> & {
  [NotificationType.COMMENT]: CommentProps;
  [NotificationType.MENTION]: MentionProps;
  [NotificationType.COLLABORATION_INVITE]: CollaborationInviteProps;
  [NotificationType.COLLABORATION_INVITE_ADMIN]: CollaborationInviteAdminProps;
  [NotificationType.DOC_DATE_REMINDER]: DocDateReminderProps;
  [NotificationType.SHEET_CELL_WATCH]: SheetCellWatchProps;
  [NotificationType.SHEET_DATE_REMINDER]: SheetDateReminderProps;
  [NotificationType.COMMENT_LIKE]: CommentLikeProps;
  [NotificationType.DOC_MODIFIED]: DocStatusProps;
  [NotificationType.DOC_DELETED]: DocStatusProps;
  [NotificationType.ENTERPRISE_ADMIN_CHANGE]: EnterpriseAdminChangeProps;
  [NotificationType.ENTERPRISE_NAME_CHANGE]: EnterpriseNameChangeProps;
  [NotificationType.ENTERPRISE_ACCOUNT_NICKNAME_UPDATE]: EnterpriseAccountUpdateProps;
  [NotificationType.ENTERPRISE_ACCOUNT_BIND_EMAIL_CREATE]: EnterpriseAccountUpdateProps;
  [NotificationType.ENTERPRISE_ACCOUNT_BIND_EMAIL_UPDATE]: EnterpriseAccountUpdateProps;
  [NotificationType.ENTERPRISE_ACCOUNT_PASSWORD_RESET]: EnterpriseAccountUpdateProps;
};

type Props<T extends NotificationType> = {
  type: T;
  innerProps: NotificationTypeToProps[T];
};

const Renderer = <T extends keyof NotificationTypeToProps>({ type, innerProps }: Props<T>) => {
  switch (type) {
    case NotificationType.COMMENT:
      return <Comment {...(innerProps as CommentProps)} />;
    case NotificationType.MENTION:
      return <Mention {...(innerProps as MentionProps)} />;
    case NotificationType.COLLABORATION_INVITE:
      return <CollaborationInvite {...(innerProps as CollaborationInviteProps)} />;
    case NotificationType.COLLABORATION_INVITE_ADMIN:
      return <CollaborationInviteAdmin {...(innerProps as CollaborationInviteAdminProps)} />;
    case NotificationType.DOC_DATE_REMINDER:
      return <DocDateReminder {...(innerProps as DocDateReminderProps)} />;
    case NotificationType.SHEET_CELL_WATCH:
      return <SheetCellWatch {...(innerProps as SheetCellWatchProps)} />;
    case NotificationType.SHEET_DATE_REMINDER:
      return <SheetDateReminder {...(innerProps as SheetDateReminderProps)} />;
    case NotificationType.COMMENT_LIKE:
      return <CommentLike {...(innerProps as CommentLikeProps)} />;
    case NotificationType.DOC_MODIFIED:
    case NotificationType.DOC_DELETED:
      return <DocStatus {...(innerProps as DocStatusProps)} />;
    case NotificationType.ENTERPRISE_ADMIN_CHANGE:
      return <EnterpriseAdminChange {...(innerProps as EnterpriseAdminChangeProps)} />;
    case NotificationType.ENTERPRISE_NAME_CHANGE:
      return <EnterpriseNameChange {...(innerProps as EnterpriseNameChangeProps)} />;
    case NotificationType.ENTERPRISE_ACCOUNT_NICKNAME_UPDATE:
    case NotificationType.ENTERPRISE_ACCOUNT_BIND_EMAIL_CREATE:
    case NotificationType.ENTERPRISE_ACCOUNT_BIND_EMAIL_UPDATE:
    case NotificationType.ENTERPRISE_ACCOUNT_PASSWORD_RESET:
      return <EnterpriseAccountUpdate {...(innerProps as EnterpriseAccountUpdateProps)} />;
    case NotificationType.APPLY_FOR_PERMISSION:
      return <ApplyForPermission {...(innerProps as ApplyForPermissionProps)} />;

    default:
      return <div>Unknown Notification type</div>;
  }
};

export default Renderer;
