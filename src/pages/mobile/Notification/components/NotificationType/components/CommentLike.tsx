import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  userName: string;
  fileName: string;
  numOfLikes: number;
  onClickFileName?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title'> & Temp;

export default ({ userName, fileName, numOfLikes, content, time, onClickFileName }: Props) => {
  const i18n_many = fm('Notification.many');
  const i18n_people = fm('Notification.people');
  const i18n_likes = fm('Notification.likes');
  const i18n_ofComment = fm('Notification.ofComment');

  const _title = (
    <>
      {userName}
      {i18n_many}
      {numOfLikes}
      {i18n_people}
      {i18n_likes}
    </>
  );
  const content_ = (
    <>
      <div style={{ marginBottom: 10 }}>
        <span className={styles.bold} onClick={onClickFileName}>
          「{fileName}」
        </span>
        {i18n_ofComment}
      </div>
      {content}
    </>
  );

  return <BaseLayout content={content_} time={time} title={_title} />;
};
