import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';

interface Temp {
  userName: string;
}
export type Props = Omit<BaseLayoutProps, 'title'> & Temp;

export default ({ userName, content, time }: Props) => {
  const i18n_updatedWatchArea = fm('Notification.updatedWatchArea');

  const _title = (
    <>
      {userName}
      {i18n_updatedWatchArea}
    </>
  );

  return <BaseLayout content={content} time={time} title={_title} />;
};
