import notifyIcon from '@/assets/images/svg/notify.svg';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  fileName: string;
  onClickFileName?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content' | 'icon'> & Temp;

export default ({ fileName, time, onClickFileName }: Props) => {
  const i18n_inYou = fm('Notification.inYou');
  const i18n_setReminderHasExpired = fm('Notification.setReminderHasExpired');

  const _title = (
    <>
      {i18n_inYou}
      <span className={styles.bold} onClick={onClickFileName}>
        「{fileName}」
      </span>
      {i18n_setReminderHasExpired}
    </>
  );

  return <BaseLayout icon={notifyIcon} time={time} title={_title} />;
};
