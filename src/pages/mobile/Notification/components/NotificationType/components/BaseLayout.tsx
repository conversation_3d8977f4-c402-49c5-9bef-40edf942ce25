import styles from './BaseLayout.less';

export interface BaseLayoutProps {
  icon?: string;
  title: string | React.ReactNode;
  content?: string | React.ReactNode;
  time: string;
  renderFooter?: () => React.ReactNode;
}

export default ({ icon, title, content, time, renderFooter }: BaseLayoutProps) => {
  return (
    <div className={styles.baseLayout}>
      <div className={styles.iconLayout}>{icon && <img className={styles.avatarIcon} src={icon} />}</div>
      <div className={styles.rightLayout}>
        <div className={styles.title}>{title}</div>
        {content && <div className={styles.content}>{content}</div>}
        <div className={styles.time}>{time}</div>
        {renderFooter && <div className={styles.footerLayout}>{renderFooter()}</div>}
      </div>
    </div>
  );
};
