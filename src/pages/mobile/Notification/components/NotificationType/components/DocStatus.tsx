import notifyIcon from '@/assets/images/svg/notify.svg';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  userName: string;
  fileName: string;
  status: 'modify' | 'delete';
  onClickFileName?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content' | 'icon'> & Temp;

export default ({ userName, fileName, status, time, onClickFileName }: Props) => {
  const i18n_modify = fm('Notification.modify');
  const i18n_delete = fm('Notification.delete');
  const i18n_youIn = fm('Notification.youIn');
  const i18n_dateReminder = fm('Notification.dateReminder');

  const _title = (
    <>
      {userName}
      {status === 'modify' ? i18n_modify : i18n_delete}
      {i18n_youIn}
      <span className={styles.bold} onClick={onClickFileName}>
        「{fileName}」
      </span>
      {i18n_dateReminder}
    </>
  );

  return <BaseLayout icon={notifyIcon} time={time} title={_title} />;
};
