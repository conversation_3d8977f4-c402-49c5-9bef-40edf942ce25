import enterpriseAvatar from '@/assets/images/svg/enterpriseAvatar.svg';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';

interface Temp {
  userName: string;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content' | 'icon'> & Temp;

export default ({ userName, time }: Props) => {
  const i18n_setYouAsEnterpriseAdmin = fm('Notification.setYouAsEnterpriseAdmin');
  const _title = (
    <>
      {userName}
      {i18n_setYouAsEnterpriseAdmin}
    </>
  );

  return <BaseLayout icon={enterpriseAvatar} time={time} title={_title} />;
};
