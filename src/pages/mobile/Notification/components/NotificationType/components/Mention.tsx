import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  userName: string;
  fileName: string;
  onClickFileName?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title'> & Temp;

export default ({ userName, fileName, time, icon, onClickFileName }: Props) => {
  const i18n_mentionYou = fm('Notification.mentionYou');

  const _title = (
    <>
      {userName}
      <span className={styles.light}>{i18n_mentionYou}</span>
    </>
  );
  const content_ = (
    <span className={styles.bold} onClick={onClickFileName}>
      「{fileName}」
    </span>
  );

  return <BaseLayout content={content_} icon={icon} time={time} title={_title} />;
};
