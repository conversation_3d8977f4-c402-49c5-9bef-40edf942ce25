.baseLayout {
  padding: 16px 24px;
  display: flex;
  flex-direction: row;

  .iconLayout {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    .avatarIcon {
      @size: 32px;

      width: @size;
      height: @size;
      border-radius: 50%;
      margin-right: 12px;
    }

    .icon {
      width: 30px;
      height: auto;
      object-fit: contain;
      margin-right: 12px;
    }
  }

  .rightLayout {
    flex-grow: 1;
    font-size: 12px;
    font-weight: 400;
    color: var(--theme-text-color-deep);

    .bold {
      font-weight: 500;
    }

    .light {
      color: var(--theme-text-color-secondary); // 浅一点
    }

    .title {
      margin-bottom: 10px;
    }

    .content {
      margin-bottom: 10px;
    }

    .time {
      .light();
    }
  }

  .footerLayout {
    margin-top: 10px;
    margin-bottom: 2px;
    display: flex;
    flex-direction: row;
    align-items: center;

    .confirmBtn {
      background-color: var(--theme-text-color-deep);
      color: var(--theme-text-color-white);
      height: 28px;
      padding: 2px 14px;
      border-radius: 4px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      margin-right: 10px;

      &:active {
        background-color: var(--theme-drive-button-color-hover);
      }
    }

    .processedBtn {
      color: var(--theme-basic-color-black);
    }
  }
}
