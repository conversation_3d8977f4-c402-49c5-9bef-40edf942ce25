import type { RoleApply } from '@/api/Message';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

type RoleApplyTyp = Omit<RoleApply, 'id'>;
interface Temp {
  userName: string;
  fileName: string;
  icon: string;
  onClickFileName?: () => void;
  roleApply?: RoleApplyTyp;
  onClickAllow?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content'> & Temp;

export default ({ userName, fileName, time, icon, onClickFileName, roleApply, onClickAllow }: Props) => {
  const i18n_readonly = fm('Notification.readonly');
  const i18n_comment = fm('Notification.comment');
  const i18n_edit = fm('Notification.edit');
  const i18n_apply = fm('Notification.apply');
  const i18n_allow = fm('Notification.allow');
  const i18n_processed = fm('Notification.processed');
  const i18n_processedByAnother = fm('Notification.processedByAnother');
  const i18n_noModifyRolePermission = fm('Notification.noModifyRolePermission');
  const i18n_fileNotFound = fm('Notification.fileNotFound');
  const i18n_userNotAdmin = fm('Notification.userNotAdmin');

  const roleApplyToPermissionType = (role: RoleApplyTyp['role']) => {
    switch (role) {
      case 'reader':
        return i18n_readonly;
      case 'commentator':
        return i18n_comment;
      case 'editor':
        return i18n_edit;
      default:
        return 'unknow';
    }
  };

  const _title = (
    <>
      {userName}
      {i18n_apply}
      <span className={styles.bold} onClick={onClickFileName}>
        「{fileName}」
      </span>
      {roleApply && roleApplyToPermissionType(roleApply.role)}
    </>
  );
  const content_ = `“${roleApply?.comment}”`;
  const renderFooter = () => (
    <>
      {roleApply?.status === '' && (
        <div className={styles.confirmBtn} onClick={onClickAllow}>
          {i18n_allow}
        </div>
      )}
      {roleApply?.status === 'ROLE_APPLY_IS_APPROVED' && <div className={styles.processedBtn}>{i18n_processed}</div>}
      {roleApply?.status === 'ROLE_APPLY_IS_APPROVED_BY_ANOTHER' && (
        <div className={styles.processedBtn}>{i18n_processedByAnother}</div>
      )}
      {roleApply?.status === 'NO_MODIFY_ROLE_PERMISSION' && (
        <div className={styles.processedBtn}>{i18n_noModifyRolePermission}</div>
      )}
      {roleApply?.status === 'FILE_NOT_FOUND' && <div className={styles.processedBtn}>{i18n_fileNotFound}</div>}
      {roleApply?.status === 'USER_NOT_ADMIN' && <div className={styles.processedBtn}>{i18n_userNotAdmin}</div>}
    </>
  );

  return <BaseLayout content={content_} icon={icon} renderFooter={renderFooter} time={time} title={_title} />;
};
