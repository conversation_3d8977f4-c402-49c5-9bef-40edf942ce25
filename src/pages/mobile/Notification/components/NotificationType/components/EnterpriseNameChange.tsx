import enterpriseAvatar from '@/assets/images/svg/enterpriseAvatar.svg';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';

interface Temp {
  userName: string;
  enterpriseName: string;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content' | 'icon'> & Temp;

export default ({ userName, enterpriseName, time }: Props) => {
  const i18n_modifiedEnterpriseName = fm('Notification.modifiedEnterpriseName');
  const _title = (
    <>
      {userName}
      {i18n_modifiedEnterpriseName}“{enterpriseName}”
    </>
  );

  return <BaseLayout icon={enterpriseAvatar} time={time} title={_title} />;
};
