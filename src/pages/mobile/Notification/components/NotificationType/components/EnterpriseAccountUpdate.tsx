import enterpriseAvatar from '@/assets/images/svg/enterpriseAvatar.svg';
import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  userName: string;
  updatedNickName?: string;
  createdBindEmail?: string;
  updatedBindEmail?: string;
  resetPwd?: boolean;
}
export type Props = Omit<BaseLayoutProps, 'title' | 'content' | 'icon'> & Temp;

export default ({ userName, updatedNickName, time, createdBindEmail, updatedBindEmail, resetPwd }: Props) => {
  const i18n_modifiedYourAccountInfo = fm('Notification.modifiedYourAccountInfo');
  const i18n_modifiedNickName = fm('Notification.modifiedNickName');
  const i18n_bindThisEmail = fm('Notification.bindThisEmail');
  const i18n_modifiedBindEmail = fm('Notification.modifiedBindEmail');
  const i18n_resetPassword = fm('Notification.resetPassword');

  const _title = (
    <>
      {userName}
      {i18n_modifiedYourAccountInfo}
    </>
  );
  const _content = updatedNickName ? (
    <span>
      {i18n_modifiedNickName}
      <span className={styles.bold}>【{updatedNickName}】</span>
    </span>
  ) : createdBindEmail ? (
    <span>
      {i18n_bindThisEmail}【{createdBindEmail}】
    </span>
  ) : updatedBindEmail ? (
    <span>
      {i18n_modifiedBindEmail}【{updatedBindEmail}】
    </span>
  ) : resetPwd ? (
    <span>{i18n_resetPassword}</span>
  ) : (
    // eslint-disable-next-line react/jsx-no-useless-fragment
    <></>
  );

  return <BaseLayout content={_content} icon={enterpriseAvatar} time={time} title={_title} />;
};
