import { fm } from '@/modules/Locale';

import BaseLayout, { type BaseLayoutProps } from './BaseLayout';
import styles from './BaseLayout.less';

interface Temp {
  userName: string;
  fileName: string;
  onClickFileName?: () => void;
}
export type Props = Omit<BaseLayoutProps, 'title'> & Temp;

export default ({ userName, fileName, content, time, icon, onClickFileName }: Props) => {
  const i18n_hasComment = fm('Notification.hasComment');

  const _title = (
    <>
      {userName}
      <span className={styles.light}>{i18n_hasComment}</span>
      <span className={styles.bold} onClick={onClickFileName}>
        「{fileName}」
      </span>
    </>
  );

  return <BaseLayout content={content} icon={icon} time={time} title={_title} />;
};
