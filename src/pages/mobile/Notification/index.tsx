// import classNames from 'classnames';
import { InfiniteScroll, List } from 'antd-mobile';
import classNames from 'classnames';
import React, { useEffect, useMemo, useState } from 'react';

import { requestApplyPermission } from '@/api/applyPermission';
import type { RoleApply } from '@/api/Message';
import { getNotifications, type IParams, readAll, readAlon } from '@/api/Message';
import ActionBar from '@/components/Mobile/ActionBar';
import { useGroupNotifications } from '@/hooks/useGroupNotifications';

import type { ActionBarHeaderProps } from './components/ActionBarHeader';
import ActionBarHeader from './components/ActionBarHeader';
import Empty from './components/Empty';
import { Notification } from './components/NotificationType';
import type { CommonNotificationProps } from './components/NotificationType/Renderer';
import styles from './index.less';
import { msgTypeToNotificationType } from './utils/notificationTypeMapper';

type GetListTyp = NonNullable<IParams['status']>;
interface ListDataItem {
  id: string;
  msg: string;
  msgType: number;
  fileGuid?: string;
  fileName: string;
  invitedRole?: number;
  createdAt: string;
  userName: string;
  userAvatar: string;
  isRead: boolean;
  roleApply?: RoleApply;
}
interface GroupedListDataItem {
  createdAt: string;
  children: ListDataItem[];
}

const OFFSET = 10;

export default () => {
  const [type, setType] = useState<ActionBarHeaderProps['type']>('all');
  const [getListType] = useState<GetListTyp>('all');
  const [listData, setListData] = useState<ListDataItem[]>([]);
  const { groupNotifications, formatTime } = useGroupNotifications();
  const [hasMore, setHasMore] = useState(true);
  const [limit, setLimit] = useState<number>(OFFSET);
  const notificationNum = useMemo<number>(() => {
    return listData.length;
  }, [listData]);
  const unreadListData = useMemo<ListDataItem[]>(() => {
    return listData.filter((item) => !item.isRead);
  }, [listData]);
  const unreadNum = useMemo<number>(() => {
    return unreadListData.length;
  }, [unreadListData]);
  const groupedUnreadList = useMemo<GroupedListDataItem[]>(() => {
    return groupNotifications(unreadListData, (item) => item.createdAt);
  }, [unreadListData, groupNotifications]);
  const groupedListData = useMemo<GroupedListDataItem[]>(() => {
    return groupNotifications(listData, (item) => item.createdAt);
  }, [listData, groupNotifications]);

  const init = async (newLimit?: number) => {
    const _newLimit = newLimit || limit;
    const res = await getNotifications({ status: getListType, limit: _newLimit });
    if (res.status !== 200) return;
    const listData_raw = res.data;

    const listData = listData_raw.map((item) => ({
      id: item.id,
      msg: item.notification.msg,
      msgType: item.notification.msgType,
      fileGuid: item.notification.fileGuid,
      fileName: item.notification.fileName,
      invitedRole: item.notification.invitedRole,
      createdAt: item.createdAt,
      userName: item.notification.user.name,
      userAvatar: item.notification.user.avatar,
      isRead: item.isRead,
      roleApply: item.notification.roleApply,
    })) satisfies ListDataItem[];

    setListData(listData);
  };

  useEffect(() => {
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleReadAll = async () => {
    const res = await readAll();
    if (res.status !== 204) return;

    init();
  };

  const handleTypeChange = (type: ActionBarHeaderProps['type']) => {
    setType(type);
  };

  const renderNotification = (item: ListDataItem) => {
    const notificationType = msgTypeToNotificationType(item.msgType, item.invitedRole);
    const baseProps = {
      time: formatTime(item.createdAt) || '',
      userName: item.userName,
      fileName: item.fileName,
      content: item.msg,
      icon: item.userAvatar,
      onClickFileName: () => {
        window.open(`/${item.fileGuid}`, '_self');
      },
      roleApply: item.roleApply,
      onClickAllow: async () => {
        if (!item.roleApply) return;
        try {
          await requestApplyPermission(item.roleApply.id);
        } catch (error) {
        } finally {
          init();
        }
      },
    } satisfies CommonNotificationProps;

    const handleClick = async (item: ListDataItem) => {
      if (item.isRead) return;
      const res = await readAlon(item.id);
      if (res.status !== 204) return;
      init();
    };

    return (
      <div
        key={item.id}
        className={classNames({
          unread: !item.isRead,
        })}
        onClick={() => handleClick(item)}
      >
        <Notification innerProps={baseProps} type={notificationType} />
      </div>
    );
  };

  const loadMore = async () => {
    const newLimit = limit + OFFSET;
    setLimit(newLimit);
    await init(newLimit);

    setHasMore(notificationNum === newLimit);
  };

  const allListUI =
    notificationNum === 0 ? (
      <Empty />
    ) : (
      <>
        {groupedListData.map((item) => {
          return (
            <List
              key={item.createdAt}
              header={<span className={styles.groupedTitle}>{formatTime(item.createdAt, 'message')}</span>}
            >
              <List.Item key={item.createdAt}>{item.children.map(renderNotification)}</List.Item>
            </List>
          );
        })}
        <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
      </>
    );

  const unreadListUI =
    unreadNum === 0 ? (
      <Empty />
    ) : (
      groupedUnreadList.map((item) => {
        return (
          <List
            key={item.createdAt}
            header={<span className={styles.groupedTitle}>{formatTime(item.createdAt, 'message')}</span>}
          >
            <List.Item key={item.createdAt}>{item.children.map(renderNotification)}</List.Item>
          </List>
        );
      })
    );

  return (
    <div className={styles.notification}>
      <ActionBar
        slots={
          <ActionBarHeader
            type={type}
            unreadCount={unreadNum}
            onReadAll={handleReadAll}
            onTypeChange={handleTypeChange}
          />
        }
      />

      <div className={styles.notificationList}>{type === 'all' ? allListUI : unreadListUI}</div>
    </div>
  );
};
