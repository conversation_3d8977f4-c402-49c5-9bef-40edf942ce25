import { createStore, useStore } from 'zustand';

import { type File, RootType } from '@/model/File';

const InitialFile: File = {
  guid: '',
  name: '',
  type: '',
};

const rootType: RootType = RootType.Folder;

interface FileState {
  file: File;
  files: File[];
  rootType: RootType;
}

interface FileAction {
  setFile: (file: File) => void;
  setFiles: (files: File[]) => void;
  setRootType: (rootType: RootType) => void;
}

type FileStore = FileState & FileAction;

export const fileStore = createStore<FileStore>((set) => ({
  file: InitialFile,
  files: [],
  rootType,
  setFile: (file: File) => set(() => ({ file })),
  setFiles: (files: File[]) => set(() => ({ files })),
  setRootType: (rootType: RootType) => set(() => ({ rootType })),
}));

export const useFileStore = <U>(selector: (state: FileStore) => U) => {
  return useStore(fileStore, selector);
};
