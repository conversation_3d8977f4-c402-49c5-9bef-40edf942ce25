import { createStore, useStore } from 'zustand';

import type { Me } from '@/model/Me';
import type { FeatureType, Quotas } from '@/model/UserPermission';

const InitialMe: Me = {
  id: null,
  name: '',
  name_pinyin: '',
  email: '',
  avatar: '',
  status: 0,
  team: {
    quitAction: '',
    scaleNum: 0,
    id: 0,
    city: '',
    mobile: '',
    name: '',
    province: '',
    quit_action: '',
    scale: '',
    type: '',
    deletedAt: '',
    updatedAt: '',
    createdAt: '',
    scale_num: 0,
    info: '',
    dissolved: false,
    isMerged: false,
  },
  teamRole: '',
  teamTime: '',
  team_time: '',
  team_role: '',
  isSeat: 1,
  is_seat: 1,
  createdAt: '',
  merged_into: null,
  mergedInto: null,
  teamId: 0,
  team_id: 0,
  mobile: '',
  mobileAccount: '',
  hasPassword: false,
  membership: {
    accountTypeExpiredAt: '',
    accountType: '',
    accountTypeCreatedAt: '',
    isEnterprisePremium: false,
    isExpired: false,
    isNewDing: false,
    isOfficial: false,
    editionId: 1,
  },
  accountMetadata: {
    isExpired: false,
    isDingtalk: false,
    isWework: false,
    isEnterprise: false,
    isFreeEnterprise: false,
    expiredAt: {
      seconds: 0,
      nanos: 0,
    },
    isTrial: false,
    isPersonalPremium: false,
    isEnterprisePremium: false,
    isEnterpriseLight: false,
    editionId: 0,
  },
  editionName: '',
  requiresIdentityVerification: false,
};

const InitialFeatures: FeatureType = [];

const InitialQuotas = {};

interface MeState {
  me: Me;
  features: FeatureType;
  quotas: Quotas;
}

interface MeAction {
  setMe: (me: Me) => void;
  setFeatures: (features: FeatureType) => void;
  setQuotas: (quotas: Quotas) => void;
}

type MeStore = MeState & MeAction;

export const meStore = createStore<MeStore>((set) => ({
  me: InitialMe,
  features: InitialFeatures,
  quotas: InitialQuotas,
  setMe: (me: Me) => set(() => ({ me })),
  setFeatures: (features: FeatureType) => set(() => ({ features })),
  setQuotas: (quotas: Quotas) => set(() => ({ quotas })),
}));

export const useMeStore = <U>(selector: (state: MeStore) => U) => {
  return useStore(meStore, selector);
};
