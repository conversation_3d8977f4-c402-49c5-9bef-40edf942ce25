import { createStore, useStore } from 'zustand';

interface PermissionsInfo {
  value: boolean;
  reason: string;
}

//待增加
interface PermissionsAndReasons {
  canCreateChildFile: PermissionsInfo;
}

//待增加
interface FolderSpaceInfo {
  guid: string;
  id: string;
  permissionsAndReasons: PermissionsAndReasons;
}

const initialFile: FolderSpaceInfo = {
  guid: '',
  id: '',
  permissionsAndReasons: {
    canCreateChildFile: {
      value: false,
      reason: '',
    },
  },
};

interface FileInfoState {
  folderSpaceInfo: FolderSpaceInfo;
}

interface FileAction {
  //存储当前文件详情
  setCurrentFolderSpaceInfo: (fileInfo: FolderSpaceInfo) => void;
}

type FileStore = FileInfoState & FileAction;

export const fileInfoStore = createStore<FileStore>((set) => ({
  folderSpaceInfo: initialFile,
  setCurrentFolderSpaceInfo: (folderSpaceInfo: FolderSpaceInfo) => set(() => ({ folderSpaceInfo })),
}));

export const useCurrentFolderSpaceInfo = <U>(selector: (state: FileStore) => U) => {
  return useStore(fileInfoStore, selector);
};
