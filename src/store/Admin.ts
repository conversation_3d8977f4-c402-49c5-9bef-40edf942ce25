import { useStore } from 'zustand';
import { createWithEqualityFn } from 'zustand/traditional';
import { shallow } from 'zustand/vanilla/shallow';

import type { AdminModeState, AuthMode } from '@/model/ManagementLayout';

export const AdminStore = createWithEqualityFn<AdminModeState>()(
  (set) => ({
    authSuccess: false,
    authMode: undefined,
    adminPasswordIsSet: false,
    setAuthSuccess: (authSuccess: boolean) => set(() => ({ authSuccess })),
    setAuthMode: (authMode: AuthMode) => set(() => ({ authMode })),
    setAdminPasswordIsSet: (adminPasswordIsSet: boolean) => set(() => ({ adminPasswordIsSet })),
  }),
  shallow,
);

export const useAdminStore = <U>(selector: (state: AdminModeState) => U) => {
  return useStore(AdminStore, selector);
};
