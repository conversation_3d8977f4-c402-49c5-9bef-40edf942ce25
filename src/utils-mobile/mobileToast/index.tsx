import { Toast } from 'antd-mobile';
import { ExclamationCircleFill } from 'antd-mobile-icons';

import styles from './index.less';

export const showErrorToast = (text: string) => {
  return Toast.show({
    content: (
      <span>
        <ExclamationCircleFill color="var(--theme-text-color-alert)" fontSize={14} /> {text}
      </span>
    ),
    maskClassName: styles.mobileErrorToastMask,
    duration: 2000,
    position: 'top',
  });
};
