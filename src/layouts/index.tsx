import { ConfigProvider, message, theme } from 'antd';
import type { ReactNode } from 'react';
import React, { Suspense, useEffect } from 'react';
import { Outlet, useLocation } from 'umi';

import UploadBoard from '@/components/UploadBoard';
import { useAnonymousEffects } from '@/hooks/Authorization';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm } from '@/modules/Locale';
import { useThemeStore } from '@/store/Theme';
import { themeConfig } from '@/themes/config';

import { ContainerLayout } from './ContainerLayout';
import DesktopLayout from './DesktopLayout';
import EditorLayout from './EditorLayout';
import styles from './index.less';
import LoginLayout from './LoginLayout';
import MobileLayout from './MobileLayout';
import SettingLayout from './SettingLayout';

const LoginPath = ['/login', '/__DRIVE__/config'];
const EditorPath = ['/docs', '/docx', '/sheets', '/presentation', '/ppt', '/tables', '/forms'];
const ProfilePath = ['/profile'];
const DesktopPath = ['/desktop', '/space', '/recent', '/share', '/trash', '/favorites', '/folder'];
const ManagementPath = [
  '/enterprise/trash',
  '/enterprise/efficiency',
  '/enterprise/members',
  '/enterprise/audit',
  '/enterprise/packages',
  '/enterprise/whitelist',
  '/enterprise/settings',
  '/enterprise/template',
];
const MobilePath = ['/mobile'];

const Layout: React.FC = () => {
  const currentTheme = useThemeStore((state) => state.theme);
  let isEditorPage = false;
  // 初始化当前用户的数据
  useAnonymousEffects();
  // 获取当前地址
  const location = useLocation();
  const networkError = fm('UseFileUpload.networkError');
  //判断网络情况
  const isOnline = useNetworkStatus();
  useEffect(() => {
    if (!isOnline) {
      // 编辑器页面已有其他网络提示，这里不再提示
      if (!EditorPath.some((path) => location.pathname.startsWith(path))) {
        message.error(networkError);
      }
    }
  }, [isOnline]);
  let content: ReactNode;
  if (LoginPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <LoginLayout>
        <Outlet />
      </LoginLayout>
    );
  } else if (EditorPath.some((path) => location.pathname.startsWith(path))) {
    isEditorPage = true;
    document.body.classList.remove('drive-dark'); //编辑器页面的sdk暂不支持暗色模式
    content = (
      <EditorLayout>
        <Outlet />
      </EditorLayout>
    );
  } else if (ProfilePath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <SettingLayout>
        <Outlet />
      </SettingLayout>
    );
  } else if (DesktopPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <DesktopLayout>
        <Outlet />
      </DesktopLayout>
    );
  } else if (ManagementPath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <ContainerLayout>
        <Outlet />
      </ContainerLayout>
    );
  } else if (MobilePath.some((path) => location.pathname.startsWith(path))) {
    content = (
      <MobileLayout>
        <Outlet />
      </MobileLayout>
    );
  } else {
    content = <Outlet />;
  }

  return (
    <Suspense fallback={'loading...'}>
      <ConfigProvider
        theme={{
          ...themeConfig,
          algorithm: currentTheme.isDark && !isEditorPage ? theme.darkAlgorithm : theme.defaultAlgorithm,
        }}
        wave={{ disabled: true }}
      >
        <div className={styles.layoutContainer}>
          {content}
          <UploadBoard />
        </div>
      </ConfigProvider>
    </Suspense>
  );
};

export default Layout;
