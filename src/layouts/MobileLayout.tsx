import Layout from 'antd/es/layout';
import { Content } from 'antd/es/layout/layout';
import type { PropsWithChildren } from 'react';

import { useLoginGuard } from '@/hooks/Authorization';
import { useMeStore } from '@/store/Me';

export default function MobileLayout(props: PropsWithChildren) {
  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;
  useLoginGuard();

  return notInitial ? null : (
    <Layout>
      <Content>{props.children}</Content>
    </Layout>
  );
}
