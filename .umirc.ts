import { defineConfig } from 'umi';

const isMobilePlatform = process.env.PLATFORM === 'mobile';

const HOST = 'drv-dev.shimorelease.com';
const ORIGIN = `https://${HOST}`;
const WS_ORIGIN = `https://ws.${HOST}`;
const OBS_UPLOAD_ORIGIN = 'smdev-svc-drive.obs.cn-north-4.myhuaweicloud.com';

const EditorPaths = ['/doc', '/spreadsheet', '/slides'];

const ProxyRules = [
  '/api/v1',
  '/lizard-api',
  '/lizard-one',
  '/perception',
  '/panda-api',
  '/static',
  '/agreements',
  '/create',
  '/logout',
  '/loginByPassword',
  '/pricing',
  '/sdk',
  '/edit',
  '/comment-api',
  '/aioa',
  '/uploader/f',
  '/uploader',
  '/minio',
  '/sdk',
  /^\/\w{16}\/collaborators/, // 协作者
  ...EditorPaths,
  `/${OBS_UPLOAD_ORIGIN}`,
];

function getProxyContext(path: string) {
  for (const rule of ProxyRules) {
    if (typeof rule === 'string' && new RegExp(`^${rule}(?!\\w)`).test(path)) {
      return true;
    } else if (rule instanceof RegExp && rule.test(path)) {
      return true;
    }
  }

  return false;
}

type UmiConfigRoutes = Parameters<typeof defineConfig>[0]['routes'];
const CommonRoutes = [
  { path: '__DRIVE__/config', component: 'common/DriveConfig' },
  { path: '/docs/:guid/*', component: 'common/Editor' },
  { path: '/login', component: 'common/Login' },
] satisfies UmiConfigRoutes;

const PcRoutes = [
  ...CommonRoutes,
  { path: '/', redirect: '/recent' }, // DRIVE-476
  { path: '/desktop', component: 'pc/Desktop' },
  { path: '/docs/:guid/*', component: 'common/Editor' },
  { path: '/docx/:guid/*', component: 'common/Editor' },
  { path: '/sheets/:guid/*', component: 'common/Editor' },
  { path: '/presentation/:guid/*', component: 'common/Editor' },
  { path: '/tables/:guid/*', component: 'common/Editor' },
  { path: '/forms/:guid/*', component: 'common/Editor' },
  { path: '/forms/:guid/fill-form/*', component: 'common/Editor' },
  { path: '/folder/:guid', component: 'pc/Desktop' },
  { path: '/space', component: 'pc/Space' },
  { path: '/space/:guid', component: 'pc/Desktop' },
  { path: '/recent', component: 'pc/Recent' },
  { path: '/share', component: 'pc/Share' },
  { path: '/favorites', component: 'pc/Favorites' },
  { path: '/trash', component: 'pc/Trash' },
  { path: '/enterprise/members', component: 'pc/Members' },
  { path: '/profile/accountinfo', component: 'pc/Profile/AccountInfo' },
  { path: '/profile/preference', component: 'pc/Profile/Preference' },
  { path: '/enterprise/trash', component: 'pc/Enterprise/Trash' },
  { path: '/enterprise/template', component: 'pc/Enterprise/Template' },
  { path: '/forbidden', component: 'pc/Forbidden' },
  { path: '/files/:guid', component: 'pc/Preview' }, // 预览文件
  { path: '/enterprise/settings', component: 'pc/Enterprise/Settings' }, // 企业设置
  { path: '/enterprise/audit', component: 'pc/Enterprise/Audit' },
] satisfies UmiConfigRoutes;

const MobileRoutes = [
  ...CommonRoutes,
  { path: '/error', component: 'mobile/Error' }, // todo 方便调试，后面删除
  { path: '/', redirect: '/mobile' },
  { path: '/mobile', component: 'mobile/Desktop' },
  { path: '/mobile/folder/:guid', component: 'mobile/Desktop' },
  { path: '/notification', component: 'mobile/Notification', layout: false },
] satisfies UmiConfigRoutes;

export default defineConfig({
  mako: {},
  base: '/',
  publicPath: process.env.WEBPACK_PUBLIC_PATH,
  jsMinifier: 'esbuild', // 关键：指定使用 esbuild
  jsMinifierOptions: {
    // esbuild 专属配置
    minify: true,
    target: 'es2015',
    format: 'iife', // 强制 IIFE 格式
    minifyIdentifiers: true,
    minifySyntax: true,
    minifyWhitespace: true,
  },
  routes: isMobilePlatform ? MobileRoutes : PcRoutes,
  // 兼容性要求参考：https://shimo.im/docs/WkMZMxtQ2LQVUgF2
  targets: {
    chrome: 78,
    edge: 84,
    safari: '13.6',
  },
  hash: true,
  codeSplitting: {
    jsStrategy: 'depPerChunk',
  },
  npmClient: 'yarn',
  proxy: {
    context: getProxyContext, // 不支持的界面需要直接跳转到 lizard 项目中
    target: ORIGIN,
    secure: false,
    changeOrigin: true,
    cookieDomainRewrite: {
      [`.${HOST}`]: '',
      [`${HOST}`]: '',
    },
    onProxyRes: (proxyRes: any, req: any, res: any) => {
      //  后端 register 和 login 接口 set cookie 时设置了 Secure，用本地 ip 访问 dev server 时带 Secure 标识会导致 set cookie 失败
      if (
        ['/api/v1/auth/password/register', '/api/v1/auth/password/login', '/api/v1/auth/password/login'].includes(
          req.originalUrl,
        )
      ) {
        const setCookieHeader = proxyRes.headers['set-cookie'];
        if (typeof setCookieHeader === 'string') {
          proxyRes.headers['set-cookie'] = setCookieHeader.replace('Secure', '').replace('SameSite=None', '');
        } else if (Array.isArray(setCookieHeader)) {
          for (let index = 0; index < setCookieHeader.length; index++) {
            const cookie = setCookieHeader[index];
            setCookieHeader[index] = cookie.replace('Secure', '').replace('SameSite=None', '');
          }
        }
      }
    },
    headers: {
      Referer: ORIGIN,
      Origin: ORIGIN,
      'X-Request-From': 'Lizard-View-Proxy',
    },
  },
  plugins: [
    '@umijs/plugins/dist/locale',
    '@umijs/plugins/dist/styled-components',
    './scripts/build-meta-plugin.ts',
    './scripts/externals-plugin.ts',
    './scripts/prefetch-plugin.ts',
    './scripts/injects-plugin.ts',
  ],
  locale: {
    default: 'zh-CN',
    antd: true, // 如果使用 antd
  },
  copy: [
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
    'externals/<EMAIL>',
  ],
  externals: {
    react: 'window.React',
    'react-dom': 'window.ReactDOM',
    lodash: 'window._',
    dayjs: 'window.dayjs',
  },
  styledComponents: {
    babelPlugin: {
      // 生成有语义的类名便于调试
      displayName: process.env.NODE_ENV === 'development',
      // 防止 CSS 类名冲突
      namespace: 'drive',
      // 生成更小的 CSS 文件
      minify: true,
    },
  },
});
