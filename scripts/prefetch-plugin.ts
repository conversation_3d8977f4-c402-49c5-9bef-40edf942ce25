/**
 * 这个 umi 插件用于在 html head 中注入一段预取数据的逻辑
 * 这段逻辑会在 externals 后面注入到页面上，会在页面的入口 js 开始下载之前就被执行
 * 预取的数据会放在 window.__DRIVE_PREFETCH__ 中，业务逻辑中的 ajax 请求会先检查 window.__DRIVE_PREFETCH__ 中是否有数据
 * 如果有数据，就直接使用数据，否则才会发起 ajax 请求
 * window.__DRIVE_PREFETCH__ 中的数据只会被使用一次，用后会被清除，以免需要刷新数据时无法获取最新的数据状态
 */
import type { IApi } from 'umi';

/**
 * 预取数据的逻辑
 * 这个函数会被以字符串的形式注入到页面中
 */
function prefetchData() {
  window.__DRIVE_PREFETCH__ = {};
  // 根据不同的路径，预取数据
  const requestUrls = ['/api/v1/users/me'];
  const { pathname } = location;
  switch (pathname) {
    case '/desktop':
      requestUrls.push('/api/v1/files');
      break;
    case '/space':
      requestUrls.push('/api/v1/spaces');
      break;
    case '/trash':
      requestUrls.push('/api/v1/trashes');
      break;
    default:
      break;
  }

  requestUrls.forEach((path) => {
    window.__DRIVE_PREFETCH__[path] = fetch(path).then((res) => {
      return res.ok ? res.json() : Promise.reject(res);
    });
  });
}

export default (api: IApi) => {
  api.describe({
    key: 'inject data prefetch logic',
  });
  api.modifyHTML(($) => {
    $('head').append(`
      <script>
        (() =>{
          ${prefetchData.toString()}
          ;prefetchData();
         })()
      </script>
      `);
    return $;
  });
};
