/**
 * 这个 umi 插件专门处理项目中的各种 external 依赖
 * 具体来说，就是要在 dev 和 production 模式下都正确地把 externals 目录下的文件注入到页面中
 */
import type { IApi } from 'umi';

export default (api: IApi) => {
  api.describe({
    key: 'inject externals to html in dev and prod mode',
  });
  api.modifyHTML(($) => {
    // 给 umijs 的入口 js 文件添加 defer 属性
    $('script[src*="umi."][src$=".js"]').attr('defer', 'defer');
    // 在 head 中添加 react 和 react-dom 的 cdn 链接
    $('head').append(`
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      <script src="${process.env.EXTERNAL_BASE_URL}<EMAIL>" defer></script>
      `);
    return $;
  });
};
